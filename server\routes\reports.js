const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const { query } = require('../database/init');
const { requireAdmin } = require('../middleware/auth');
const { generateComplianceHTMLReport } = require('../modules/reports/templates');
const {
  getComplianceReportData,
  getComplianceSummaryData,
  getCompletionStats,
  getUserActivityData,
  getTotalCertificateCount
} = require('../modules/reports/queries');

const router = express.Router();

// Generate compliance report
router.get('/compliance', requireAdmin, async (req, res) => {
  try {
    const { moduleId, startDate, endDate, format = 'html' } = req.query;

    // Build query conditions - only include active modules
    let whereConditions = ['tm.is_active = true'];
    let queryParams = [];
    let paramIndex = 1;

    if (moduleId) {
      whereConditions.push(`tm.id = $${paramIndex++}`);
      queryParams.push(moduleId);
    }

    if (startDate) {
      whereConditions.push(`up.created_at >= $${paramIndex++}`);
      queryParams.push(startDate);
    }

    if (endDate) {
      whereConditions.push(`up.created_at <= $${paramIndex++}`);
      queryParams.push(endDate);
    }

    // Get comprehensive training data
    const reportData = await getComplianceReportData(whereConditions, queryParams);

    // Get summary statistics
    const summaryData = await getComplianceSummaryData(whereConditions, queryParams, moduleId);
    const summary = summaryData.rows[0];

    if (format === 'json') {
      return res.json({
        summary,
        reportData: reportData.rows,
        generatedAt: new Date().toISOString(),
        filters: { moduleId, startDate, endDate }
      });
    }

    // Generate HTML report
    const htmlReport = generateComplianceHTMLReport(summary, reportData.rows, { moduleId, startDate, endDate });

    res.setHeader('Content-Type', 'text/html');
    res.setHeader('Content-Disposition', `attachment; filename="Training_Compliance_Report_${new Date().toISOString().split('T')[0]}.html"`);
    res.send(htmlReport);

  } catch (error) {
    console.error('Compliance report error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get training completion statistics
router.get('/completion-stats', requireAdmin, async (req, res) => {
  try {
    const { moduleId } = req.query;

    let moduleFilter = 'WHERE tm.is_active = true';
    let queryParams = [];

    if (moduleId) {
      moduleFilter = 'WHERE tm.is_active = true AND tm.id = $1';
      queryParams.push(moduleId);
    }

    const stats = await getCompletionStats(moduleFilter, queryParams);

    // Get total certificate count across all modules for the dashboard
    const totalCertificates = await getTotalCertificateCount();

    res.json({
      stats: stats.rows,
      totalCertificates: totalCertificates
    });
  } catch (error) {
    console.error('Completion stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user activity report
router.get('/user-activity', requireAdmin, async (req, res) => {
  try {
    const { startDate, endDate, userEmail } = req.query;

    let whereConditions = ['tm.is_active = true', 'q.is_active = true'];
    let queryParams = [];
    let paramIndex = 1;

    if (startDate) {
      whereConditions.push(`qa.completed_at >= $${paramIndex++}`);
      queryParams.push(startDate);
    }

    if (endDate) {
      whereConditions.push(`qa.completed_at <= $${paramIndex++}`);
      queryParams.push(endDate);
    }

    if (userEmail) {
      whereConditions.push(`u.email = $${paramIndex++}`);
      queryParams.push(userEmail.toLowerCase());
    }

    const activity = await getUserActivityData(whereConditions, queryParams);

    res.json({ activity: activity.rows });
  } catch (error) {
    console.error('User activity error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});



// Get question-level statistics with filters
router.get('/question-statistics', requireAdmin, async (req, res) => {
  try {
    const { moduleId, quizId, startDate, endDate, questionType } = req.query;

    // Build query conditions
    let whereConditions = ['tm.is_active = true'];
    let queryParams = [];
    let paramIndex = 1;

    if (moduleId) {
      whereConditions.push(`tm.id = $${paramIndex++}`);
      queryParams.push(moduleId);
    }

    if (quizId) {
      whereConditions.push(`q.id = $${paramIndex++}`);
      queryParams.push(quizId);
    }

    if (questionType) {
      whereConditions.push(`qq.question_type = $${paramIndex++}`);
      queryParams.push(questionType);
    }

    if (startDate) {
      whereConditions.push(`qa.completed_at >= $${paramIndex++}`);
      queryParams.push(startDate);
    }

    if (endDate) {
      whereConditions.push(`qa.completed_at <= $${paramIndex++}`);
      queryParams.push(endDate);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get detailed question statistics
    const questionStats = await query(`
      SELECT
        qq.id as question_id,
        qq.question_text,
        qq.question_type,
        qq.question_order,
        qq.points,
        q.id as quiz_id,
        q.title as quiz_title,
        tm.id as module_id,
        tm.title as module_title,
        tv.language_code,
        COUNT(qaa.id) as total_attempts,
        COUNT(CASE WHEN qaa.is_correct = true THEN 1 END) as correct_answers,
        COUNT(CASE WHEN qaa.is_correct = false THEN 1 END) as incorrect_answers,
        ROUND(
          (COUNT(CASE WHEN qaa.is_correct = true THEN 1 END)::DECIMAL /
           NULLIF(COUNT(qaa.id), 0)) * 100, 2
        ) as correct_percentage,
        AVG(qaa.points_earned) as average_points_earned,
        COUNT(DISTINCT qa.user_email) as unique_users_attempted
      FROM quiz_questions qq
      JOIN quizzes q ON qq.quiz_id = q.id
      JOIN training_videos tv ON q.video_id = tv.id
      JOIN training_modules tm ON tv.module_id = tm.id
      LEFT JOIN quiz_attempt_answers qaa ON qq.id = qaa.question_id
      LEFT JOIN quiz_attempts qa ON qaa.attempt_id = qa.id
      ${whereClause}
      GROUP BY qq.id, qq.question_text, qq.question_type, qq.question_order,
               qq.points, q.id, q.title, tm.id, tm.title, tv.language_code
      ORDER BY tm.title, q.title, qq.question_order
    `, queryParams);

    // Get summary statistics
    const summaryStats = await query(`
      SELECT
        COUNT(DISTINCT qq.id) as total_questions,
        COUNT(DISTINCT q.id) as total_quizzes,
        COUNT(DISTINCT tm.id) as total_modules,
        COUNT(qaa.id) as total_question_attempts,
        COUNT(CASE WHEN qaa.is_correct = true THEN 1 END) as total_correct,
        COUNT(CASE WHEN qaa.is_correct = false THEN 1 END) as total_incorrect,
        ROUND(
          (COUNT(CASE WHEN qaa.is_correct = true THEN 1 END)::DECIMAL /
           NULLIF(COUNT(qaa.id), 0)) * 100, 2
        ) as overall_correct_percentage
      FROM quiz_questions qq
      JOIN quizzes q ON qq.quiz_id = q.id
      JOIN training_videos tv ON q.video_id = tv.id
      JOIN training_modules tm ON tv.module_id = tm.id
      LEFT JOIN quiz_attempt_answers qaa ON qq.id = qaa.question_id
      LEFT JOIN quiz_attempts qa ON qaa.attempt_id = qa.id
      ${whereClause}
    `, queryParams);

    res.json({
      questionStats: questionStats.rows,
      summary: summaryStats.rows[0],
      filters: { moduleId, quizId, startDate, endDate, questionType }
    });
  } catch (error) {
    console.error('Question statistics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get quiz results overview - all quizzes with user attempt summaries
router.get('/quiz-results-overview', requireAdmin, async (req, res) => {
  try {
    const { moduleId, languageCode } = req.query;

    let whereConditions = ['tm.is_active = true', 'q.is_active = true'];
    let queryParams = [];
    let paramIndex = 1;

    if (moduleId) {
      whereConditions.push(`tm.id = $${paramIndex++}`);
      queryParams.push(moduleId);
    }

    if (languageCode) {
      whereConditions.push(`tv.language_code = $${paramIndex++}`);
      queryParams.push(languageCode);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get quiz results
    const quizResults = await query(`
      SELECT
        q.id as quiz_id,
        q.title as quiz_title,
        q.passing_score,
        tm.id as module_id,
        tm.title as module_title,
        tv.language_code,
        COUNT(DISTINCT qa.user_email) as total_users_attempted,
        COUNT(DISTINCT CASE WHEN qa.passed = true THEN qa.user_email END) as users_passed,
        ROUND(AVG(qa.score)::numeric, 1) as average_score,
        COUNT(qa.id) as total_attempts,
        MAX(qa.completed_at) as last_attempt_date
      FROM quizzes q
      JOIN training_videos tv ON q.video_id = tv.id
      JOIN training_modules tm ON tv.module_id = tm.id
      LEFT JOIN quiz_attempts qa ON q.id = qa.quiz_id
      ${whereClause}
      GROUP BY q.id, q.title, q.passing_score, tm.id, tm.title, tv.language_code
      ORDER BY tm.title, tv.language_code, q.title
    `, queryParams);

    // Get summary statistics
    const summaryStats = await query(`
      SELECT
        COUNT(DISTINCT q.id) as total_quizzes,
        COUNT(DISTINCT qa.user_email) as total_unique_users,
        COUNT(qa.id) as total_attempts,
        ROUND(
          CASE
            WHEN COUNT(DISTINCT qa.user_email) > 0 THEN
              (COUNT(DISTINCT CASE WHEN qa.passed = true THEN qa.user_email END)::numeric / COUNT(DISTINCT qa.user_email) * 100)
            ELSE 0
          END, 0
        ) as overall_pass_rate
      FROM quizzes q
      JOIN training_videos tv ON q.video_id = tv.id
      JOIN training_modules tm ON tv.module_id = tm.id
      LEFT JOIN quiz_attempts qa ON q.id = qa.quiz_id
      ${whereClause}
    `, queryParams);

    // Get available languages for filtering
    const availableLanguages = await query(`
      SELECT DISTINCT tv.language_code
      FROM training_videos tv
      JOIN training_modules tm ON tv.module_id = tm.id
      JOIN quizzes q ON tv.id = q.video_id
      ${whereClause}
      ORDER BY tv.language_code
    `, queryParams);

    res.json({
      quizResults: quizResults.rows,
      summary: summaryStats.rows[0],
      availableLanguages: availableLanguages.rows.map(row => row.language_code)
    });
  } catch (error) {
    console.error('Quiz results overview error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get detailed quiz results for a specific quiz
router.get('/quiz-results-detail/:quizId', requireAdmin, async (req, res) => {
  try {
    const { quizId } = req.params;

    // Get quiz information
    const quizInfo = await query(`
      SELECT
        q.id,
        q.title as quiz_title,
        q.description,
        q.passing_score,
        q.time_limit_minutes,
        tm.title as module_title,
        tv.language_code
      FROM quizzes q
      JOIN training_videos tv ON q.video_id = tv.id
      JOIN training_modules tm ON tv.module_id = tm.id
      WHERE q.id = $1 AND q.is_active = true
    `, [quizId]);

    if (quizInfo.rows.length === 0) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    // Get all user attempts for this quiz
    const userAttempts = await query(`
      SELECT
        u.email,
        COALESCE(NULLIF(u.first_name, ''),
          CASE
            WHEN POSITION('.' IN SPLIT_PART(u.email, '@', 1)) > 0 THEN
              INITCAP(SPLIT_PART(SPLIT_PART(u.email, '@', 1), '.', 1))
            ELSE
              INITCAP(SPLIT_PART(u.email, '@', 1))
          END
        ) as first_name,
        COALESCE(NULLIF(u.last_name, ''),
          CASE
            WHEN POSITION('.' IN SPLIT_PART(u.email, '@', 1)) > 0 THEN
              INITCAP(SPLIT_PART(SPLIT_PART(u.email, '@', 1), '.', 2))
            ELSE
              ''
          END
        ) as last_name,
        COUNT(qa.id) as total_attempts,
        MAX(qa.score) as best_score,
        MIN(qa.score) as worst_score,
        ROUND(AVG(qa.score)::numeric, 1) as average_score,
        MAX(CASE WHEN qa.passed = true THEN qa.score END) as passing_score_achieved,
        MAX(CASE WHEN qa.passed = true THEN qa.completed_at END) as first_passed_date,
        MAX(qa.completed_at) as last_attempt_date,
        BOOL_OR(qa.passed) as ever_passed
      FROM users u
      JOIN quiz_attempts qa ON u.email = qa.user_email
      WHERE qa.quiz_id = $1
      GROUP BY u.email, u.first_name, u.last_name
      ORDER BY u.first_name, u.last_name, u.email
    `, [quizId]);

    res.json({
      quiz: quizInfo.rows[0],
      userAttempts: userAttempts.rows
    });
  } catch (error) {
    console.error('Quiz results detail error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get detailed attempt results for a specific user and quiz
router.get('/quiz-results-user/:quizId/:userEmail', requireAdmin, async (req, res) => {
  try {
    const { quizId, userEmail } = req.params;

    // Get quiz and user information
    const quizInfo = await query(`
      SELECT
        q.id,
        q.title as quiz_title,
        q.description,
        q.passing_score,
        tm.title as module_title,
        tv.language_code,
        COALESCE(NULLIF(u.first_name, ''),
          CASE
            WHEN POSITION('.' IN SPLIT_PART(u.email, '@', 1)) > 0 THEN
              INITCAP(SPLIT_PART(SPLIT_PART(u.email, '@', 1), '.', 1))
            ELSE
              INITCAP(SPLIT_PART(u.email, '@', 1))
          END
        ) as first_name,
        COALESCE(NULLIF(u.last_name, ''),
          CASE
            WHEN POSITION('.' IN SPLIT_PART(u.email, '@', 1)) > 0 THEN
              INITCAP(SPLIT_PART(SPLIT_PART(u.email, '@', 1), '.', 2))
            ELSE
              ''
          END
        ) as last_name,
        u.email
      FROM quizzes q
      JOIN training_videos tv ON q.video_id = tv.id
      JOIN training_modules tm ON tv.module_id = tm.id
      CROSS JOIN users u
      WHERE q.id = $1 AND u.email = $2 AND q.is_active = true
    `, [quizId, userEmail]);

    if (quizInfo.rows.length === 0) {
      return res.status(404).json({ error: 'Quiz or user not found' });
    }

    // Get all attempts for this user and quiz
    const attempts = await query(`
      SELECT
        qa.id,
        qa.attempt_number,
        qa.score,
        qa.total_questions,
        qa.passed,
        qa.completed_at,
        qa.time_taken_seconds
      FROM quiz_attempts qa
      WHERE qa.quiz_id = $1 AND qa.user_email = $2
      ORDER BY qa.attempt_number
    `, [quizId, userEmail]);

    // Get detailed answers for each attempt
    const attemptDetails = [];
    for (const attempt of attempts.rows) {
      const answers = await query(`
        SELECT
          qaa.id,
          qq.question_text,
          qq.question_type,
          qq.points as max_points,
          qaa.selected_option_id,
          qaa.selected_option_ids,
          qaa.answer_text,
          qaa.is_correct,
          qaa.points_earned,
          qaa.answered_at,
          -- Get correct answer information
          CASE
            WHEN qq.question_type IN ('multiple_choice', 'true_false') THEN
              (SELECT option_text FROM quiz_question_options WHERE id = qaa.selected_option_id)
            WHEN qq.question_type = 'multi_choice' THEN
              (SELECT string_agg(option_text, ', ' ORDER BY option_order)
               FROM quiz_question_options
               WHERE id = ANY(qaa.selected_option_ids))
            ELSE qaa.answer_text
          END as user_answer,
          -- Get correct answer(s)
          CASE
            WHEN qq.question_type IN ('multiple_choice', 'true_false') THEN
              (SELECT option_text FROM quiz_question_options WHERE question_id = qq.id AND is_correct = true LIMIT 1)
            WHEN qq.question_type = 'multi_choice' THEN
              (SELECT string_agg(option_text, ', ' ORDER BY option_order)
               FROM quiz_question_options
               WHERE question_id = qq.id AND is_correct = true)
            ELSE 'Manual grading required'
          END as correct_answer
        FROM quiz_attempt_answers qaa
        JOIN quiz_questions qq ON qaa.question_id = qq.id
        WHERE qaa.attempt_id = $1
        ORDER BY qq.question_order
      `, [attempt.id]);

      attemptDetails.push({
        ...attempt,
        answers: answers.rows
      });
    }

    res.json({
      quiz: quizInfo.rows[0],
      attempts: attemptDetails
    });
  } catch (error) {
    console.error('Quiz results user detail error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get available quizzes for filtering (based on selected module)
router.get('/available-quizzes', requireAdmin, async (req, res) => {
  try {
    const { moduleId } = req.query;

    let whereCondition = 'WHERE tm.is_active = true';
    let queryParams = [];

    if (moduleId) {
      whereCondition += ' AND tm.id = $1';
      queryParams.push(moduleId);
    }

    const quizzes = await query(`
      SELECT
        q.id,
        q.title,
        tm.id as module_id,
        tm.title as module_title,
        tv.language_code,
        COUNT(qq.id) as question_count
      FROM quizzes q
      JOIN training_videos tv ON q.video_id = tv.id
      JOIN training_modules tm ON tv.module_id = tm.id
      LEFT JOIN quiz_questions qq ON q.id = qq.quiz_id
      ${whereCondition}
      GROUP BY q.id, q.title, tm.id, tm.title, tv.language_code
      ORDER BY tm.title, tv.language_code, q.title
    `, queryParams);

    res.json({ quizzes: quizzes.rows });
  } catch (error) {
    console.error('Available quizzes error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
