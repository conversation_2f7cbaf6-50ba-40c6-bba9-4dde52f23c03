import React, { useState, useEffect } from 'react'
import { Card, Button, Typography, Space, Spin, Alert, message } from 'antd'
import { ArrowLeftOutlined, PlayCircleOutlined, CloseCircleOutlined, TrophyOutlined } from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import { quizAPI, handleAPIError } from '../services/api'
import { useAuth } from '../contexts/AuthContext'

// Import modular components
import { QuestionRenderer, QuizResults, QuizTimer } from '../components/Quiz'

// Import utilities
import { formatTime } from '../utils/videoHelpers'

const { Title, Text, Paragraph } = Typography

// Import utilities that were previously defined here
import { getVideoUrl } from '../utils/videoHelpers'

function Quiz() {
  const { t } = useTranslation()
  const { moduleId, languageCode } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const [answers, setAnswers] = useState({})
  const [timeLeft, setTimeLeft] = useState(null)
  const [quizStarted, setQuizStarted] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [quizResults, setQuizResults] = useState(null)
  const [showTimeUpScreen, setShowTimeUpScreen] = useState(false)

  // Fetch quiz data
  const { data: quizData, isLoading, error } = useQuery({
    queryKey: ['quiz', moduleId, languageCode, user?.email],
    queryFn: () => quizAPI.getQuiz(moduleId, languageCode),
    select: (response) => response.data,
    enabled: !!moduleId && !!languageCode && !!user?.email,
  })

  // Submit quiz mutation
  const submitQuizMutation = useMutation(
    (answers) => quizAPI.submitQuiz(moduleId, languageCode, answers),
    {
      onSuccess: (response) => {
        // Invalidate user-specific queries to ensure fresh data
        queryClient.invalidateQueries(['training-progress', moduleId, user?.email])
        queryClient.invalidateQueries(['user-profile', user?.email])
        queryClient.invalidateQueries(['quiz', moduleId, languageCode, user?.email])

        setQuizResults(response.data.result)
        setShowResults(true)
        if (response.data.result.passed) {
          message.success(t('quiz.congratulationsPassed'))
        } else {
          message.error(t('quiz.quizNotPassedTryAgain'))
        }
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('quiz.failedToSubmitQuiz')}: ${errorInfo.message}`)
      }
    }
  )

  // Timer effect
  useEffect(() => {
    if (quizStarted && timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (quizStarted && timeLeft === 0 && !showTimeUpScreen) {
      // Show time's up screen instead of directly submitting
      setShowTimeUpScreen(true)
    }
  }, [quizStarted, timeLeft, showTimeUpScreen])

  const startQuiz = () => {
    setQuizStarted(true)
    if (quizData.quiz.time_limit_minutes) {
      setTimeLeft(quizData.quiz.time_limit_minutes * 60)
    }
  }

  const handleAnswerChange = (questionId, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }))
  }

  const handleMultiChoiceChange = (questionId, optionId, checked) => {
    setAnswers(prev => {
      const currentAnswers = prev[questionId] || []
      if (checked) {
        // Add option if not already selected
        if (!currentAnswers.includes(optionId)) {
          return {
            ...prev,
            [questionId]: [...currentAnswers, optionId]
          }
        }
      } else {
        // Remove option if currently selected
        return {
          ...prev,
          [questionId]: currentAnswers.filter(id => id !== optionId)
        }
      }
      return prev
    })
  }

  const handleSubmitQuiz = () => {
    const formattedAnswers = Object.entries(answers).map(([questionId, answer]) => {
      const question = quizData.quiz.questions.find(q => q.id === parseInt(questionId))

      if (question.question_type === 'short_answer') {
        return {
          questionId: parseInt(questionId),
          answerText: answer
        }
      } else if (question.question_type === 'multi_choice') {
        return {
          questionId: parseInt(questionId),
          selectedOptionIds: Array.isArray(answer) ? answer : []
        }
      } else {
        return {
          questionId: parseInt(questionId),
          selectedOptionId: answer
        }
      }
    })

    submitQuizMutation.mutate(formattedAnswers)
  }

  const handleTimeUp = () => {
    // Auto-submit with current answers (likely incomplete, resulting in failure)
    const formattedAnswers = Object.entries(answers).map(([questionId, answer]) => {
      const question = quizData.quiz.questions.find(q => q.id === parseInt(questionId))

      if (question.question_type === 'short_answer') {
        return {
          questionId: parseInt(questionId),
          answerText: answer || '' // Empty answer if not provided
        }
      } else if (question.question_type === 'multi_choice') {
        return {
          questionId: parseInt(questionId),
          selectedOptionIds: Array.isArray(answer) ? answer : []
        }
      } else {
        return {
          questionId: parseInt(questionId),
          selectedOptionId: answer || null // No selection if not provided
        }
      }
    })

    // Add any unanswered questions as empty answers
    quizData.quiz.questions.forEach(question => {
      const hasAnswer = formattedAnswers.some(a => a.questionId === question.id)
      if (!hasAnswer) {
        if (question.question_type === 'short_answer') {
          formattedAnswers.push({
            questionId: question.id,
            answerText: ''
          })
        } else if (question.question_type === 'multi_choice') {
          formattedAnswers.push({
            questionId: question.id,
            selectedOptionIds: []
          })
        } else {
          formattedAnswers.push({
            questionId: question.id,
            selectedOptionId: null
          })
        }
      }
    })

    submitQuizMutation.mutate(formattedAnswers)
  }



  const handleViewCertificate = () => {
    navigate(`/training/${moduleId}/certificate/${languageCode}`)
  }

  const handleRetakeQuiz = () => {
    setAnswers({})
    setQuizStarted(false)
    setShowResults(false)
    setQuizResults(null)
    setTimeLeft(null)
    setShowTimeUpScreen(false)
  }

  const handleWatchVideoInNewTab = () => {
    if (quizData && quizData.video) {
      // Navigate to the video player page instead of opening direct video URL
      const videoPlayerUrl = `/training/${moduleId}/video/${languageCode}`
      window.open(videoPlayerUrl, '_blank', 'noopener,noreferrer')
      message.info(t('quiz.videoOpenedInNewTab'))
    } else {
      message.error(t('quiz.videoUrlNotAvailable'))
    }
  }

  if (isLoading) {
    return (
      <div className="loading-spinner">
        <Spin size="large" />
        <span className="loading-text">{t('quiz.loadingQuiz')}</span>
      </div>
    )
  }

  if (error) {
    const errorInfo = handleAPIError(error)
    return (
      <Alert
        message={t('quiz.errorLoadingQuiz')}
        description={errorInfo.message}
        type="error"
        showIcon
        action={
          <Button onClick={() => navigate('/training-modules')}>
            {t('quiz.backToModules')}
          </Button>
        }
      />
    )
  }

  const { quiz, alreadyPassed } = quizData

  // Show time's up screen
  if (showTimeUpScreen) {
    return (
      <div>
        <div style={{ marginBottom: 24 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/training-modules')}
            style={{ marginBottom: 16 }}
          >
            {t('common.back')}
          </Button>
        </div>

        <Card style={{ textAlign: 'center', maxWidth: 600, margin: '0 auto' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <CloseCircleOutlined style={{ fontSize: 64, color: '#ff4d4f' }} />

            <div>
              <Title level={2} style={{ color: '#ff4d4f' }}>
                {t('quiz.timeUp')}
              </Title>
              <Paragraph style={{ fontSize: 16, marginBottom: 24 }}>
                {t('quiz.timeUpDescription')}
              </Paragraph>
            </div>

            <div style={{
              background: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: 6,
              padding: 16,
              marginBottom: 24
            }}>
              <Text style={{ fontSize: 16, color: '#cf1322' }}>
                {t('quiz.timeUpAutoSubmit')}
              </Text>
            </div>

            <Button
              type="primary"
              size="large"
              onClick={handleTimeUp}
              loading={submitQuizMutation.isLoading}
              style={{ minWidth: 120 }}
            >
              {t('quiz.submitQuiz')}
            </Button>
          </Space>
        </Card>
      </div>
    )
  }

  if (showResults) {
    return (
      <QuizResults
        results={quizResults}
        quiz={quiz}
        onRetakeQuiz={handleRetakeQuiz}
        onViewCertificate={handleViewCertificate}
        onBackToModules={() => navigate('/training-modules')}
      />
    )
  }

  if (alreadyPassed) {
    return (
      <div>
        <div style={{ marginBottom: 24 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/training-modules')}
            style={{ marginBottom: 16 }}
          >
            {t('quiz.backToModules')}
          </Button>
        </div>

        <Card style={{ textAlign: 'center', maxWidth: 600, margin: '0 auto' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <TrophyOutlined style={{ fontSize: 64, color: '#52c41a' }} />
            
            <div>
              <Title level={2} style={{ color: '#52c41a' }}>
                {t('quiz.quizAlreadyCompleted')}
              </Title>
              <Paragraph>
                {t('quiz.alreadyPassedDescription')}
              </Paragraph>
            </div>

            <Space>
              <Button
                type="primary"
                size="large"
                icon={<TrophyOutlined />}
                onClick={handleViewCertificate}
              >
                {t('quiz.viewCertificate')}
              </Button>

              <Button
                size="large"
                onClick={() => navigate('/training-modules')}
              >
                {t('quiz.backToModules')}
              </Button>
            </Space>
          </Space>
        </Card>
      </div>
    )
  }

  if (!quizStarted) {
    return (
      <div>
        <div style={{ marginBottom: 24 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/training-modules')}
            style={{ marginBottom: 16 }}
          >
            {t('quiz.backToModules')}
          </Button>
        </div>

        <Card style={{ maxWidth: 600, margin: '0 auto' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2}>{quiz.title}</Title>
              {quiz.description && (
                <Paragraph>{quiz.description}</Paragraph>
              )}
            </div>

            <div style={{ background: '#f8f9fa', padding: 16, borderRadius: 6 }}>
              <Title level={4}>{t('quiz.quizInformation')}</Title>
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                <li>{t('quiz.questions')}: {quiz.questions.length}</li>
                <li>{t('quiz.passingScore')}: {quiz.passing_score}%</li>
                {quiz.time_limit_minutes && (
                  <li>{t('quiz.timeLimit')}: {quiz.time_limit_minutes} {t('quiz.minutes')}</li>
                )}
                <li>{t('quiz.retakeNote')}</li>
              </ul>
            </div>

            <Alert
              message={t('quiz.importantInstructions')}
              description={t('quiz.instructionsDescription')}
              type="info"
              showIcon
            />

            <div style={{ textAlign: 'center' }}>
              <Button
                type="primary"
                size="large"
                onClick={startQuiz}
              >
                {t('quiz.startQuiz')}
              </Button>
            </div>
          </Space>
        </Card>
      </div>
    )
  }

  return (
    <div>
      {/* Quiz Timer */}
      {quiz.time_limit_minutes && (
        <QuizTimer
          timeLimit={quiz.time_limit_minutes}
          onTimeUp={handleTimeUp}
          isActive={quizStarted}
        />
      )}

      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
          <Title level={2}>{quiz.title}</Title>
          <Button
            icon={<PlayCircleOutlined />}
            onClick={handleWatchVideoInNewTab}
            type="default"
          >
            {t('quiz.watchVideoInNewTab')}
          </Button>
        </div>

        <Alert
          message={t('quiz.needToReviewVideo')}
          description={t('quiz.videoReferenceDescription')}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      </div>

      <div className="quiz-container">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {quiz.questions.map((question, index) => (
            <QuestionRenderer
              key={question.id}
              question={question}
              questionIndex={index}
              answer={answers[question.id]}
              onAnswerChange={(questionId, value) => {
                if (question.question_type === 'multi_choice') {
                  // Handle multi-choice differently
                  const currentAnswers = answers[questionId] || []
                  setAnswers(prev => ({
                    ...prev,
                    [questionId]: value
                  }))
                } else {
                  handleAnswerChange(questionId, value)
                }
              }}
            />
          ))}

          <Card style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              size="large"
              onClick={handleSubmitQuiz}
              loading={submitQuizMutation.isLoading}
              disabled={Object.keys(answers).length !== quiz.questions.length}
            >
              {t('quiz.submitQuiz')}
            </Button>
            {Object.keys(answers).length !== quiz.questions.length && (
              <div style={{ marginTop: 8, color: '#8c8c8c' }}>
                {t('quiz.answerAllQuestions')}
              </div>
            )}
          </Card>
        </Space>
      </div>
    </div>
  )
}

export default Quiz
