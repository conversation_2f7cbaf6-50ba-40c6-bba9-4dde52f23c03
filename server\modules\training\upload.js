const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const systemConfigService = require('../../services/systemConfigService');

// Import formatFileSize function from systemConfigService
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Configure multer for video uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/videos');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `video-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: systemConfigService.getMaxFileSizeSync(), // Dynamic file size limit (sync for multer config)
  },
  fileFilter: (req, file, cb) => {
    console.log('=== FILE FILTER DEBUG ===');
    console.log('Original filename:', file.originalname);
    console.log('MIME type:', file.mimetype);
    console.log('File size:', file.size);

    const allowedExtensions = /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i;
    const allowedMimeTypes = /^video\/(mp4|avi|quicktime|x-msvideo|x-ms-wmv|x-flv|webm|x-matroska|mp4v-es|3gpp|3gpp2)$/i;
    const maxFileSize = systemConfigService.getMaxFileSizeSync(); // Dynamic file size limit (sync for multer fileFilter)

    const filename = file.originalname.toLowerCase();
    const mimetype = file.mimetype.toLowerCase();

    // Check file size if available (note: file.size might be undefined during fileFilter)
    if (file.size !== undefined) {
      console.log('File size check:', `${(file.size / 1024 / 1024).toFixed(2)}MB`);
      if (file.size > maxFileSize) {
        console.log('File rejected - too large');
        const maxSizeFormatted = formatFileSize(maxFileSize);
        return cb(new Error(`File too large. Maximum size is ${maxSizeFormatted}. Received: ${(file.size / 1024 / 1024).toFixed(2)}MB`));
      }
    } else {
      console.log('File size not available during filter (will be checked by multer limits)');
    }

    const extensionValid = allowedExtensions.test(filename);
    const mimetypeValid = allowedMimeTypes.test(mimetype);

    // Also accept if MIME type starts with 'video/' (fallback for unknown video types)
    const isVideoMimeType = mimetype.startsWith('video/');

    console.log('Extension valid:', extensionValid);
    console.log('MIME type valid:', mimetypeValid);
    console.log('Is video MIME type:', isVideoMimeType);

    // Accept if extension is valid OR if it's any video MIME type
    if (extensionValid || mimetypeValid || (isVideoMimeType && extensionValid)) {
      console.log('File accepted');
      return cb(null, true);
    } else {
      console.log('File rejected - neither extension nor MIME type is valid');
      cb(new Error(`Only video files are allowed. Received: ${file.originalname} (${file.mimetype})`));
    }
  }
});

/**
 * Handle multer upload errors
 */
async function handleUploadError(err, req, res, next) {
  if (err) {
    console.error('Multer upload error:', err);

    // Handle specific multer errors
    if (err.code === 'LIMIT_FILE_SIZE') {
      const maxSizeFormatted = await systemConfigService.getMaxFileSizeFormatted();
      return res.status(400).json({
        error: 'File too large',
        details: `Maximum file size is ${maxSizeFormatted}`
      });
    } else if (err.message.includes('Only video files are allowed')) {
      return res.status(400).json({
        error: 'Invalid file type',
        details: err.message
      });
    } else {
      return res.status(400).json({
        error: 'Upload failed',
        details: err.message
      });
    }
  }

  // Continue to the main handler
  next();
}

module.exports = {
  upload,
  handleUploadError
};
