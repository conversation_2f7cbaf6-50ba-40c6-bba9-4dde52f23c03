const Joi = require('joi');

// Validation schemas for quiz operations
const createQuizSchema = Joi.object({
  videoId: Joi.number().integer().positive().required(),
  title: Joi.string().min(1).max(255).required(),
  description: Joi.string().max(1000).optional(),
  passingScore: Joi.number().integer().min(1).max(100).default(70),
  timeLimitMinutes: Joi.number().integer().min(1).optional(),
  questions: Joi.array().items(
    Joi.object({
      questionText: Joi.string().min(1).required(),
      questionType: Joi.string().valid('multiple_choice', 'true_false', 'short_answer', 'multi_choice').required(),
      points: Joi.when('questionType', {
        is: 'short_answer',
        then: Joi.number().integer().valid(0).default(0), // Short answer questions must have 0 points
        otherwise: Joi.number().integer().min(1).default(1)
      }),
      questionImage: Joi.string().optional().allow(''),
      options: Joi.when('questionType', {
        is: 'multiple_choice',
        then: Joi.array().items(
          Joi.object({
            optionText: Joi.string().min(1).required(),
            isCorrect: Joi.boolean().required(),
            optionImage: Joi.string().optional().allow('')
          })
        ).min(2).required().custom((value, helpers) => {
          // Ensure exactly one correct answer for multiple_choice
          const correctCount = value.filter(option => option.isCorrect).length;
          if (correctCount !== 1) {
            return helpers.error('any.custom', { message: 'Multiple choice questions must have exactly one correct answer' });
          }
          return value;
        }),
        otherwise: Joi.when('questionType', {
          is: 'multi_choice',
          then: Joi.array().items(
            Joi.object({
              optionText: Joi.string().min(1).required(),
              isCorrect: Joi.boolean().required(),
              optionImage: Joi.string().optional().allow('')
            })
          ).min(2).required().custom((value, helpers) => {
            // Ensure at least one correct answer for multi_choice
            const correctCount = value.filter(option => option.isCorrect).length;
            if (correctCount === 0) {
              return helpers.error('any.custom', { message: 'Multi-choice questions must have at least one correct answer' });
            }
            return value;
          }),
          otherwise: Joi.when('questionType', {
            is: 'true_false',
            then: Joi.array().items(
              Joi.object({
                optionText: Joi.string().valid('True', 'False').required(),
                isCorrect: Joi.boolean().required(),
                optionImage: Joi.string().optional().allow('')
              })
            ).length(2).required().custom((value, helpers) => {
              // Ensure exactly one correct answer for true_false
              const correctCount = value.filter(option => option.isCorrect).length;
              if (correctCount !== 1) {
                return helpers.error('any.custom', { message: 'True/False questions must have exactly one correct answer' });
              }
              return value;
            }),
            otherwise: Joi.array().max(0).optional() // Allow empty array for short_answer
          })
        })
      })
    })
  ).min(1).required()
});

const updateQuizSchema = Joi.object({
  title: Joi.string().min(1).max(255).optional(),
  description: Joi.string().max(1000).optional(),
  passingScore: Joi.number().integer().min(1).max(100).optional(),
  timeLimitMinutes: Joi.number().integer().min(1).optional(),
  questions: Joi.array().items(
    Joi.object({
      questionText: Joi.string().min(1).required(),
      questionType: Joi.string().valid('multiple_choice', 'true_false', 'short_answer', 'multi_choice').required(),
      points: Joi.when('questionType', {
        is: 'short_answer',
        then: Joi.number().integer().valid(0).default(0), // Short answer questions must have 0 points
        otherwise: Joi.number().integer().min(1).default(1)
      }),
      questionImage: Joi.string().optional().allow(''),
      options: Joi.when('questionType', {
        is: 'multiple_choice',
        then: Joi.array().items(
          Joi.object({
            optionText: Joi.string().min(1).required(),
            isCorrect: Joi.boolean().required(),
            optionImage: Joi.string().optional().allow('')
          })
        ).min(2).required().custom((value, helpers) => {
          // Ensure exactly one correct answer for multiple_choice
          const correctCount = value.filter(option => option.isCorrect).length;
          if (correctCount !== 1) {
            return helpers.error('any.custom', { message: 'Multiple choice questions must have exactly one correct answer' });
          }
          return value;
        }),
        otherwise: Joi.when('questionType', {
          is: 'multi_choice',
          then: Joi.array().items(
            Joi.object({
              optionText: Joi.string().min(1).required(),
              isCorrect: Joi.boolean().required(),
              optionImage: Joi.string().optional().allow('')
            })
          ).min(2).required().custom((value, helpers) => {
            // Ensure at least one correct answer for multi_choice
            const correctCount = value.filter(option => option.isCorrect).length;
            if (correctCount === 0) {
              return helpers.error('any.custom', { message: 'Multi-choice questions must have at least one correct answer' });
            }
            return value;
          }),
          otherwise: Joi.when('questionType', {
            is: 'true_false',
            then: Joi.array().items(
              Joi.object({
                optionText: Joi.string().valid('True', 'False').required(),
                isCorrect: Joi.boolean().required(),
                optionImage: Joi.string().optional().allow('')
              })
            ).length(2).required().custom((value, helpers) => {
              // Ensure exactly one correct answer for true_false
              const correctCount = value.filter(option => option.isCorrect).length;
              if (correctCount !== 1) {
                return helpers.error('any.custom', { message: 'True/False questions must have exactly one correct answer' });
              }
              return value;
            }),
            otherwise: Joi.array().max(0).optional() // Allow empty array for short_answer
          })
        })
      })
    })
  ).min(1).optional()
});

const submitQuizSchema = Joi.object({
  answers: Joi.array().items(
    Joi.object({
      questionId: Joi.number().integer().positive().required(),
      selectedOptionId: Joi.number().integer().positive().allow(null).optional(),
      selectedOptionIds: Joi.array().items(Joi.number().integer().positive()).optional(), // For multi_choice questions
      answerText: Joi.string().allow('').optional(),
    })
    // Removed custom validation - allow unanswered questions
  ).min(1).required()
});

module.exports = {
  createQuizSchema,
  updateQuizSchema,
  submitQuizSchema
};
