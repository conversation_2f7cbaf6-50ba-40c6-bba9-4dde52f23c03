/**
 * Video Controls Component
 * Extracted from VideoPlayer.jsx for better modularity
 */

import React from 'react'
import { Button, Space, Tooltip } from 'antd'
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  BackwardOutlined, 
  ForwardOutlined,
  SoundOutlined,
  SoundFilled,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

function VideoControls({
  playing,
  volume,
  muted,
  isFullscreen,
  canSkipForward,
  onPlayPause,
  onSkipBackward,
  onSkipForward,
  onVolumeUp,
  onVolumeDown,
  onToggleMute,
  onToggleFullscreen,
  disabled = false
}) {
  const { t } = useTranslation()

  const handlePlayPause = () => {
    if (!disabled) {
      onPlayPause?.()
    }
  }

  const handleSkipBackward = () => {
    if (!disabled) {
      onSkipBackward?.()
    }
  }

  const handleSkipForward = () => {
    if (!disabled) {
      onSkipForward?.()
    }
  }

  const handleVolumeUp = () => {
    if (!disabled) {
      onVolumeUp?.()
    }
  }

  const handleVolumeDown = () => {
    if (!disabled) {
      onVolumeDown?.()
    }
  }

  const handleToggleMute = () => {
    if (!disabled) {
      onToggleMute?.()
    }
  }

  const handleToggleFullscreen = () => {
    if (!disabled) {
      onToggleFullscreen?.()
    }
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center',
      padding: '16px',
      backgroundColor: '#f5f5f5',
      borderRadius: '8px',
      gap: '8px'
    }}>
      <Space size="small">
        {/* Skip Backward Button */}
        <Tooltip title={t('components.videoPlayer.controls.skipBackward')}>
          <Button
            type="text"
            icon={<BackwardOutlined />}
            onClick={handleSkipBackward}
            disabled={disabled}
            size="large"
            style={{ 
              fontSize: '18px',
              width: '48px',
              height: '48px'
            }}
          />
        </Tooltip>

        {/* Play/Pause Button */}
        <Tooltip title={t('components.videoPlayer.controls.playPause')}>
          <Button
            type="primary"
            icon={playing ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={handlePlayPause}
            disabled={disabled}
            size="large"
            style={{ 
              fontSize: '24px',
              width: '56px',
              height: '56px',
              borderRadius: '50%'
            }}
          />
        </Tooltip>

        {/* Skip Forward Button */}
        <Tooltip 
          title={
            canSkipForward 
              ? t('components.videoPlayer.controls.skipForward')
              : t('videoPlayer.limitedForward')
          }
        >
          <Button
            type="text"
            icon={<ForwardOutlined />}
            onClick={handleSkipForward}
            disabled={disabled || !canSkipForward}
            size="large"
            style={{ 
              fontSize: '18px',
              width: '48px',
              height: '48px',
              opacity: canSkipForward ? 1 : 0.5
            }}
          />
        </Tooltip>
      </Space>

      {/* Volume Controls */}
      <div style={{ 
        marginLeft: '24px',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <Tooltip title={t('components.videoPlayer.controls.volumeDown')}>
          <Button
            type="text"
            onClick={handleVolumeDown}
            disabled={disabled || volume <= 0}
            size="small"
            style={{ fontSize: '14px' }}
          >
            -
          </Button>
        </Tooltip>

        <Tooltip 
          title={
            muted 
              ? t('components.videoPlayer.controls.unmute')
              : t('components.videoPlayer.controls.mute')
          }
        >
          <Button
            type="text"
            icon={muted ? <SoundFilled /> : <SoundOutlined />}
            onClick={handleToggleMute}
            disabled={disabled}
            size="small"
            style={{ 
              fontSize: '16px',
              color: muted ? '#ff4d4f' : '#1890ff'
            }}
          />
        </Tooltip>

        <Tooltip title={t('components.videoPlayer.controls.volumeUp')}>
          <Button
            type="text"
            onClick={handleVolumeUp}
            disabled={disabled || volume >= 1}
            size="small"
            style={{ fontSize: '14px' }}
          >
            +
          </Button>
        </Tooltip>

        {/* Volume Indicator */}
        <div style={{ 
          fontSize: '12px', 
          color: '#666',
          minWidth: '35px',
          textAlign: 'center'
        }}>
          {muted ? '0%' : `${Math.round(volume * 100)}%`}
        </div>
      </div>

      {/* Fullscreen Toggle */}
      <div style={{ marginLeft: '24px' }}>
        <Tooltip 
          title={
            isFullscreen 
              ? t('components.videoPlayer.controls.exitFullscreen')
              : t('components.videoPlayer.controls.fullscreen')
          }
        >
          <Button
            type="text"
            icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={handleToggleFullscreen}
            disabled={disabled}
            size="large"
            style={{ 
              fontSize: '18px',
              width: '48px',
              height: '48px'
            }}
          />
        </Tooltip>
      </div>
    </div>
  )
}

export default VideoControls
