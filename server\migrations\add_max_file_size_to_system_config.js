// Import query function - handle circular dependency
let query;
try {
  query = require('../database/init').query;
} catch (error) {
  // Fallback for circular dependency during initialization
  const { Pool } = require('pg');
  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'training_system',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });
  query = async (text, params) => {
    return await pool.query(text, params);
  };
}

async function addMaxFileSizeToSystemConfig() {
  try {
    console.log('Running migration: Add max_file_size to system_config table...');

    // Check if max_file_size column exists
    const columnCheck = await query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'system_config'
      AND column_name = 'max_file_size'
    `);

    if (columnCheck.rows.length === 0) {
      console.log('Adding max_file_size column to system_config table...');
      
      // Add max_file_size column with default value
      await query(`
        ALTER TABLE system_config 
        ADD COLUMN max_file_size VARCHAR(20) DEFAULT '500MB'
      `);
      
      console.log('✅ Added max_file_size column to system_config table');
      
      // Update existing record if it exists
      const updateResult = await query(`
        UPDATE system_config 
        SET max_file_size = '500MB' 
        WHERE id = 1 AND max_file_size IS NULL
      `);
      
      if (updateResult.rowCount > 0) {
        console.log('✅ Updated existing system config with default max file size');
      }
    } else {
      console.log('✅ max_file_size column already exists in system_config table');
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error running max_file_size migration:', error);
    throw error;
  }
}

module.exports = { addMaxFileSizeToSystemConfig };
