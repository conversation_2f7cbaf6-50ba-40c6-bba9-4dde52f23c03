#!/usr/bin/env node

/**
 * Unified Deployment Script
 * Handles deployment for different environments using the unified .env configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const config = require('../config/environment');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  try {
    log(`🔄 ${description}...`, 'blue');
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed`, 'green');
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

function validateEnvironment() {
  log('🔍 Validating environment configuration...', 'yellow');
  
  const deploymentType = config.get('environment').deploymentType;
  const nodeEnv = config.get('environment').nodeEnv;
  
  log(`Environment: ${nodeEnv}`, 'cyan');
  log(`Deployment Type: ${deploymentType}`, 'cyan');
  
  // Print configuration summary
  config.printSummary();
  
  return { deploymentType, nodeEnv };
}

function buildApplications() {
  log('🔨 Building applications...', 'yellow');
  
  // Build frontend
  execCommand('cd client && npm ci', 'Installing frontend dependencies');
  execCommand('cd client && npm run build', 'Building frontend');
  
  // Install backend dependencies
  execCommand('cd server && npm ci --only=production', 'Installing backend dependencies');
  
  log('✅ Applications built successfully', 'green');
}

function deployLocal() {
  log('🏠 Deploying for local development...', 'yellow');
  
  // Start services
  execCommand('npm run dev', 'Starting development servers');
}

function deployDocker() {
  log('🐳 Deploying with Docker Compose...', 'yellow');
  
  // Check if .env file exists
  if (!fs.existsSync('.env')) {
    log('❌ .env file not found in root directory', 'red');
    log('Please copy .env.example to .env and configure it', 'yellow');
    process.exit(1);
  }
  
  // Build and start containers
  execCommand('docker-compose down', 'Stopping existing containers');
  execCommand('docker-compose up --build -d', 'Building and starting containers');
  
  // Wait for services to be ready
  log('⏳ Waiting for services to be ready...', 'yellow');
  setTimeout(() => {
    execCommand('docker-compose ps', 'Checking container status');
    
    const frontendPort = config.get('client').port;
    const backendPort = config.get('server').port;

    log('🎉 Docker deployment completed!', 'green');
    log(`Frontend: http://localhost${frontendPort === 80 ? '' : ':' + frontendPort}`, 'cyan');
    log(`Backend: http://localhost:${backendPort}`, 'cyan');
  }, 10000);
}

function deployAzure() {
  log('☁️ Deploying to Azure...', 'yellow');
  
  const azureConfig = config.get('azure');
  
  if (!azureConfig.subscriptionId || !azureConfig.resourceGroup) {
    log('❌ Azure configuration incomplete', 'red');
    log('Please configure AZURE_SUBSCRIPTION_ID and AZURE_RESOURCE_GROUP in .env', 'yellow');
    process.exit(1);
  }
  
  // Check if Azure CLI is installed
  try {
    execSync('az --version', { stdio: 'ignore' });
  } catch (error) {
    log('❌ Azure CLI not found', 'red');
    log('Please install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli', 'yellow');
    process.exit(1);
  }
  
  // Build applications first
  buildApplications();
  
  // Deploy using PowerShell script
  const deployScript = path.join(__dirname, '../deploy-azure.ps1');
  if (fs.existsSync(deployScript)) {
    execCommand(
      `powershell -ExecutionPolicy Bypass -File "${deployScript}" -ResourceGroupName "${azureConfig.resourceGroup}" -AppName "training-system" -Location "${azureConfig.location}"`,
      'Running Azure deployment script'
    );
  } else {
    log('❌ Azure deployment script not found', 'red');
    log('Please ensure deploy-azure.ps1 exists in the root directory', 'yellow');
    process.exit(1);
  }
}

function healthCheck() {
  log('🏥 Performing health checks...', 'yellow');
  
  const deploymentType = config.get('environment').deploymentType;
  const frontendPort = config.get('client').port;
  const backendPort = config.get('server').port;
  
  let frontendUrl, backendUrl;
  
  if (deploymentType === 'azure-app-service') {
    const azureConfig = config.get('azure');
    frontendUrl = `https://${azureConfig.frontendAppName || 'training-system-frontend'}.azurewebsites.net`;
    backendUrl = `https://${azureConfig.backendAppName || 'training-system-backend'}.azurewebsites.net`;
  } else {
    frontendUrl = `http://localhost${frontendPort === 80 ? '' : ':' + frontendPort}`;
    backendUrl = `http://localhost:${backendPort}`;
  }
  
  // Simple health check (would need curl or similar tool)
  log(`Frontend URL: ${frontendUrl}`, 'cyan');
  log(`Backend URL: ${backendUrl}`, 'cyan');
  log('Please manually verify the applications are running', 'yellow');
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  log('🚀 Training System Deployment Script', 'bright');
  log('=====================================', 'bright');
  
  const { deploymentType } = validateEnvironment();
  
  switch (command) {
    case 'build':
      buildApplications();
      break;
      
    case 'local':
      deployLocal();
      break;
      
    case 'docker':
      deployDocker();
      break;
      
    case 'azure':
      deployAzure();
      break;
      
    case 'health':
      healthCheck();
      break;
      
    default:
      // Auto-deploy based on deployment type
      switch (deploymentType) {
        case 'local':
          deployLocal();
          break;
        case 'docker':
          deployDocker();
          break;
        case 'azure-app-service':
        case 'azure-container-instances':
          deployAzure();
          break;
        default:
          log('Usage: node scripts/deploy.js [build|local|docker|azure|health]', 'yellow');
          log('Or set DEPLOYMENT_TYPE in .env for automatic deployment', 'yellow');
          process.exit(1);
      }
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  log(`❌ Uncaught exception: ${error.message}`, 'red');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log(`❌ Unhandled rejection at: ${promise}, reason: ${reason}`, 'red');
  process.exit(1);
});

main();
