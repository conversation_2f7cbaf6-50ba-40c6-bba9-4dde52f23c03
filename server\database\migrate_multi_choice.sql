-- Migration script to add multi_choice question type support
-- Run this script on existing databases to add multi_choice support

-- Update quiz_questions constraint to include multi_choice
DO $$
BEGIN
    -- Check if the constraint exists and doesn't include multi_choice
    IF EXISTS (
        SELECT 1 FROM information_schema.check_constraints
        WHERE constraint_name = 'quiz_questions_question_type_check'
        AND check_clause NOT LIKE '%multi_choice%'
    ) THEN
        -- Drop the old constraint
        ALTER TABLE quiz_questions DROP CONSTRAINT quiz_questions_question_type_check;
        -- Add the new constraint with multi_choice
        ALTER TABLE quiz_questions ADD CONSTRAINT quiz_questions_question_type_check
        CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'multi_choice'));

        RAISE NOTICE 'Updated quiz_questions constraint to include multi_choice';
    ELSE
        RAISE NOTICE 'quiz_questions constraint already includes multi_choice or does not exist';
    END IF;

    -- Add selected_option_ids column to quiz_attempt_answers if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'quiz_attempt_answers' AND column_name = 'selected_option_ids') THEN
        ALTER TABLE quiz_attempt_answers ADD COLUMN selected_option_ids INTEGER[];
        RAISE NOTICE 'Added selected_option_ids column to quiz_attempt_answers';
    ELSE
        RAISE NOTICE 'selected_option_ids column already exists in quiz_attempt_answers';
    END IF;

    RAISE NOTICE 'Multi-choice migration completed successfully';
END $$;
