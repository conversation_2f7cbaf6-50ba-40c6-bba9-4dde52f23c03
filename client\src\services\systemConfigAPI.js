import axios from 'axios';

// Create axios instance with default config (same as main api.js)
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 30000,
});

// Function to get token from localStorage
const getToken = () => {
  return localStorage.getItem('authToken');
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

const systemConfigAPI = {
  // Get system configuration
  getSystemConfig: () => {
    return api.get('/system-config');
  },

  // Update system configuration
  updateSystemConfig: (config) => {
    return api.put('/system-config', config);
  },

  // Get password expiration days (convenience method)
  getPasswordExpirationDays: () => {
    return api.get('/system-config/password-expiration-days');
  },

  // Get system timezone (convenience method)
  getSystemTimezone: () => {
    return api.get('/system-config/timezone');
  },

  // Get file upload configuration
  getUploadConfig: () => {
    return api.get('/system-config/upload-config');
  }
};

export default systemConfigAPI;
