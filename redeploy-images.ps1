# =============================================================================
# REDEPLOY UPDATED IMAGES TO EXISTING AZURE DEPLOYMENT
# =============================================================================

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName = "tradelink-training-rg",
    
    [Parameter(Mandatory=$true)]
    [string]$AppName = "tradelink-training",
    
    [Parameter(Mandatory=$true)]
    [string]$ContainerRegistry = "tradelinktraining"
)

$ErrorActionPreference = "Stop"

Write-Host "Redeploying updated images..." -ForegroundColor Green
Write-Host "Configuration:" -ForegroundColor Blue
Write-Host "   Resource Group: $ResourceGroupName" -ForegroundColor White
Write-Host "   App Name: $AppName" -ForegroundColor White
Write-Host "   Container Registry: $ContainerRegistry" -ForegroundColor White

# Get ACR login server
$acrLoginServer = az acr show --name $ContainerRegistry --query loginServer --output tsv
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to get Container Registry details" -ForegroundColor Red
    exit 1
}

Write-Host "Container Registry: $acrLoginServer" -ForegroundColor Green

# =============================================================================
# STEP 1: BUILD AND PUSH UPDATED IMAGES
# =============================================================================
Write-Host "Building and pushing updated container images..." -ForegroundColor Blue

# Login to ACR
Write-Host "Logging into Azure Container Registry..." -ForegroundColor Yellow
az acr login --name $ContainerRegistry
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to login to Container Registry" -ForegroundColor Red
    exit 1
}

# Build and push backend image
Write-Host "Building backend image..." -ForegroundColor Yellow
docker build -f server/Dockerfile.azure -t "$acrLoginServer/$AppName-backend:latest" .
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to build backend image" -ForegroundColor Red
    exit 1
}

Write-Host "Pushing backend image..." -ForegroundColor Yellow
docker push "$acrLoginServer/$AppName-backend:latest"
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to push backend image" -ForegroundColor Red
    exit 1
}

# Build and push frontend image
Write-Host "Building frontend image..." -ForegroundColor Yellow
docker build -f client/Dockerfile.azure -t "$acrLoginServer/$AppName-frontend:latest" .
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to build frontend image" -ForegroundColor Red
    exit 1
}

Write-Host "Pushing frontend image..." -ForegroundColor Yellow
docker push "$acrLoginServer/$AppName-frontend:latest"
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to push frontend image" -ForegroundColor Red
    exit 1
}

Write-Host "Container images built and pushed successfully!" -ForegroundColor Green

# =============================================================================
# STEP 2: RESTART APP SERVICES TO PULL NEW IMAGES
# =============================================================================
Write-Host "Restarting App Services to pull new images..." -ForegroundColor Blue

# Restart frontend app service
Write-Host "Restarting frontend app service..." -ForegroundColor Yellow
az webapp restart --name "$AppName-frontend" --resource-group $ResourceGroupName
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to restart frontend app service" -ForegroundColor Red
    exit 1
}

# Restart backend app service
Write-Host "Restarting backend app service..." -ForegroundColor Yellow
az webapp restart --name "$AppName-backend" --resource-group $ResourceGroupName
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to restart backend app service" -ForegroundColor Red
    exit 1
}

Write-Host "App Services restarted successfully!" -ForegroundColor Green

# =============================================================================
# STEP 3: VERIFY DEPLOYMENT
# =============================================================================
Write-Host "Verifying deployment..." -ForegroundColor Blue

# Wait a moment for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check app service status
$frontendStatus = az webapp show --name "$AppName-frontend" --resource-group $ResourceGroupName --query state --output tsv
$backendStatus = az webapp show --name "$AppName-backend" --resource-group $ResourceGroupName --query state --output tsv

Write-Host ""
Write-Host "Deployment Status:" -ForegroundColor Cyan
Write-Host "   Frontend Status: $frontendStatus" -ForegroundColor White
Write-Host "   Backend Status: $backendStatus" -ForegroundColor White
Write-Host ""
Write-Host "Application URLs:" -ForegroundColor Cyan
Write-Host "   Frontend: https://$AppName-frontend.azurewebsites.net" -ForegroundColor Green
Write-Host "   Backend: https://$AppName-backend.azurewebsites.net" -ForegroundColor Green
Write-Host ""
Write-Host "Redeployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Check the application logs for any remaining errors" -ForegroundColor White
Write-Host "   2. Test the frontend application in your browser" -ForegroundColor White
Write-Host "   3. Verify the backend API is responding correctly" -ForegroundColor White
