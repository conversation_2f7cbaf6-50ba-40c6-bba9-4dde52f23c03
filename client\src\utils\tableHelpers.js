/**
 * Table Configuration and Helper Utilities
 * Common table configurations and utilities for Ant Design tables
 */

import React from 'react'
import { Button, Space, Tag, Tooltip } from 'antd'
import { EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons'

/**
 * Common table pagination configuration
 * @param {Function} t - Translation function
 * @param {number} pageSize - Items per page
 * @returns {Object} - Pagination configuration
 */
export const getTablePagination = (t, pageSize = 10) => ({
  pageSize,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => 
    t('components.tables.pagination.total', { total }),
  pageSizeOptions: ['10', '20', '50', '100'],
  size: 'default'
})

/**
 * Common table loading configuration
 * @param {Function} t - Translation function
 * @returns {Object} - Loading configuration
 */
export const getTableLoadingConfig = (t) => ({
  spinning: true,
  tip: t('components.tables.loading')
})

/**
 * Common empty data configuration
 * @param {Function} t - Translation function
 * @returns {Object} - Empty data configuration
 */
export const getTableEmptyConfig = (t) => ({
  description: t('components.tables.noData')
})

/**
 * Generate common action column
 * @param {Function} t - Translation function
 * @param {Object} actions - Action handlers
 * @returns {Object} - Action column configuration
 */
export const getActionColumn = (t, actions = {}) => ({
  title: t('components.tables.actions'),
  key: 'actions',
  width: 200,
  fixed: 'right',
  render: (_, record) => (
    <Space size="small">
      {actions.view && (
        <Tooltip title={t('common.view')}>
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => actions.view(record)}
          />
        </Tooltip>
      )}
      {actions.edit && (
        <Tooltip title={t('common.edit')}>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => actions.edit(record)}
          />
        </Tooltip>
      )}
      {actions.delete && (
        <Tooltip title={t('common.delete')}>
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => actions.delete(record)}
          />
        </Tooltip>
      )}
      {actions.custom && actions.custom.map((customAction, index) => (
        <Tooltip key={index} title={customAction.title}>
          <Button
            type={customAction.type || 'text'}
            size="small"
            icon={customAction.icon}
            onClick={() => customAction.onClick(record)}
            {...customAction.props}
          >
            {customAction.text}
          </Button>
        </Tooltip>
      ))}
    </Space>
  )
})

/**
 * Generate status column with colored tags
 * @param {Function} t - Translation function
 * @param {string} dataIndex - Data field name
 * @param {Object} statusConfig - Status configuration mapping
 * @returns {Object} - Status column configuration
 */
export const getStatusColumn = (t, dataIndex, statusConfig = {}) => ({
  title: t('common.status'),
  dataIndex,
  key: dataIndex,
  width: 120,
  render: (status) => {
    const config = statusConfig[status] || { color: 'default', text: status }
    return (
      <Tag color={config.color}>
        {config.text || status}
      </Tag>
    )
  }
})

/**
 * Generate date column with formatting
 * @param {Function} t - Translation function
 * @param {string} title - Column title
 * @param {string} dataIndex - Data field name
 * @param {Function} formatDate - Date formatting function
 * @returns {Object} - Date column configuration
 */
export const getDateColumn = (t, title, dataIndex, formatDate) => ({
  title,
  dataIndex,
  key: dataIndex,
  width: 150,
  render: (date) => date ? formatDate(date) : t('common.notAvailable'),
  sorter: (a, b) => new Date(a[dataIndex]) - new Date(b[dataIndex])
})

/**
 * Generate text column with ellipsis
 * @param {Function} t - Translation function
 * @param {string} title - Column title
 * @param {string} dataIndex - Data field name
 * @param {number} maxWidth - Maximum width
 * @returns {Object} - Text column configuration
 */
export const getTextColumn = (t, title, dataIndex, maxWidth = 200) => ({
  title,
  dataIndex,
  key: dataIndex,
  width: maxWidth,
  ellipsis: {
    showTitle: false
  },
  render: (text) => (
    <Tooltip placement="topLeft" title={text}>
      {text}
    </Tooltip>
  )
})

/**
 * Generate number column with formatting
 * @param {Function} t - Translation function
 * @param {string} title - Column title
 * @param {string} dataIndex - Data field name
 * @param {Object} options - Formatting options
 * @returns {Object} - Number column configuration
 */
export const getNumberColumn = (t, title, dataIndex, options = {}) => ({
  title,
  dataIndex,
  key: dataIndex,
  width: options.width || 100,
  align: 'right',
  render: (value) => {
    if (value === null || value === undefined) return '-'
    
    if (options.isPercentage) {
      return `${value}%`
    }
    
    if (options.isCurrency) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: options.currency || 'USD'
      }).format(value)
    }
    
    return new Intl.NumberFormat().format(value)
  },
  sorter: (a, b) => (a[dataIndex] || 0) - (b[dataIndex] || 0)
})

/**
 * Common table row selection configuration
 * @param {Function} onSelectionChange - Selection change handler
 * @param {Array} selectedRowKeys - Currently selected row keys
 * @returns {Object} - Row selection configuration
 */
export const getRowSelection = (onSelectionChange, selectedRowKeys = []) => ({
  selectedRowKeys,
  onChange: onSelectionChange,
  getCheckboxProps: (record) => ({
    disabled: record.disabled || false,
    name: record.name || record.id
  })
})

/**
 * Generate expandable row configuration
 * @param {Function} renderExpandedRow - Function to render expanded content
 * @param {Function} isExpandable - Function to determine if row is expandable
 * @returns {Object} - Expandable configuration
 */
export const getExpandableConfig = (renderExpandedRow, isExpandable) => ({
  expandedRowRender: renderExpandedRow,
  rowExpandable: isExpandable || (() => true),
  expandRowByClick: false
})

/**
 * Common table scroll configuration
 * @param {number} x - Horizontal scroll width
 * @param {number} y - Vertical scroll height
 * @returns {Object} - Scroll configuration
 */
export const getScrollConfig = (x, y) => ({
  x: x || undefined,
  y: y || undefined
})

/**
 * Generate table configuration with common settings
 * @param {Object} options - Configuration options
 * @returns {Object} - Complete table configuration
 */
export const getTableConfig = (options = {}) => {
  const {
    t,
    columns = [],
    dataSource = [],
    loading = false,
    rowKey = 'id',
    pageSize = 10,
    showPagination = true,
    showSelection = false,
    onSelectionChange,
    selectedRowKeys = [],
    expandable,
    scroll,
    size = 'middle'
  } = options

  const config = {
    columns,
    dataSource,
    rowKey,
    loading: loading ? getTableLoadingConfig(t) : false,
    locale: getTableEmptyConfig(t),
    size
  }

  if (showPagination) {
    config.pagination = getTablePagination(t, pageSize)
  } else {
    config.pagination = false
  }

  if (showSelection) {
    config.rowSelection = getRowSelection(onSelectionChange, selectedRowKeys)
  }

  if (expandable) {
    config.expandable = expandable
  }

  if (scroll) {
    config.scroll = scroll
  }

  return config
}

/**
 * Common status configurations for different entities
 */
export const statusConfigs = {
  user: {
    active: { color: 'green', text: 'Active' },
    inactive: { color: 'red', text: 'Inactive' },
    pending: { color: 'orange', text: 'Pending' }
  },
  training: {
    completed: { color: 'green', text: 'Completed' },
    in_progress: { color: 'blue', text: 'In Progress' },
    not_started: { color: 'default', text: 'Not Started' }
  },
  quiz: {
    passed: { color: 'green', text: 'Passed' },
    failed: { color: 'red', text: 'Failed' },
    pending: { color: 'orange', text: 'Pending' }
  }
}
