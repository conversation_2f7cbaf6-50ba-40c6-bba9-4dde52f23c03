const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');

// Load unified environment configuration
const config = require('../../config/environment');

// Database connection configuration from unified config
const dbConfig = config.get('database');

// Create database connection pool
const pool = new Pool(dbConfig);

// Test database connection
async function testConnection() {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    console.log('Database connected successfully at:', result.rows[0].now);
    client.release();
    return true;
  } catch (error) {
    console.error('Database connection failed:', error.message);
    return false;
  }
}

// Initialize database schema
async function initializeDatabase() {
  try {
    console.log('Initializing database...');

    // Test connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }

    // Read and execute initialization SQL
    const sqlPath = path.join(__dirname, 'init.sql');
    const sqlContent = await fs.readFile(sqlPath, 'utf8');

    const client = await pool.connect();
    try {
      await client.query(sqlContent);
      console.log('Database schema initialized successfully');
    } catch (error) {
      // Log the error but don't fail if it's just duplicate objects or column issues
      if (error.code === '42710' || error.code === '42P07' || error.code === '42P06' || error.code === '42703') {
        console.log('Database schema already exists or column migration needed, continuing...');
      } else {
        throw error;
      }
    } finally {
      client.release();
    }

    return true;
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
}

// Execute a query with error handling
async function query(text, params) {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('Executed query', { text: text.substring(0, 100), duration, rows: result.rowCount });
    return result;
  } catch (error) {
    console.error('Query error:', { text: text.substring(0, 100), error: error.message });
    throw error;
  }
}

// Get a client from the pool for transactions
async function getClient() {
  return await pool.connect();
}

// Close the database pool
async function closePool() {
  await pool.end();
  console.log('Database pool closed');
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('Received SIGINT, closing database pool...');
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, closing database pool...');
  await closePool();
  process.exit(0);
});

module.exports = {
  pool,
  query,
  getClient,
  initializeDatabase,
  testConnection,
  closePool,
};
