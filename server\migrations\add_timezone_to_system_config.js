// Import query function - handle circular dependency
let query;
try {
  query = require('../database/init').query;
} catch (error) {
  // Fallback for circular dependency during initialization
  const { Pool } = require('pg');
  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'training_system',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });
  query = async (text, params) => {
    return await pool.query(text, params);
  };
}

async function addTimezoneToSystemConfig() {
  try {
    console.log('Running migration: Add timezone to system_config table...');

    // Check if timezone column exists
    const columnCheck = await query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'system_config'
      AND column_name = 'timezone'
    `);

    if (columnCheck.rows.length === 0) {
      console.log('Adding timezone column to system_config table...');
      
      // Add timezone column with default value
      await query(`
        ALTER TABLE system_config 
        ADD COLUMN timezone VARCHAR(50) DEFAULT 'Asia/Shanghai'
      `);
      
      console.log('✅ Added timezone column to system_config table');
      
      // Update existing record if it exists
      const updateResult = await query(`
        UPDATE system_config 
        SET timezone = 'Asia/Shanghai' 
        WHERE id = 1 AND timezone IS NULL
      `);
      
      if (updateResult.rowCount > 0) {
        console.log('✅ Updated existing system config with default timezone');
      }
    } else {
      console.log('✅ Timezone column already exists in system_config table');
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error running timezone migration:', error);
    throw error;
  }
}

module.exports = { addTimezoneToSystemConfig };
