import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import { useTranslation } from 'react-i18next'
import AppHeader from './components/Layout/AppHeader'
import AppSidebar from './components/Layout/AppSidebar'
import GlobalTutorial from './components/GlobalTutorial'
import LoadingImage from './components/LoadingImage'
import ErrorBoundary from './components/ErrorBoundary'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import TrainingModules from './pages/TrainingModules'
import VideoPlayer from './pages/VideoPlayer'
import Quiz from './pages/Quiz'
import Certificate from './pages/Certificate'
import AdminDashboard from './pages/Admin/AdminDashboard'
import UserManagement from './pages/Admin/UserManagement'
import ModuleManagement from './pages/Admin/ModuleManagement'

import Reports from './pages/Admin/Reports'
import QuizResults from './pages/Admin/QuizResults'
import EmailConfiguration from './pages/Admin/EmailConfiguration'
import SystemConfiguration from './pages/Admin/SystemConfiguration'
import Profile from './pages/Profile'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { TutorialProvider } from './contexts/TutorialContext'

const { Content } = Layout

// Protected Route Component
function ProtectedRoute({ children, adminOnly = false }) {
  const { user, loading } = useAuth()
  const { t } = useTranslation()

  if (loading) {
    return <LoadingImage text={t('common.loading')} />
  }

  if (!user) {
    return <Navigate to="/login" replace />
  }

  if (adminOnly && !user.isAdmin) {
    return <Navigate to="/dashboard" replace />
  }

  return children
}

// Route configuration
const routes = [
  {
    path: '/dashboard',
    element: <Dashboard />,
    protected: true,
  },
  {
    path: '/training-modules',
    element: <TrainingModules />,
    protected: true,
  },
  {
    path: '/training/:moduleId/video/:languageCode',
    element: <VideoPlayer />,
    protected: true,
  },
  {
    path: '/training/:moduleId/quiz/:languageCode',
    element: <Quiz />,
    protected: true,
  },
  {
    path: '/training/:moduleId/certificate/:languageCode',
    element: <Certificate />,
    protected: true,
  },
  {
    path: '/profile',
    element: <Profile />,
    protected: true,
  },
  {
    path: '/admin',
    element: <AdminDashboard />,
    protected: true,
    adminOnly: true,
  },
  {
    path: '/admin/users',
    element: <UserManagement />,
    protected: true,
    adminOnly: true,
  },
  {
    path: '/admin/modules',
    element: <ModuleManagement />,
    protected: true,
    adminOnly: true,
  },
  {
    path: '/admin/reports',
    element: <Reports />,
    protected: true,
    adminOnly: true,
  },
  {
    path: '/admin/quiz-results',
    element: <QuizResults />,
    protected: true,
    adminOnly: true,
  },
  {
    path: '/admin/email-config',
    element: <EmailConfiguration />,
    protected: true,
    adminOnly: true,
  },
  {
    path: '/admin/system-config',
    element: <SystemConfiguration />,
    protected: true,
    adminOnly: true,
  },
]

// Main App Layout
function AppLayout() {
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false)

  const handleSidebarCollapse = (collapsed) => {
    setSidebarCollapsed(collapsed)
  }

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <TutorialProvider>
      <Layout className="app-layout">
        <AppHeader onToggleSidebar={handleToggleSidebar} />
        <Layout>
          <AppSidebar onCollapse={handleSidebarCollapse} collapsed={sidebarCollapsed} />
          <Content className={`app-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
            <Routes>
              {/* Dynamic route generation */}
              {routes.map(({ path, element, protected: isProtected, adminOnly }) => (
                <Route
                  key={path}
                  path={path}
                  element={
                    isProtected ? (
                      <ProtectedRoute adminOnly={adminOnly}>
                        {element}
                      </ProtectedRoute>
                    ) : (
                      element
                    )
                  }
                />
              ))}

              {/* Redirect routes */}
              <Route path="/training" element={<Navigate to="/training-modules" replace />} />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Content>
        </Layout>
        {/* Global Tutorial Component */}
        <GlobalTutorial />
      </Layout>
    </TutorialProvider>
  )
}

// Main App Component
function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ErrorBoundary>
  )
}

// App Content with Auth Context
function AppContent() {
  const { user, loading } = useAuth()
  const { t } = useTranslation()

  if (loading) {
    return <LoadingImage text={t('common.loading')} />
  }

  return (
    <Routes>
      <Route 
        path="/login" 
        element={user ? <Navigate to="/dashboard" replace /> : <Login />} 
      />
      <Route 
        path="/*" 
        element={user ? <AppLayout /> : <Navigate to="/login" replace />} 
      />
    </Routes>
  )
}

export default App
