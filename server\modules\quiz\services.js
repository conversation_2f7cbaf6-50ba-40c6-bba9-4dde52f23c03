const { v4: uuidv4 } = require('uuid');
const { query, getClient } = require('../../database/init');
const EmailTemplateService = require('../../services/email/EmailTemplateService');

/**
 * Calculate quiz score and process answers
 */
async function calculateQuizScore(client, quiz, answers, questions) {
  let totalPoints = 0;
  let earnedPoints = 0;
  const detailedAnswers = [];

  // Create a map of provided answers for quick lookup
  const answerMap = new Map(answers.map(a => [a.questionId, a]));

  // Process ALL questions in the quiz
  for (const question of questions) {
    totalPoints += question.points;

    const answer = answerMap.get(question.id);
    let isCorrect = false;
    let pointsEarned = 0;

    // Only process if an answer was provided
    if (answer) {
      if (question.question_type === 'short_answer') {
        // Short answer questions are not auto-graded
        isCorrect = false;
        pointsEarned = 0;
      } else if (question.question_type === 'multi_choice') {
        // Multi-choice questions: all correct options must be selected, no incorrect ones
        const correctOptions = question.options.filter(opt => opt.isCorrect);
        const selectedIds = answer.selectedOptionIds || [];

        // Check if all correct options are selected and no incorrect ones
        const correctIds = correctOptions.map(opt => opt.id);
        const hasAllCorrect = correctIds.every(id => selectedIds.includes(id));
        const hasOnlyCorrect = selectedIds.every(id => correctIds.includes(id));

        if (hasAllCorrect && hasOnlyCorrect && selectedIds.length > 0) {
          isCorrect = true;
          pointsEarned = question.points;
          earnedPoints += pointsEarned;
        }
      } else {
        // Single choice (multiple_choice or true_false)
        const correctOption = question.options.find(opt => opt.isCorrect);
        if (correctOption && answer.selectedOptionId === correctOption.id) {
          isCorrect = true;
          pointsEarned = question.points;
          earnedPoints += pointsEarned;
        }
      }
    }
    // If no answer provided, isCorrect remains false and pointsEarned remains 0

    detailedAnswers.push({
      questionId: question.id,
      selectedOptionId: answer?.selectedOptionId || null,
      selectedOptionIds: answer?.selectedOptionIds || null,
      answerText: answer?.answerText || null,
      isCorrect,
      pointsEarned
    });
  }

  const score = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;
  const passed = score >= quiz.passing_score;

  return { score, passed, detailedAnswers, earnedPoints, totalPoints };
}

/**
 * Generate certificate for passed quiz
 */
async function generateCertificate(client, userEmail, user, video, score) {
  const certificateNumber = `CERT-${Date.now()}-${uuidv4().substring(0, 8).toUpperCase()}`;

  // Create EmailTemplateService instance to use helper functions
  const emailTemplateService = new EmailTemplateService();

  // Fetch the latest user data from database to get the most up-to-date full_name
  const userResult = await client.query(
    'SELECT full_name, first_name, last_name FROM users WHERE email = $1',
    [userEmail]
  );

  let userName = '';

  if (userResult.rows.length > 0) {
    const dbUser = userResult.rows[0];

    // 1. Try to use full_name from database (most reliable)
    if (dbUser.full_name && dbUser.full_name.trim()) {
      userName = dbUser.full_name.trim();
    }
    // 2. Try to construct from first_name and last_name from database
    else if ((dbUser.first_name && dbUser.first_name.trim()) || (dbUser.last_name && dbUser.last_name.trim())) {
      const firstName = dbUser.first_name ? dbUser.first_name.trim() : '';
      const lastName = dbUser.last_name ? dbUser.last_name.trim() : '';
      userName = `${firstName} ${lastName}`.trim();
    }
  }

  // 3. If database doesn't have name info, try to extract from email address
  if (!userName && userEmail) {
    const extracted = emailTemplateService.extractNameFromEmail(userEmail);
    const firstName = extracted.firstName || '';
    const lastName = extracted.lastName || '';
    userName = `${firstName} ${lastName}`.trim();
  }

  // 4. Final fallback - use a more generic term
  if (!userName) {
    userName = 'Training Participant';
  }

  await client.query(`
    INSERT INTO certificates (
      user_email,
      module_id,
      language_code,
      certificate_number,
      certificate_data
    ) VALUES ($1, $2, $3, $4, $5)
  `, [
    userEmail,
    video.module_id,
    video.language_code,
    certificateNumber,
    JSON.stringify({
      userName: userName,
      moduleTitle: video.module_title,
      score: score,
      completionDate: new Date().toISOString()
    })
  ]);

  await client.query(`
    UPDATE user_progress
    SET
      certificate_generated = true,
      certificate_generated_at = CURRENT_TIMESTAMP
    WHERE user_email = $1 AND module_id = $2 AND language_code = $3
  `, [userEmail, video.module_id, video.language_code]);

  return certificateNumber;
}

/**
 * Create quiz attempt record and detailed answers
 */
async function createQuizAttempt(client, userEmail, quiz, score, questions, passed, answers, detailedAnswers) {
  // Get next attempt number
  const attemptCountResult = await client.query(
    'SELECT COUNT(*) as count FROM quiz_attempts WHERE user_email = $1 AND quiz_id = $2',
    [userEmail, quiz.id]
  );
  const attemptNumber = parseInt(attemptCountResult.rows[0].count) + 1;

  // Create quiz attempt record
  const attemptResult = await client.query(`
    INSERT INTO quiz_attempts (
      user_email,
      quiz_id,
      attempt_number,
      score,
      total_questions,
      passed,
      completed_at,
      answers
    ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, $7)
    RETURNING id
  `, [
    userEmail,
    quiz.id,
    attemptNumber,
    score,
    questions.length,
    passed,
    JSON.stringify(answers)
  ]);

  const attemptId = attemptResult.rows[0].id;

  // Insert detailed answers
  for (const answer of detailedAnswers) {
    await client.query(`
      INSERT INTO quiz_attempt_answers (
        attempt_id,
        question_id,
        selected_option_id,
        selected_option_ids,
        answer_text,
        is_correct,
        points_earned
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      attemptId,
      answer.questionId,
      answer.selectedOptionId,
      answer.selectedOptionIds,
      answer.answerText,
      answer.isCorrect,
      answer.pointsEarned
    ]);
  }

  return { attemptId, attemptNumber };
}

/**
 * Update user progress after quiz completion
 */
async function updateUserProgress(client, userEmail, video, passed, score) {
  await client.query(`
    UPDATE user_progress
    SET
      quiz_completed = true,
      quiz_passed = $1,
      quiz_score = $2,
      quiz_completed_at = CURRENT_TIMESTAMP,
      updated_at = CURRENT_TIMESTAMP
    WHERE user_email = $3 AND module_id = $4 AND language_code = $5
  `, [passed, score, userEmail, video.module_id, video.language_code]);
}

module.exports = {
  calculateQuizScore,
  generateCertificate,
  createQuizAttempt,
  updateUserProgress
};
