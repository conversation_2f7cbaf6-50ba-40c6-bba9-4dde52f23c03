const express = require('express');
const { query } = require('../database/init');
const { requireAuth, requireAdmin } = require('../middleware/auth');
const bcrypt = require('bcryptjs');
const fs = require('fs').promises;
const path = require('path');

const router = express.Router();

// Helper function to generate default quiz data using the current quiz creation format
// This ensures compatibility with the existing quiz creation system
function generateDefaultQuizData() {
  return {
    title: 'Cybersecurity Fundamentals Quiz',
    description: 'Test your knowledge of basic cybersecurity concepts.',
    passingScore: 70,
    timeLimitMinutes: 15,
    questions: [
      {
        questionText: 'What is the most important factor in creating a strong password?',
        questionType: 'multiple_choice',
        points: 10,
        options: [
          { optionText: 'Length and complexity', isCorrect: true },
          { optionText: 'Using personal information', isCorrect: false },
          { optionText: 'Using common words', isCorrect: false },
          { optionText: 'Making it easy to remember', isCorrect: false }
        ]
      },
      {
        questionText: 'What is phishing?',
        questionType: 'multiple_choice',
        points: 10,
        options: [
          { optionText: 'A type of malware', isCorrect: false },
          { optionText: 'A social engineering attack to steal sensitive information', isCorrect: true },
          { optionText: 'A network security protocol', isCorrect: false },
          { optionText: 'A type of firewall', isCorrect: false }
        ]
      },
      {
        questionText: 'Name two things you should do if you suspect your computer has been compromised.',
        questionType: 'short_answer',
        points: 0, // Short answer questions have 0 points as per current system requirements
        options: [] // No options for short answer questions
      }
    ]
  };
}

// Helper function to create quiz using the same logic as the quiz creation API
// This ensures consistency with the existing quiz creation system
async function createQuizFromData(videoId, quizData) {
  const { title, description, passingScore, timeLimitMinutes, questions } = quizData;

  // Create quiz
  const quizResult = await query(`
    INSERT INTO quizzes (video_id, title, description, passing_score, time_limit_minutes, is_active)
    VALUES ($1, $2, $3, $4, $5, $6)
    RETURNING id
  `, [videoId, title, description, passingScore, timeLimitMinutes, true]);

  const quizId = quizResult.rows[0].id;

  // Create questions
  for (let i = 0; i < questions.length; i++) {
    const question = questions[i];

    // Ensure short answer questions have 0 points
    const points = question.questionType === 'short_answer' ? 0 : question.points;

    const questionResult = await query(`
      INSERT INTO quiz_questions (quiz_id, question_text, question_type, points, question_order, question_image)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id
    `, [quizId, question.questionText, question.questionType, points, i + 1, question.questionImage || null]);

    const questionId = questionResult.rows[0].id;

    // Add options for multiple choice and true/false questions
    if (question.options && question.options.length > 0) {
      for (let j = 0; j < question.options.length; j++) {
        const option = question.options[j];

        await query(`
          INSERT INTO quiz_question_options (question_id, option_text, is_correct, option_order, option_image)
          VALUES ($1, $2, $3, $4, $5)
        `, [questionId, option.optionText, option.isCorrect, j + 1, option.optionImage || null]);
      }
    }
  }

  return quizId;
}

// Reset database to defaults
router.post('/reset-database', requireAuth, requireAdmin, async (req, res) => {
  try {
    console.log('=== DATABASE RESET INITIATED ===');
    console.log('Admin user:', req.user.email);

    // Start transaction
    await query('BEGIN');

    try {
      // 1. Clear all user progress and attempts
      console.log('Clearing user progress...');
      await query('DELETE FROM quiz_attempt_answers');
      await query('DELETE FROM quiz_attempts');
      await query('DELETE FROM user_progress');
      await query('DELETE FROM certificates');

      // 2. Clear all training content
      console.log('Clearing training content...');
      await query('DELETE FROM quiz_question_options');
      await query('DELETE FROM quiz_questions');
      await query('DELETE FROM quizzes');
      await query('DELETE FROM training_videos');
      await query('DELETE FROM training_modules');

      // 3. Clear email configuration and system configuration
      console.log('Clearing email configuration...');
      await query('DELETE FROM email_config');

      console.log('Clearing system configuration...');
      await query('DELETE FROM system_config');

      // Ensure all missing columns exist (add if missing)
      console.log('Ensuring all required columns exist...');

      try {
        // Add language column to users table if missing
        await query('ALTER TABLE users ADD COLUMN IF NOT EXISTS language VARCHAR(10) DEFAULT \'en\'');

        // Add email template columns if missing
        await query('ALTER TABLE email_config ADD COLUMN IF NOT EXISTS email_reply_to_address VARCHAR(255)');
        await query('ALTER TABLE email_config ADD COLUMN IF NOT EXISTS email_cc_addresses TEXT');
        await query('ALTER TABLE email_config ADD COLUMN IF NOT EXISTS email_template_subject_en VARCHAR(500)');
        await query('ALTER TABLE email_config ADD COLUMN IF NOT EXISTS email_template_body_en TEXT');
        await query('ALTER TABLE email_config ADD COLUMN IF NOT EXISTS email_template_subject_zh VARCHAR(500)');
        await query('ALTER TABLE email_config ADD COLUMN IF NOT EXISTS email_template_body_zh TEXT');
        console.log('✅ All required columns verified/added');
      } catch (error) {
        console.log('Note: Some columns may already exist (this is normal)');
      }

      // 4. Clear all users except the current admin
      console.log('Clearing users (except current admin)...');
      await query('DELETE FROM users WHERE email != $1', [req.user.email]);

      // 5. Reset sequences
      console.log('Resetting sequences...');
      await query('ALTER SEQUENCE training_modules_id_seq RESTART WITH 1');
      await query('ALTER SEQUENCE training_videos_id_seq RESTART WITH 1');
      await query('ALTER SEQUENCE quizzes_id_seq RESTART WITH 1');
      await query('ALTER SEQUENCE quiz_questions_id_seq RESTART WITH 1');
      await query('ALTER SEQUENCE quiz_question_options_id_seq RESTART WITH 1');
      await query('ALTER SEQUENCE quiz_attempts_id_seq RESTART WITH 1');
      await query('ALTER SEQUENCE quiz_attempt_answers_id_seq RESTART WITH 1');
      await query('ALTER SEQUENCE user_progress_id_seq RESTART WITH 1');
      await query('ALTER SEQUENCE certificates_id_seq RESTART WITH 1');

      // 6. Create default admin user if it doesn't exist
      console.log('Ensuring default admin user exists...');
      const defaultAdminEmail = '<EMAIL>';
      const defaultAdminPassword = 'admin123';

      const existingAdmin = await query('SELECT email FROM users WHERE email = $1', [defaultAdminEmail]);

      if (existingAdmin.rows.length === 0) {
        const hashedPassword = await bcrypt.hash(defaultAdminPassword, 10);
        await query(`
          INSERT INTO users (email, password_hash, first_name, last_name, is_admin, is_active, is_default_admin)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [defaultAdminEmail, hashedPassword, 'System', 'Administrator', true, true, true]);
        console.log('Default admin user created');
      } else {
        // Ensure existing default admin has the flag set
        await query('UPDATE users SET is_default_admin = true WHERE email = $1', [defaultAdminEmail]);
        console.log('Default admin flag updated');
      }

      // 7. Create sample training module
      console.log('Creating sample training module...');
      const moduleResult = await query(`
        INSERT INTO training_modules (title, description, is_active)
        VALUES ($1, $2, $3)
        RETURNING id
      `, [
        'Cybersecurity Fundamentals',
        'Learn the basics of cybersecurity including password security, phishing awareness, and data protection.',
        true
      ]);

      const moduleId = moduleResult.rows[0].id;

      // 8. Create sample video
      console.log('Creating sample video...');
      const videoResult = await query(`
        INSERT INTO training_videos (module_id, language_code, language_name, video_filename, video_url, duration_seconds)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
      `, [
        moduleId,
        'en',
        'English',
        'sample_cybersecurity_intro.mp4', // Sample filename
        'https://www.youtube.com/watch?v=inWWhr5tnEA', // Sample cybersecurity video
        1800 // 30 minutes
      ]);

      const videoId = videoResult.rows[0].id;

      // 9. Create sample quiz using the dynamic helper function
      console.log('Creating sample quiz using dynamic generation...');
      const defaultQuizData = generateDefaultQuizData();
      const quizId = await createQuizFromData(videoId, defaultQuizData);
      console.log(`Sample quiz created with ID: ${quizId}`);

      // 10. Create default email configuration (clipboard method)
      console.log('Creating default email configuration...');

      const defaultTemplateEn = `Dear {{fullName}} ,
 
This is an automated email generated by the TradeLink Internal Training System.
 
Your account has been created for the TradeLink Internal Training System.
 
Please visit WEB ADDRESS HERE with the following credentials:
Email: {{email}}
Password: {{password}}

Please complete all required training modules as soon as possible, as the password is soon to expire 8 days later.
 
Best regards,
TradeLink Internal Training System
Systems Team
<EMAIL>
This email is sent at : {{time}} {{date}}`;

      const defaultTemplateZh = `亲爱的 {{fullName}}，
这是由 TradeLink 内部培训系统自动生成的邮件。
您的 TradeLink 内部培训系统账户已创建。
 
请使用以下凭据访问 WEB ADDRESS HERE：
邮箱：{{email}}
密码：{{password}}
 
提供的密码将在发送此邮件后的 8 天内过期，请尽快完成所有必修模块。
此致，
TradeLink 内部培训系统
系统团队
<EMAIL>
此邮件发送于：{{time}} {{date}}`;

      await query(`
        INSERT INTO email_config (
          id, email_enabled, email_method, smtp_host, smtp_port, smtp_secure,
          smtp_user, smtp_pass, email_from_name, email_from_address, email_reply_to_address, email_cc_addresses,
          email_template_subject_en, email_template_body_en, email_template_subject_zh, email_template_body_zh
        ) VALUES (1, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
      `, [
        false,                    // email_enabled
        'clipboard',              // email_method
        '',                       // smtp_host
        25,                      // smtp_port
        false,                    // smtp_secure
        '',                       // smtp_user
        '',                       // smtp_pass
        'TradeLink Internal Training System',        // email_from_name
        '',                       // email_from_address
        '<EMAIL>',                       // email_reply_to_address
        '',                       // email_cc_addresses
        'Training System Login Credentials for {{fullName}}', // email_template_subject_en
        defaultTemplateEn,        // email_template_body_en
        '{{fullName}} 的培训系统登录凭据',      // email_template_subject_zh
        defaultTemplateZh         // email_template_body_zh
      ]);
      console.log('Default email configuration created (clipboard method)');

      // 7. Create default system configuration
      console.log('Creating default system configuration...');
      await query(`
        INSERT INTO system_config (id, password_expiration_days, timezone, max_file_size)
        VALUES (1, 8, 'Asia/Shanghai', '500MB')
      `);
      console.log('Default system configuration created (8 days password expiration, Asia/Shanghai timezone, 500MB max file size)');

      // Commit transaction
      await query('COMMIT');

      console.log('=== DATABASE RESET COMPLETED SUCCESSFULLY ===');

      res.json({
        success: true,
        message: 'Database reset completed successfully',
        details: {
          defaultAdmin: {
            email: '<EMAIL>',
            password: 'admin123'
          },
          sampleModule: {
            title: 'Cybersecurity Fundamentals',
            videoCount: 1,
            quizQuestions: 3
          },
          emailConfiguration: {
            method: 'clipboard',
            enabled: false,
            note: 'Email system reset to clipboard method (disabled)'
          },
          systemConfiguration: {
            passwordExpirationDays: 8,
            note: 'Password expiration set to default 8 days'
          }
        }
      });

    } catch (error) {
      // Rollback on error
      await query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('Database reset error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset database',
      details: error.message
    });
  }
});

// Clear uploaded files
router.post('/clear-uploads', requireAuth, requireAdmin, async (req, res) => {
  try {
    console.log('=== CLEARING UPLOADED FILES ===');
    console.log('Admin user:', req.user.email);

    const uploadsDir = path.join(__dirname, '../uploads/videos');
    
    try {
      // Check if directory exists
      await fs.access(uploadsDir);
      
      // Read directory contents
      const files = await fs.readdir(uploadsDir);
      
      // Delete all files
      for (const file of files) {
        const filePath = path.join(uploadsDir, file);
        await fs.unlink(filePath);
        console.log(`Deleted: ${file}`);
      }
      
      console.log(`Cleared ${files.length} uploaded files`);
      
      res.json({
        success: true,
        message: `Successfully cleared ${files.length} uploaded files`,
        filesDeleted: files.length
      });
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        // Directory doesn't exist, that's fine
        res.json({
          success: true,
          message: 'No uploaded files to clear',
          filesDeleted: 0
        });
      } else {
        throw error;
      }
    }

  } catch (error) {
    console.error('Clear uploads error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear uploaded files',
      details: error.message
    });
  }
});

// Create test video with YouTube URL (for E2E testing)
router.post('/create-test-video', requireAuth, requireAdmin, async (req, res) => {
  try {
    console.log('=== CREATING TEST VIDEO WITH YOUTUBE URL ===');
    console.log('Admin user:', req.user.email);
    console.log('Request body:', req.body);

    const { moduleId, languageCode, languageName, videoUrl, durationSeconds } = req.body;

    // Validate required fields
    if (!moduleId || !languageCode || !languageName || !videoUrl) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['moduleId', 'languageCode', 'languageName', 'videoUrl']
      });
    }

    // Check if module exists
    const moduleCheck = await query('SELECT id FROM training_modules WHERE id = $1', [moduleId]);
    if (moduleCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    // Create video record with YouTube URL
    const videoResult = await query(`
      INSERT INTO training_videos (module_id, language_code, language_name, video_filename, video_url, duration_seconds)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (module_id, language_code)
      DO UPDATE SET
        language_name = EXCLUDED.language_name,
        video_filename = EXCLUDED.video_filename,
        video_url = EXCLUDED.video_url,
        duration_seconds = EXCLUDED.duration_seconds,
        updated_at = CURRENT_TIMESTAMP
      RETURNING *
    `, [
      moduleId,
      languageCode.toLowerCase(),
      languageName,
      `test_video_${languageCode}.mp4`,
      videoUrl,
      durationSeconds || 1800
    ]);

    console.log('Test video created successfully:', videoResult.rows[0]);

    res.status(201).json({
      success: true,
      message: 'Test video created successfully',
      video: videoResult.rows[0]
    });

  } catch (error) {
    console.error('Create test video error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

module.exports = router;
