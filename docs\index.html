<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Internal Training System - Documentation</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Internal Training System</h1>
            <p class="subtitle">Comprehensive Documentation</p>
        </header>

        <nav class="nav">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#overview">System Overview</a></li>
                <li><a href="#setup">Setup and Installation</a></li>
                <li><a href="#account-management">Account Management</a></li>
                <li><a href="#module-management">Module Management</a></li>
                <li><a href="#video-upload">Video Upload System</a></li>
                <li><a href="#quiz-certificate">Quiz and Certificate System</a></li>
                <li><a href="#system-config">System Configuration</a></li>
                <li><a href="#architecture">System Architecture</a></li>
                <li><a href="#known-issues">Known Issues and Quirks</a></li>
                <li><a href="#api-docs">API Documentation</a></li>
                <li><a href="#troubleshooting">Troubleshooting</a></li>
            </ul>
        </nav>

        <main class="content">
            <section id="overview" class="section">
                <h2>System Overview</h2>
                <p>The Internal Training System is a comprehensive web-based platform designed for cybersecurity video training and assessment. It provides a complete learning management system with video streaming, progress tracking, quizzes, and certificate generation.</p>
                
                <h3>Key Features</h3>
                <ul>
                    <li><strong>Multi-language Support:</strong> English and Chinese interface with language-specific training content</li>
                    <li><strong>Video Training Modules:</strong> Chunked video upload system supporting large files (up to configurable limits)</li>
                    <li><strong>Progress Tracking:</strong> Detailed user progress monitoring with watch time and completion status</li>
                    <li><strong>Quiz System:</strong> Multiple choice and short answer questions with automatic scoring</li>
                    <li><strong>Certificate Generation:</strong> Automatic certificate creation upon successful quiz completion</li>
                    <li><strong>User Management:</strong> Role-based access control with admin and regular user roles</li>
                    <li><strong>Email Integration:</strong> Automated email notifications with customizable templates</li>
                    <li><strong>Docker Support:</strong> Complete containerized deployment with PostgreSQL database</li>
                </ul>

                <h3>Technology Stack</h3>
                <div class="tech-stack">
                    <div class="tech-category">
                        <h4>Frontend</h4>
                        <ul>
                            <li>React 18.2.0</li>
                            <li>Ant Design 5.12.8</li>
                            <li>Vite 6.3.5</li>
                            <li>React Router 6.8.1</li>
                            <li>React Query 3.39.3</li>
                            <li>i18next (internationalization)</li>
                        </ul>
                    </div>
                    <div class="tech-category">
                        <h4>Backend</h4>
                        <ul>
                            <li>Node.js with Express 4.18.2</li>
                            <li>PostgreSQL 15</li>
                            <li>JWT Authentication</li>
                            <li>Multer (file uploads)</li>
                            <li>bcrypt (password hashing)</li>
                            <li>Nodemailer (email)</li>
                        </ul>
                    </div>
                    <div class="tech-category">
                        <h4>Infrastructure</h4>
                        <ul>
                            <li>Docker & Docker Compose</li>
                            <li>Nginx (reverse proxy)</li>
                            <li>File-based video storage</li>
                            <li>Session-based authentication</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section id="setup" class="section">
                <h2>Setup and Installation</h2>
                
                <h3>Prerequisites</h3>
                <ul>
                    <li>Node.js 22+ (preferred) or 18+</li>
                    <li>PostgreSQL 15+</li>
                    <li>Docker and Docker Compose (for containerized deployment)</li>
                    <li>Git</li>
                </ul>

                <h3>Environment Variables</h3>
                <p>Create a <code>.env</code> file in the server directory with the following variables:</p>
                
                <pre><code class="language-bash"># Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=training_system
DB_USER=training_user
DB_PASSWORD=training_password

# Server Configuration
PORT=3002
NODE_ENV=development
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# Email Configuration (Optional)
EMAIL_ENABLED=false
EMAIL_METHOD=clipboard
SMTP_HOST=
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=
SMTP_PASS=
EMAIL_FROM_NAME=TradeLink Training System
EMAIL_FROM_ADDRESS=

# File Upload Configuration
UPLOAD_PATH=./uploads
</code></pre>

                <h3>Local Development Setup</h3>
                <ol>
                    <li><strong>Clone the repository:</strong>
                        <pre><code class="language-bash">git clone &lt;repository-url&gt;
cd Internal-Training-System</code></pre>
                    </li>
                    
                    <li><strong>Install dependencies:</strong>
                        <pre><code class="language-bash"># Install root dependencies
npm install

# Install server dependencies
cd server && npm install

# Install client dependencies
cd ../client && npm install</code></pre>
                    </li>
                    
                    <li><strong>Setup PostgreSQL database:</strong>
                        <pre><code class="language-bash"># Create database and user
sudo -u postgres psql
CREATE DATABASE training_system;
CREATE USER training_user WITH PASSWORD 'training_password';
GRANT ALL PRIVILEGES ON DATABASE training_system TO training_user;</code></pre>
                    </li>
                    
                    <li><strong>Initialize database schema:</strong>
                        <pre><code class="language-bash"># The database schema will be automatically initialized when the server starts
cd server
npm run dev</code></pre>
                    </li>
                    
                    <li><strong>Start development servers:</strong>
                        <pre><code class="language-bash"># From project root - starts both frontend and backend
npm run dev

# Or start individually:
npm run dev:server  # Backend on port 3002
npm run dev:client  # Frontend on port 3000</code></pre>
                    </li>
                </ol>

                <h3>Docker Deployment</h3>
                <p>The system includes a complete Docker Compose setup for production deployment:</p>
                
                <ol>
                    <li><strong>Configure environment:</strong>
                        <p>Update the <code>docker-compose.yml</code> file with your specific configuration, particularly the <code>REACT_APP_API_URL</code> in the frontend build args.</p>
                    </li>
                    
                    <li><strong>Deploy with Docker Compose:</strong>
                        <pre><code class="language-bash"># Build and start all services
npm run docker:up

# Or manually:
docker-compose up --build

# View logs
npm run docker:logs

# Stop services
npm run docker:down</code></pre>
                    </li>
                    
                    <li><strong>Access the application:</strong>
                        <ul>
                            <li>Frontend: <code>http://localhost:3001</code></li>
                            <li>Backend API: <code>http://localhost:3002</code></li>
                            <li>PostgreSQL: <code>localhost:5432</code></li>
                        </ul>
                    </li>
                </ol>

                <div class="warning-box">
                    <h4>⚠️ Important Notes</h4>
                    <ul>
                        <li>The system runs Node.js processes as root user inside Docker containers to avoid file permission issues</li>
                        <li>Upload directory requires write permissions for the application user</li>
                        <li>Default admin account is created automatically on first startup</li>
                        <li>Change default passwords and secrets in production</li>
                    </ul>
                </div>

                <h3>Initial Configuration</h3>
                <ol>
                    <li><strong>Create default admin account:</strong>
                        <pre><code class="language-bash"># Run the migration script to create default admin
npm run migrate:default-admin</code></pre>
                    </li>
                    
                    <li><strong>Access admin panel:</strong>
                        <p>Login with the default admin credentials and navigate to the admin panel to configure:</p>
                        <ul>
                            <li>System settings (password expiration, file size limits)</li>
                            <li>Email configuration</li>
                            <li>User management</li>
                            <li>Training modules</li>
                        </ul>
                    </li>
                </ol>
            </section>

            <section id="account-management" class="section">
                <h2>Account Management</h2>

                <h3>User Roles and Permissions</h3>
                <div class="role-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Feature</th>
                                <th>Regular User</th>
                                <th>Admin User</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>View training modules</td>
                                <td>✅ Active modules only</td>
                                <td>✅ All modules</td>
                            </tr>
                            <tr>
                                <td>Watch training videos</td>
                                <td>✅</td>
                                <td>✅</td>
                            </tr>
                            <tr>
                                <td>Take quizzes</td>
                                <td>✅</td>
                                <td>✅</td>
                            </tr>
                            <tr>
                                <td>Generate certificates</td>
                                <td>✅</td>
                                <td>✅</td>
                            </tr>
                            <tr>
                                <td>Create/edit modules</td>
                                <td>❌</td>
                                <td>✅</td>
                            </tr>
                            <tr>
                                <td>Upload/replace videos</td>
                                <td>❌</td>
                                <td>✅</td>
                            </tr>
                            <tr>
                                <td>User management</td>
                                <td>❌</td>
                                <td>✅</td>
                            </tr>
                            <tr>
                                <td>System configuration</td>
                                <td>❌</td>
                                <td>✅</td>
                            </tr>
                            <tr>
                                <td>View reports</td>
                                <td>❌</td>
                                <td>✅</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Authentication Flow</h3>
                <ol>
                    <li><strong>Login Process:</strong>
                        <ul>
                            <li>Email and password validation using Joi schema</li>
                            <li>bcrypt password verification with performance logging</li>
                            <li>JWT token generation with user information</li>
                            <li>Session creation with 24-hour expiration</li>
                            <li>Last login timestamp update</li>
                        </ul>
                    </li>

                    <li><strong>Password Security:</strong>
                        <ul>
                            <li>Passwords are hashed using bcrypt with salt rounds</li>
                            <li>Configurable password expiration (default: 8 days)</li>
                            <li>Password creation timestamp tracking</li>
                            <li>Secure password generation for new users</li>
                        </ul>
                    </li>

                    <li><strong>Session Management:</strong>
                        <ul>
                            <li>HTTP-only cookies for security</li>
                            <li>Secure flag enabled in production</li>
                            <li>24-hour session timeout</li>
                            <li>Automatic session cleanup</li>
                        </ul>
                    </li>
                </ol>

                <h3>User Management Procedures</h3>

                <h4>Creating New Users</h4>
                <ol>
                    <li>Navigate to Admin Panel → User Management</li>
                    <li>Click "Add New User"</li>
                    <li>Fill in user details:
                        <ul>
                            <li>Email (used as primary key)</li>
                            <li>First Name, Last Name, Full Name</li>
                            <li>Admin privileges (checkbox)</li>
                            <li>Preferred language (en/zh)</li>
                        </ul>
                    </li>
                    <li>System generates secure password automatically</li>
                    <li>User credentials are sent via configured email method</li>
                </ol>

                <h4>User Account States</h4>
                <ul>
                    <li><strong>Active:</strong> User can login and access system</li>
                    <li><strong>Inactive:</strong> Account disabled, login blocked</li>
                    <li><strong>Password Expired:</strong> User must change password on next login</li>
                    <li><strong>Default Admin:</strong> Special flag for system-created admin accounts</li>
                </ul>

                <div class="info-box">
                    <h4>💡 User Management Tips</h4>
                    <ul>
                        <li>Email addresses are case-insensitive and stored in lowercase</li>
                        <li>Full Name field is used for certificate generation</li>
                        <li>User language preference affects interface and email templates</li>
                        <li>Admin users can manage other admin accounts</li>
                        <li>Default admin account cannot be deleted through UI</li>
                    </ul>
                </div>
            </section>

            <section id="module-management" class="section">
                <h2>Module Management</h2>

                <h3>Training Module Structure</h3>
                <p>Training modules are the core content units in the system. Each module can contain:</p>
                <ul>
                    <li><strong>Multiple language versions</strong> of the same content</li>
                    <li><strong>Video content</strong> with automatic duration detection</li>
                    <li><strong>Associated quizzes</strong> for assessment</li>
                    <li><strong>Progress tracking</strong> per user per language</li>
                    <li><strong>Certificate generation</strong> upon completion</li>
                </ul>

                <h3>Module Properties</h3>
                <div class="property-list">
                    <ul>
                        <li><strong>ID:</strong> Auto-generated unique identifier</li>
                        <li><strong>Title:</strong> Module name (1-255 characters)</li>
                        <li><strong>Description:</strong> Optional detailed description (max 1000 characters)</li>
                        <li><strong>Status:</strong> Active/Inactive (affects visibility to regular users)</li>
                        <li><strong>Created/Updated timestamps:</strong> Automatic tracking</li>
                    </ul>
                </div>

                <h3>Creating and Managing Modules</h3>

                <h4>Creating a New Module</h4>
                <ol>
                    <li>Navigate to Admin Panel → Module Management</li>
                    <li>Click "Create New Module"</li>
                    <li>Enter module title and description</li>
                    <li>Module is created in active state by default</li>
                    <li>Upload videos for different languages</li>
                    <li>Create associated quizzes</li>
                </ol>

                <h4>Video Management per Module</h4>
                <p>Each module supports multiple language versions:</p>
                <ul>
                    <li><strong>Language Code:</strong> ISO language code (e.g., 'en', 'zh')</li>
                    <li><strong>Language Name:</strong> Display name (e.g., 'English', '中文')</li>
                    <li><strong>Video File:</strong> Uploaded video content</li>
                    <li><strong>Duration:</strong> Automatically detected using ffprobe</li>
                    <li><strong>File Size:</strong> Tracked for storage management</li>
                </ul>

                <h3>Progress Tracking System</h3>
                <p>The system tracks detailed progress for each user-module-language combination:</p>

                <h4>Progress Metrics</h4>
                <ul>
                    <li><strong>Video Watch Time:</strong> Seconds watched (can exceed video duration)</li>
                    <li><strong>Video Completion:</strong> Boolean flag for video completion</li>
                    <li><strong>Quiz Completion:</strong> Boolean flag for quiz attempt</li>
                    <li><strong>Quiz Score:</strong> Percentage score achieved</li>
                    <li><strong>Quiz Passed:</strong> Boolean based on passing score threshold</li>
                    <li><strong>Certificate Generated:</strong> Boolean flag for certificate creation</li>
                </ul>

                <h4>Progress Calculation Logic</h4>
                <pre><code class="language-javascript">// Progress is calculated only for the selected language
// Not across all available languages for that module
const moduleProgress = {
  videoWatched: watchTimeSeconds > 0,
  videoCompleted: watchTimeSeconds >= videoDurationSeconds * 0.9, // 90% threshold
  quizCompleted: quizAttempted,
  quizPassed: quizScore >= passingScore,
  certificateEligible: videoCompleted && quizPassed
};</code></pre>

                <div class="warning-box">
                    <h4>⚠️ Important Progress Tracking Notes</h4>
                    <ul>
                        <li>Progress is tracked per language - users must complete each language separately</li>
                        <li>Watch time can exceed video duration (due to rewinding/replaying)</li>
                        <li>Video completion requires 90% watch time threshold</li>
                        <li>Certificate generation requires both video completion and quiz passing</li>
                        <li>Progress data is stored in user_progress table with composite keys</li>
                    </ul>
                </div>

                <h3>Module Status Management</h3>
                <ul>
                    <li><strong>Active Modules:</strong> Visible to all users, can be accessed</li>
                    <li><strong>Inactive Modules:</strong> Hidden from regular users, admin-only access</li>
                    <li><strong>Toggle Status:</strong> Admins can activate/deactivate modules instantly</li>
                    <li><strong>Soft Delete:</strong> Modules are never permanently deleted, only deactivated</li>
                </ul>
            </section>

            <section id="video-upload" class="section">
                <h2>Video Upload System</h2>

                <h3>Chunked Upload Architecture</h3>
                <p>The system implements a sophisticated chunked upload mechanism to handle large video files reliably:</p>

                <h4>Key Features</h4>
                <ul>
                    <li><strong>10MB Chunk Size:</strong> Files are split into 10MB chunks for upload</li>
                    <li><strong>Concurrent Uploads:</strong> 3 chunks uploaded simultaneously by default</li>
                    <li><strong>Retry Logic:</strong> Failed chunks are retried up to 3 times with exponential backoff</li>
                    <li><strong>Progress Tracking:</strong> Real-time upload progress with detailed metrics</li>
                    <li><strong>Infinite Timeout:</strong> No timeout limits for upload operations</li>
                    <li><strong>Automatic Cleanup:</strong> Orphaned chunks are cleaned up periodically</li>
                </ul>

                <h4>Upload Process Flow</h4>
                <ol>
                    <li><strong>Initiation:</strong>
                        <pre><code class="language-javascript">// Client calculates file chunks
const chunks = Math.ceil(file.size / CHUNK_SIZE);
const uploadId = await initiateUpload(moduleId, {
  filename: file.name,
  fileSize: file.size,
  totalChunks: chunks,
  languageCode: 'en',
  languageName: 'English'
});</code></pre>
                    </li>

                    <li><strong>Chunk Upload:</strong>
                        <pre><code class="language-javascript">// Upload chunks with retry logic
for (let i = 0; i < chunks; i++) {
  const chunk = file.slice(i * CHUNK_SIZE, (i + 1) * CHUNK_SIZE);
  await uploadChunk(moduleId, uploadId, i, chunk);
}</code></pre>
                    </li>

                    <li><strong>Finalization:</strong>
                        <pre><code class="language-javascript">// Server reassembles chunks into final video file
await finalizeUpload(moduleId, uploadId);
// Chunks are cleaned up automatically</code></pre>
                    </li>
                </ol>

                <h3>File Validation and Processing</h3>

                <h4>Supported Video Formats</h4>
                <ul>
                    <li><strong>Extensions:</strong> .mp4, .avi, .mov, .wmv, .flv, .webm, .mkv</li>
                    <li><strong>MIME Types:</strong> video/mp4, video/avi, video/quicktime, video/x-msvideo, etc.</li>
                    <li><strong>Size Limits:</strong> Configurable (default: 500MB, can be set up to several GB)</li>
                </ul>

                <h4>Automatic Processing</h4>
                <ul>
                    <li><strong>Duration Detection:</strong> Uses ffprobe to extract video duration</li>
                    <li><strong>File Validation:</strong> MIME type and extension validation</li>
                    <li><strong>Unique Naming:</strong> Automatic filename generation to prevent conflicts</li>
                    <li><strong>Database Integration:</strong> Video metadata stored in training_videos table</li>
                </ul>

                <h3>Video Replacement System</h3>
                <p>The system supports in-place video replacement with atomic operations:</p>

                <h4>Replacement Process</h4>
                <ol>
                    <li><strong>Upload New Video:</strong> Uses same chunked upload system</li>
                    <li><strong>Atomic Swap:</strong> Database transaction ensures consistency</li>
                    <li><strong>File Management:</strong> Old file deleted after successful replacement</li>
                    <li><strong>Metadata Update:</strong> Duration and file information updated</li>
                </ol>

                <h4>Replacement Endpoints</h4>
                <pre><code class="language-bash"># Chunked replacement endpoints
POST /api/training/modules/:id/videos/:languageCode/replace/initiate
POST /api/training/modules/:id/videos/:languageCode/replace/chunk
POST /api/training/modules/:id/videos/:languageCode/replace/finalize
POST /api/training/modules/:id/videos/:languageCode/replace/cancel

# Single file replacement (legacy)
PUT /api/training/modules/:id/videos/:languageCode/replace</code></pre>

                <h3>Storage Management</h3>

                <h4>File Organization</h4>
                <pre><code class="language-bash">server/uploads/
├── videos/           # Final video files
│   ├── video-1234567890-abc123.mp4
│   └── video-1234567891-def456.avi
├── chunks/           # Temporary chunk storage
│   ├── upload-uuid-1/
│   │   ├── chunk-000000
│   │   ├── chunk-000001
│   │   └── chunk-000002
│   └── upload-uuid-2/
└── temp/             # Temporary processing files</code></pre>

                <h4>Cleanup Mechanisms</h4>
                <ul>
                    <li><strong>Automatic Cleanup:</strong> Runs every 30 minutes</li>
                    <li><strong>Expired Uploads:</strong> Removes uploads older than 24 hours</li>
                    <li><strong>Orphaned Chunks:</strong> Cleans up chunks without active uploads</li>
                    <li><strong>Failed Uploads:</strong> Removes incomplete upload directories</li>
                </ul>

                <div class="info-box">
                    <h4>💡 Upload System Tips</h4>
                    <ul>
                        <li>Large files are handled efficiently through chunking</li>
                        <li>Upload can be resumed if connection is lost</li>
                        <li>Progress is shown in real-time with detailed metrics</li>
                        <li>Server handles file permissions automatically in Docker</li>
                        <li>Video duration is extracted automatically using ffprobe</li>
                    </ul>
                </div>

                <div class="warning-box">
                    <h4>⚠️ Upload System Limitations</h4>
                    <ul>
                        <li>Maximum file size is configurable but limited by disk space</li>
                        <li>Chunk upload requires stable internet connection</li>
                        <li>Video processing may take time for very large files</li>
                        <li>Only one upload per module-language combination at a time</li>
                        <li>Failed uploads require manual cleanup if automatic cleanup fails</li>
                    </ul>
                </div>
            </section>

            <section id="quiz-certificate" class="section">
                <h2>Quiz and Certificate System</h2>

                <h3>Quiz System Overview</h3>
                <p>The quiz system provides comprehensive assessment capabilities with automatic scoring and certificate generation.</p>

                <h4>Question Types</h4>
                <ul>
                    <li><strong>Multiple Choice:</strong> Single correct answer selection</li>
                    <li><strong>Multi-Choice:</strong> Multiple correct answers (all must be selected)</li>
                    <li><strong>Short Answer:</strong> Text input (manual grading required)</li>
                </ul>

                <h4>Quiz Configuration</h4>
                <ul>
                    <li><strong>Passing Score:</strong> Configurable percentage threshold</li>
                    <li><strong>Time Limit:</strong> Optional time constraints</li>
                    <li><strong>Question Points:</strong> Weighted scoring system</li>
                    <li><strong>Language Support:</strong> Quizzes can be language-specific</li>
                </ul>

                <h3>Scoring Algorithm</h3>
                <pre><code class="language-javascript">// Quiz scoring logic
function calculateScore(quiz, answers, questions) {
  let totalPoints = 0;
  let earnedPoints = 0;

  for (const question of questions) {
    totalPoints += question.points;

    if (question.question_type === 'multi_choice') {
      // All correct options must be selected, no incorrect ones
      const correctIds = question.options
        .filter(opt => opt.isCorrect)
        .map(opt => opt.id);
      const selectedIds = answer.selectedOptionIds || [];

      const hasAllCorrect = correctIds.every(id => selectedIds.includes(id));
      const hasOnlyCorrect = selectedIds.every(id => correctIds.includes(id));

      if (hasAllCorrect && hasOnlyCorrect) {
        earnedPoints += question.points;
      }
    }
    // Short answer questions require manual grading
  }

  const score = Math.round((earnedPoints / totalPoints) * 100);
  const passed = score >= quiz.passing_score;

  return { score, passed };
}</code></pre>

                <h3>Certificate Generation</h3>
                <p>Certificates are automatically generated when users pass quizzes with the required score.</p>

                <h4>Certificate Features</h4>
                <ul>
                    <li><strong>Unique Certificate Numbers:</strong> Format: CERT-timestamp-UUID</li>
                    <li><strong>User Full Name:</strong> Uses full_name field from user database</li>
                    <li><strong>Module Information:</strong> Training module title and completion date</li>
                    <li><strong>TradeLink Branding:</strong> Company logo and styling</li>
                    <li><strong>Print Optimization:</strong> CSS optimized for printing</li>
                </ul>

                <h4>Certificate Layout Issues</h4>
                <div class="warning-box">
                    <h4>⚠️ Known Certificate PDF Issues</h4>
                    <ul>
                        <li>Print buttons at top: Bottom content may be cut off in PDF</li>
                        <li>Print buttons at bottom: Top content may be cut off in PDF</li>
                        <li>Current implementation has print button at top only</li>
                        <li>Users should use browser's print preview to check layout</li>
                    </ul>
                </div>

                <h3>Certificate Database Schema</h3>
                <pre><code class="language-sql">-- Certificate tracking in user_progress table
UPDATE user_progress SET
  quiz_completed = true,
  quiz_score = $score,
  quiz_passed = $passed,
  certificate_generated = $certificateGenerated,
  certificate_number = $certificateNumber,
  updated_at = CURRENT_TIMESTAMP
WHERE user_email = $email
  AND module_id = $moduleId
  AND language_code = $languageCode;</code></pre>
            </section>

            <section id="system-config" class="section">
                <h2>System Configuration</h2>

                <h3>Configuration Management</h3>
                <p>The system provides comprehensive configuration management through both database settings and environment variables.</p>

                <h4>System Settings</h4>
                <ul>
                    <li><strong>Password Expiration:</strong> Configurable days (1-365, default: 8)</li>
                    <li><strong>System Timezone:</strong> Default: Asia/Shanghai</li>
                    <li><strong>Max File Size:</strong> Configurable upload limit (default: 500MB)</li>
                    <li><strong>Email Configuration:</strong> SMTP settings and templates</li>
                </ul>

                <h3>Email System Configuration</h3>

                <h4>Email Methods</h4>
                <ul>
                    <li><strong>Clipboard:</strong> Credentials copied to clipboard (development)</li>
                    <li><strong>SMTP:</strong> Email sent via configured SMTP server</li>
                    <li><strong>Disabled:</strong> No email functionality</li>
                </ul>

                <h4>SMTP Configuration</h4>
                <pre><code class="language-javascript">// Email configuration structure
{
  email_enabled: true,
  email_method: 'smtp',
  smtp_host: 'smtp.gmail.com',
  smtp_port: 587,
  smtp_secure: false,
  smtp_user: '<EMAIL>',
  smtp_pass: 'encrypted-password',
  email_from_name: 'TradeLink Training System',
  email_from_address: 'encrypted-address'
}</code></pre>

                <h4>Email Templates</h4>
                <p>The system supports bilingual email templates with variable substitution:</p>

                <h5>Available Variables</h5>
                <ul>
                    <li><code>{{firstName}}</code> - User's first name</li>
                    <li><code>{{lastName}}</code> - User's last name</li>
                    <li><code>{{fullName}}</code> - User's full name</li>
                    <li><code>{{email}}</code> - User's email address</li>
                    <li><code>{{password}}</code> - Generated password</li>
                    <li><code>{{passwordExpirationDays}}</code> - Days until expiration</li>
                    <li><code>{{passwordExpirationDate}}</code> - Expiration date</li>
                    <li><code>{{currentDate}}</code> - Current date</li>
                    <li><code>{{currentTime}}</code> - Current time</li>
                    <li><code>{{currentYear}}</code> - Current year</li>
                </ul>

                <h3>File Size Configuration</h3>
                <p>The system supports dynamic file size limits with proper validation:</p>

                <pre><code class="language-javascript">// File size parsing and validation
function parseFileSize(sizeStr) {
  const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)$/i);
  if (!match) throw new Error('Invalid file size format');

  const value = parseFloat(match[1]);
  const unit = match[2].toUpperCase();
  const multipliers = { B: 1, KB: 1024, MB: 1024**2, GB: 1024**3, TB: 1024**4 };

  return value * multipliers[unit];
}</code></pre>

                <div class="info-box">
                    <h4>💡 Configuration Tips</h4>
                    <ul>
                        <li>Email passwords and addresses are encrypted in database</li>
                        <li>File size limits are cached for performance</li>
                        <li>Configuration changes take effect immediately</li>
                        <li>Test email functionality before deploying</li>
                        <li>Use environment variables for sensitive data</li>
                    </ul>
                </div>
            </section>

            <section id="architecture" class="section">
                <h2>System Architecture</h2>

                <h3>Application Architecture</h3>
                <div class="architecture-diagram">
                    <pre><code>┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Client  │    │  Express Server │    │   PostgreSQL    │
│                 │    │                 │    │    Database     │
│ • Ant Design    │◄──►│ • REST API      │◄──►│                 │
│ • React Router  │    │ • JWT Auth      │    │ • User Data     │
│ • React Query   │    │ • File Upload   │    │ • Training Data │
│ • i18next       │    │ • Video Stream  │    │ • Progress      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │  File Storage   │              │
         └──────────────►│                 │◄─────────────┘
                        │ • Video Files   │
                        │ • Temp Chunks   │
                        │ • Upload Cache  │
                        └─────────────────┘</code></pre>
                </div>

                <h3>Database Schema Overview</h3>

                <h4>Core Tables</h4>
                <ul>
                    <li><strong>users:</strong> User accounts and authentication</li>
                    <li><strong>training_modules:</strong> Training content modules</li>
                    <li><strong>training_videos:</strong> Video files and metadata</li>
                    <li><strong>user_progress:</strong> User completion tracking</li>
                    <li><strong>quizzes:</strong> Quiz definitions and settings</li>
                    <li><strong>quiz_questions:</strong> Individual quiz questions</li>
                    <li><strong>quiz_attempts:</strong> User quiz submissions</li>
                    <li><strong>system_config:</strong> System-wide settings</li>
                    <li><strong>email_config:</strong> Email configuration</li>
                </ul>

                <h4>Key Relationships</h4>
                <pre><code class="language-sql">-- User Progress Tracking
user_progress (
  user_email → users.email,
  module_id → training_modules.id,
  language_code → training_videos.language_code
)

-- Video-Module Relationship
training_videos (
  module_id → training_modules.id,
  language_code (composite key)
)

-- Quiz-Video Relationship
quizzes (
  video_id → training_videos.id
)</code></pre>

                <h3>API Architecture</h3>

                <h4>Route Organization</h4>
                <ul>
                    <li><strong>/api/auth:</strong> Authentication endpoints</li>
                    <li><strong>/api/users:</strong> User management</li>
                    <li><strong>/api/training:</strong> Training modules and videos</li>
                    <li><strong>/api/quiz:</strong> Quiz system</li>
                    <li><strong>/api/reports:</strong> Admin reporting</li>
                    <li><strong>/api/admin:</strong> Administrative functions</li>
                    <li><strong>/api/email-config:</strong> Email settings</li>
                    <li><strong>/api/system-config:</strong> System settings</li>
                </ul>

                <h4>Middleware Stack</h4>
                <ol>
                    <li><strong>Security:</strong> Helmet, CORS, Rate limiting</li>
                    <li><strong>Parsing:</strong> JSON, URL-encoded, Multipart</li>
                    <li><strong>Session:</strong> Express-session with secure cookies</li>
                    <li><strong>Authentication:</strong> JWT verification</li>
                    <li><strong>Authorization:</strong> Role-based access control</li>
                    <li><strong>Logging:</strong> Morgan HTTP request logging</li>
                </ol>

                <h3>Deployment Architecture</h3>

                <h4>Docker Compose Services</h4>
                <ul>
                    <li><strong>postgres:</strong> PostgreSQL 15 database</li>
                    <li><strong>backend:</strong> Node.js Express server</li>
                    <li><strong>frontend:</strong> React application with Nginx</li>
                </ul>

                <h4>Network Configuration</h4>
                <pre><code class="language-yaml"># Docker network setup
networks:
  training_network:
    driver: bridge

# Port mapping
frontend: 3001:3001  # React app
backend:  3002:3002  # Express API
postgres: 5432:5432  # Database</code></pre>

                <h4>Volume Management</h4>
                <ul>
                    <li><strong>postgres_data:</strong> Database persistence</li>
                    <li><strong>./server/uploads:/app/uploads:</strong> File storage</li>
                    <li><strong>./server/database/init.sql:</strong> Database initialization</li>
                </ul>
            </section>

            <section id="known-issues" class="section">
                <h2>Known Issues and Quirks</h2>

                <h3>System Limitations</h3>

                <h4>Certificate PDF Generation</h4>
                <div class="warning-box">
                    <ul>
                        <li><strong>Layout Issues:</strong> Print buttons at top cause bottom content to be cut off in PDF export</li>
                        <li><strong>Workaround:</strong> Users should use browser's print preview to verify layout before printing</li>
                        <li><strong>Current State:</strong> Print button positioned at top of certificate page</li>
                        <li><strong>Impact:</strong> May affect certificate completeness in PDF format</li>
                    </ul>
                </div>

                <h4>Progress Tracking Behavior</h4>
                <ul>
                    <li><strong>Language-Specific Progress:</strong> Progress is calculated only for selected language, not across all languages</li>
                    <li><strong>Watch Time Accumulation:</strong> Watch time can exceed video duration due to rewinding/replaying</li>
                    <li><strong>Completion Threshold:</strong> Video completion requires 90% watch time, not 100%</li>
                    <li><strong>Quiz Dependency:</strong> Certificate generation requires both video completion AND quiz passing</li>
                </ul>

                <h4>File Upload Constraints</h4>
                <ul>
                    <li><strong>Concurrent Uploads:</strong> Only one upload per module-language combination allowed</li>
                    <li><strong>Chunk Size Fixed:</strong> 10MB chunk size is hardcoded, not configurable</li>
                    <li><strong>Browser Dependency:</strong> Large file uploads require stable browser connection</li>
                    <li><strong>Cleanup Timing:</strong> Failed uploads may require manual cleanup if automatic cleanup fails</li>
                </ul>

                <h3>Docker-Specific Quirks</h3>

                <h4>File Permissions</h4>
                <ul>
                    <li><strong>Root User Requirement:</strong> Node.js processes run as root inside containers to avoid permission issues</li>
                    <li><strong>Upload Directory:</strong> Requires full write permissions for application user</li>
                    <li><strong>Volume Mounting:</strong> Host directory permissions must allow container write access</li>
                </ul>

                <h4>Network Configuration</h4>
                <ul>
                    <li><strong>API URL Configuration:</strong> Frontend build requires correct REACT_APP_API_URL at build time</li>
                    <li><strong>Host IP Dependency:</strong> Default configuration uses specific IP (**************)</li>
                    <li><strong>Port Conflicts:</strong> Ensure ports 3001, 3002, 5432 are available</li>
                </ul>

                <h3>Database Quirks</h3>

                <h4>Email as Primary Key</h4>
                <ul>
                    <li><strong>Case Sensitivity:</strong> Emails stored in lowercase, but comparison is case-insensitive</li>
                    <li><strong>Update Limitations:</strong> Changing user email requires careful handling of foreign key relationships</li>
                    <li><strong>Migration Complexity:</strong> Email changes affect user_progress and other related tables</li>
                </ul>

                <h4>Password Management</h4>
                <ul>
                    <li><strong>Bcrypt Performance:</strong> Password verification includes performance logging (may be verbose)</li>
                    <li><strong>Expiration Logic:</strong> Password expiration based on creation timestamp, not last change</li>
                    <li><strong>Default Admin:</strong> Special handling for system-created admin accounts</li>
                </ul>

                <h3>Technical Debt</h3>

                <h4>Code Quality Issues</h4>
                <ul>
                    <li><strong>Mixed Authentication:</strong> Both JWT and session-based auth used simultaneously</li>
                    <li><strong>Verbose Logging:</strong> Extensive console logging in production code</li>
                    <li><strong>Error Handling:</strong> Inconsistent error response formats across endpoints</li>
                    <li><strong>Validation:</strong> Some endpoints lack comprehensive input validation</li>
                </ul>

                <h4>Performance Considerations</h4>
                <ul>
                    <li><strong>File Size Caching:</strong> Max file size cached but may become stale</li>
                    <li><strong>Database Queries:</strong> Some queries could benefit from indexing optimization</li>
                    <li><strong>Video Streaming:</strong> No CDN integration for large-scale deployment</li>
                    <li><strong>Chunk Cleanup:</strong> Periodic cleanup runs every 30 minutes (may be too frequent)</li>
                </ul>
            </section>

            <section id="api-docs" class="section">
                <h2>API Documentation</h2>

                <h3>Authentication Endpoints</h3>

                <h4>POST /api/auth/login</h4>
                <pre><code class="language-javascript">// Request
{
  "email": "<EMAIL>",
  "password": "userpassword"
}

// Response (Success)
{
  "message": "Login successful",
  "user": {
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "fullName": "John Doe",
    "isAdmin": false,
    "language": "en"
  },
  "token": "jwt-token-here"
}

// Response (Error)
{
  "error": "Invalid email or password"
}</code></pre>

                <h4>POST /api/auth/logout</h4>
                <pre><code class="language-javascript">// Response
{
  "message": "Logout successful"
}</code></pre>

                <h3>Training Module Endpoints</h3>

                <h4>GET /api/training/modules</h4>
                <pre><code class="language-javascript">// Response
{
  "modules": [
    {
      "id": 1,
      "title": "Cybersecurity Basics",
      "description": "Introduction to cybersecurity",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000Z",
      "language_count": 2,
      "available_languages": ["en", "zh"]
    }
  ]
}</code></pre>

                <h4>POST /api/training/modules (Admin Only)</h4>
                <pre><code class="language-javascript">// Request
{
  "title": "New Training Module",
  "description": "Optional description"
}

// Response
{
  "message": "Training module created successfully",
  "module": {
    "id": 2,
    "title": "New Training Module",
    "description": "Optional description",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000Z"
  }
}</code></pre>

                <h3>Video Upload Endpoints</h3>

                <h4>Chunked Upload Flow</h4>
                <pre><code class="language-javascript">// 1. Initiate Upload
POST /api/training/modules/:id/videos/initiate
{
  "filename": "video.mp4",
  "fileSize": 104857600,
  "mimeType": "video/mp4",
  "totalChunks": 10,
  "chunkSize": 10485760,
  "languageCode": "en",
  "languageName": "English"
}

// 2. Upload Chunks
POST /api/training/modules/:id/videos/chunk
FormData: {
  uploadId: "uuid",
  chunkIndex: 0,
  chunk: File
}

// 3. Finalize Upload
POST /api/training/modules/:id/videos/finalize
{
  "uploadId": "uuid"
}</code></pre>

                <h3>User Management Endpoints</h3>

                <h4>GET /api/users (Admin Only)</h4>
                <pre><code class="language-javascript">// Response
{
  "users": [
    {
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "full_name": "John Doe",
      "is_admin": false,
      "is_active": true,
      "language": "en",
      "created_at": "2024-01-01T00:00:00.000Z",
      "last_login": "2024-01-02T00:00:00.000Z"
    }
  ]
}</code></pre>

                <h4>POST /api/users (Admin Only)</h4>
                <pre><code class="language-javascript">// Request
{
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "fullName": "Jane Smith",
  "isAdmin": false,
  "language": "en"
}

// Response
{
  "message": "User created successfully",
  "user": { /* user object */ },
  "credentials": {
    "email": "<EMAIL>",
    "password": "generated-password"
  }
}</code></pre>

                <h3>Progress Tracking Endpoints</h3>

                <h4>PUT /api/training/modules/:id/progress</h4>
                <pre><code class="language-javascript">// Request
{
  "watchTime": 1800,
  "completed": false,
  "languageCode": "en"
}

// Response
{
  "message": "Progress updated successfully",
  "progress": {
    "video_watch_time_seconds": 1800,
    "video_watched": true,
    "video_completed": false,
    "language_code": "en"
  }
}</code></pre>
            </section>

            <section id="troubleshooting" class="section">
                <h2>Troubleshooting</h2>

                <h3>Common Issues and Solutions</h3>

                <h4>Database Connection Issues</h4>
                <div class="troubleshoot-item">
                    <h5>Problem: "Database connection failed"</h5>
                    <p><strong>Symptoms:</strong> Server fails to start, database connection errors in logs</p>
                    <p><strong>Solutions:</strong></p>
                    <ol>
                        <li>Verify PostgreSQL is running: <code>sudo systemctl status postgresql</code></li>
                        <li>Check database credentials in environment variables</li>
                        <li>Ensure database and user exist:
                            <pre><code class="language-sql">-- Connect as postgres user
sudo -u postgres psql
\l  -- List databases
\du -- List users</code></pre>
                        </li>
                        <li>Test connection manually:
                            <pre><code class="language-bash">psql -h localhost -U training_user -d training_system</code></pre>
                        </li>
                    </ol>
                </div>

                <h4>File Upload Problems</h4>
                <div class="troubleshoot-item">
                    <h5>Problem: "Upload failed" or chunks not uploading</h5>
                    <p><strong>Symptoms:</strong> Upload progress stalls, chunk upload errors, file not appearing</p>
                    <p><strong>Solutions:</strong></p>
                    <ol>
                        <li>Check file size limits:
                            <pre><code class="language-bash"># Check current limit in admin panel
# Or check database
SELECT max_file_size FROM system_config WHERE id = 1;</code></pre>
                        </li>
                        <li>Verify upload directory permissions:
                            <pre><code class="language-bash">ls -la server/uploads/
# Should show write permissions for application user</code></pre>
                        </li>
                        <li>Clear orphaned chunks:
                            <pre><code class="language-bash"># Manual cleanup
rm -rf server/uploads/chunks/*</code></pre>
                        </li>
                        <li>Check disk space: <code>df -h</code></li>
                        <li>Review server logs for specific error messages</li>
                    </ol>
                </div>

                <h4>Authentication Issues</h4>
                <div class="troubleshoot-item">
                    <h5>Problem: "Invalid email or password" for valid credentials</h5>
                    <p><strong>Symptoms:</strong> Users cannot login despite correct credentials</p>
                    <p><strong>Solutions:</strong></p>
                    <ol>
                        <li>Check user account status:
                            <pre><code class="language-sql">SELECT email, is_active, password_created_at
FROM users WHERE email = '<EMAIL>';</code></pre>
                        </li>
                        <li>Verify password hasn't expired:
                            <pre><code class="language-sql">SELECT email,
  password_created_at,
  password_created_at + INTERVAL '8 days' as expires_at,
  NOW() > password_created_at + INTERVAL '8 days' as is_expired
FROM users WHERE email = '<EMAIL>';</code></pre>
                        </li>
                        <li>Reset user password if needed:
                            <pre><code class="language-bash"># Use admin panel or run script
node server/scripts/reset-user-password.js <EMAIL></code></pre>
                        </li>
                        <li>Check bcrypt performance logs for timing issues</li>
                    </ol>
                </div>

                <h4>Docker Deployment Issues</h4>
                <div class="troubleshoot-item">
                    <h5>Problem: Containers failing to start or communicate</h5>
                    <p><strong>Symptoms:</strong> Services not accessible, container exits, network errors</p>
                    <p><strong>Solutions:</strong></p>
                    <ol>
                        <li>Check container status:
                            <pre><code class="language-bash">docker-compose ps
docker-compose logs backend
docker-compose logs frontend
docker-compose logs postgres</code></pre>
                        </li>
                        <li>Verify port availability:
                            <pre><code class="language-bash">netstat -tulpn | grep -E ':(3001|3002|5432)'</code></pre>
                        </li>
                        <li>Check Docker network:
                            <pre><code class="language-bash">docker network ls
docker network inspect internal-training-system_training_network</code></pre>
                        </li>
                        <li>Rebuild containers:
                            <pre><code class="language-bash">docker-compose down
docker-compose build --no-cache
docker-compose up</code></pre>
                        </li>
                        <li>Check volume permissions:
                            <pre><code class="language-bash">ls -la server/uploads/
# Ensure Docker can write to this directory</code></pre>
                        </li>
                    </ol>
                </div>

                <h4>Email System Issues</h4>
                <div class="troubleshoot-item">
                    <h5>Problem: Emails not sending or template errors</h5>
                    <p><strong>Symptoms:</strong> User creation emails fail, template variables not replaced</p>
                    <p><strong>Solutions:</strong></p>
                    <ol>
                        <li>Test email configuration:
                            <pre><code class="language-bash"># Use admin panel test email feature
# Or run debug script
node server/scripts/debug/comprehensive-email-tests.js</code></pre>
                        </li>
                        <li>Check SMTP credentials and settings</li>
                        <li>Fix email template issues:
                            <pre><code class="language-bash"># Run template fix script
node server/scripts/debug/fix-email-template.js</code></pre>
                        </li>
                        <li>Verify email method configuration:
                            <pre><code class="language-sql">SELECT email_enabled, email_method, smtp_host
FROM email_config WHERE id = 1;</code></pre>
                        </li>
                    </ol>
                </div>

                <h3>Performance Issues</h3>

                <h4>Slow Video Loading</h4>
                <ul>
                    <li><strong>Check file sizes:</strong> Large videos may need compression</li>
                    <li><strong>Network bandwidth:</strong> Ensure adequate connection speed</li>
                    <li><strong>Server resources:</strong> Monitor CPU and memory usage</li>
                    <li><strong>Storage performance:</strong> Consider SSD for video storage</li>
                </ul>

                <h4>Database Performance</h4>
                <ul>
                    <li><strong>Query optimization:</strong> Add indexes for frequently queried columns</li>
                    <li><strong>Connection pooling:</strong> Monitor database connection usage</li>
                    <li><strong>Cleanup old data:</strong> Archive completed training records</li>
                    <li><strong>Database maintenance:</strong> Regular VACUUM and ANALYZE operations</li>
                </ul>

                <h3>Maintenance Tasks</h3>

                <h4>Regular Maintenance</h4>
                <ul>
                    <li><strong>Log rotation:</strong> Prevent log files from growing too large</li>
                    <li><strong>Chunk cleanup:</strong> Monitor and clean orphaned upload chunks</li>
                    <li><strong>Database backup:</strong> Regular PostgreSQL backups</li>
                    <li><strong>Security updates:</strong> Keep dependencies updated</li>
                    <li><strong>Certificate monitoring:</strong> Check SSL certificate expiration</li>
                </ul>

                <h4>Monitoring Commands</h4>
                <pre><code class="language-bash"># Check system resources
htop
df -h
du -sh server/uploads/*

# Monitor database
sudo -u postgres psql training_system -c "SELECT * FROM pg_stat_activity;"

# Check application logs
tail -f server/logs/application.log
docker-compose logs -f backend

# Monitor upload directory
watch -n 5 'ls -la server/uploads/chunks/ | wc -l'</code></pre>

                <div class="info-box">
                    <h4>💡 Troubleshooting Tips</h4>
                    <ul>
                        <li>Always check logs first - they contain detailed error information</li>
                        <li>Use browser developer tools to debug frontend issues</li>
                        <li>Test API endpoints directly with curl or Postman</li>
                        <li>Monitor system resources during heavy usage</li>
                        <li>Keep backups of database and uploaded files</li>
                        <li>Document any custom fixes or workarounds</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>
</body>
</html>
