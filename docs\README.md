# Internal Training System Documentation

This directory contains comprehensive documentation for the Internal Training System.

## How to Use

1. **Open the documentation**: Open `index.html` in any modern web browser
2. **Navigate**: Use the table of contents on the left to jump to specific sections
3. **Print/Save**: The documentation is optimized for printing and PDF export

## Documentation Sections

- **System Overview**: Architecture, features, and technology stack
- **Setup and Installation**: Complete setup guide for development and production
- **Account Management**: User roles, authentication, and user management
- **Module Management**: Training modules, videos, and progress tracking
- **Video Upload System**: Chunked upload implementation and file handling
- **Quiz and Certificate System**: Assessment and certification features
- **System Configuration**: Email settings, file limits, and system parameters
- **System Architecture**: Technical architecture and database design
- **Known Issues and Quirks**: System limitations and workarounds
- **API Documentation**: Key endpoints and usage examples
- **Troubleshooting**: Common issues and solutions

## Features

- **Self-contained**: No external dependencies except for syntax highlighting
- **Responsive**: Works on desktop, tablet, and mobile devices
- **Print-friendly**: Optimized for printing and PDF generation
- **Searchable**: Use browser's find function (Ctrl+F) to search content
- **Professional styling**: Clean, modern design with syntax highlighting

## Updating Documentation

To update the documentation:

1. Edit `index.html` for content changes
2. Edit `styles.css` for styling changes
3. Add images to the `assets/` directory if needed
4. Test in multiple browsers to ensure compatibility

## Browser Compatibility

The documentation works in all modern browsers:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## File Structure

```
docs/
├── index.html          # Main documentation file
├── styles.css          # Styling and layout
├── assets/            # Images and diagrams (placeholder)
└── README.md          # This file
```

## Notes

- The documentation includes code examples with syntax highlighting
- All system quirks and known issues are documented
- Troubleshooting section covers common problems and solutions
- API documentation includes request/response examples
- Setup instructions cover both development and production deployment
