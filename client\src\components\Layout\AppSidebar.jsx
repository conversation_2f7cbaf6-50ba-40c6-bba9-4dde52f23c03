import React, { useState } from 'react'
import { Layout, <PERSON>u, But<PERSON> } from 'antd'
import {
  DashboardOutlined,
  BookOutlined,
  UserOutlined,
  SettingOutlined,
  TeamOutlined,
  BarChartOutlined,
  MailOutlined,
  FileTextOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useAuth } from '../../contexts/AuthContext'
import { useTutorial } from '../../contexts/TutorialContext'

const { Sider } = Layout

// Navigation menu configuration
const getMenuItems = (user, t) => {
  const baseItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: t('navigation.dashboard'),
    },
    {
      key: '/training-modules',
      icon: <BookOutlined />,
      label: t('navigation.trainingModules'),
    },
    {
      key: '/profile',
      icon: <UserOutlined />,
      label: t('navigation.profile'),
    },
  ]

  // Add admin section if user is admin
  if (user?.isAdmin) {
    baseItems.push({
      key: 'admin',
      icon: <SettingOutlined />,
      label: t('navigation.administration'),
      children: [
        {
          key: '/admin',
          icon: <DashboardOutlined />,
          label: t('navigation.adminDashboard'),
        },
        {
          key: '/admin/users',
          icon: <TeamOutlined />,
          label: t('navigation.userManagement'),
        },
        {
          key: '/admin/modules',
          icon: <BookOutlined />,
          label: t('navigation.moduleManagement'),
        },
        {
          key: '/admin/reports',
          icon: <BarChartOutlined />,
          label: t('navigation.reports'),
        },
        {
          key: '/admin/quiz-results',
          icon: <FileTextOutlined />,
          label: t('navigation.quizResults'),
        },
        {
          key: '/admin/email-config',
          icon: <MailOutlined />,
          label: t('navigation.emailConfiguration'),
        },
        {
          key: '/admin/system-config',
          icon: <SettingOutlined />,
          label: t('navigation.systemConfiguration'),
        },
      ],
    })
  }

  return baseItems
}

function AppSidebar({ onCollapse, collapsed = false }) {
  const { user } = useAuth()
  const { t } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()
  const { startTutorial } = useTutorial()
  const [openKeys, setOpenKeys] = useState([])

  // Notify parent of collapse state changes
  React.useEffect(() => {
    onCollapse?.(collapsed)
  }, [collapsed, onCollapse])

  // Update open keys when location changes
  React.useEffect(() => {
    setOpenKeys(getOpenKeys())
  }, [location.pathname])

  const handleMenuClick = ({ key }) => {
    navigate(key)
  }

  const handleOpenChange = (keys) => {
    setOpenKeys(keys)
  }

  // Get current selected keys based on pathname
  const getSelectedKeys = () => {
    const path = location.pathname
    if (path.startsWith('/admin')) {
      if (path.includes('/users')) return ['/admin/users']
      if (path.includes('/modules')) return ['/admin/modules']
      if (path.includes('/reports')) return ['/admin/reports']
      if (path.includes('/quiz-results')) return ['/admin/quiz-results']
      if (path.includes('/email-config')) return ['/admin/email-config']
      if (path.includes('/system-config')) return ['/admin/system-config']
      return ['/admin']
    }
    if (path.startsWith('/training-modules')) return ['/training-modules']
    if (path.startsWith('/profile')) return ['/profile']
    return ['/dashboard']
  }

  // Get open keys for submenus
  const getOpenKeys = () => {
    return location.pathname.startsWith('/admin') ? ['admin'] : []
  }

  const menuItems = getMenuItems(user, t)

  return (
    <Sider
      collapsed={collapsed}
      width={250}
      style={{
        overflow: 'hidden',
        height: 'calc(100vh - 64px)',
        position: 'fixed',
        left: 0,
        top: 64,
        zIndex: 100,
        backgroundColor: '#595959'
      }}
      breakpoint="lg"
      collapsedWidth="80"
      trigger={null}
    >
      <div style={{
        height: 'calc(100vh - 64px)',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Menu
          theme="dark"
          mode="inline"
          items={menuItems}
          selectedKeys={getSelectedKeys()}
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          onClick={handleMenuClick}
          style={{
            flex: 1,
            borderRight: 0,
            backgroundColor: '#595959',
            overflow: 'auto',
            minHeight: 0
          }}
        />

        {/* View Tutorial Button - only show for non-admin users */}
        {user && !user.isAdmin && (
          <div style={{
            padding: collapsed ? '8px' : '16px',
            backgroundColor: '#595959',
            borderTop: '1px solid #434343',
            flexShrink: 0,
            minHeight: collapsed ? '48px' : '72px',
            display: 'flex',
            alignItems: 'center'
          }}>
            <Button
              type="default"
              size="small"
              icon={<QuestionCircleOutlined />}
              onClick={startTutorial}
              style={{
                width: '100%',
                backgroundColor: '#434343',
                borderColor: '#434343',
                color: '#fff',
                height: collapsed ? '32px' : '40px'
              }}
              title={collapsed ? t('navigation.viewTutorial') : undefined}
            >
              {!collapsed && t('navigation.viewTutorial')}
            </Button>
          </div>
        )}
      </div>
    </Sider>
  )
}

export default AppSidebar
