#!/usr/bin/env node

const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');
const bcrypt = require('bcryptjs');

// Load environment variables from root .env file
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'training_system',
  user: process.env.DB_USER || 'training_user',
  password: process.env.DB_PASSWORD || 'training_password',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

const pool = new Pool(dbConfig);

// ANSI color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`[${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

async function confirmAction() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    log('\n' + '='.repeat(60), 'red');
    log('🚨 DATA WIPE WARNING 🚨', 'red');
    log('='.repeat(60), 'red');
    log('This script will:', 'yellow');
    log('• DROP ALL DATA from the database', 'yellow');
    log('• DELETE ALL uploaded video files', 'yellow');
    log('• RECREATE the database schema with admin account', 'yellow');
    log('• This action is IRREVERSIBLE!', 'red');
    log('='.repeat(60), 'red');
    
    rl.question('\nAre you sure you want to continue? Type "YES" to confirm: ', (answer) => {
      rl.close();
      resolve(answer === 'YES');
    });
  });
}

async function dropAllTables() {
  logStep('1', 'Dropping all database tables...');
  
  const client = await pool.connect();
  try {
    // Get all table names
    const tablesResult = await client.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public'
    `);
    
    if (tablesResult.rows.length === 0) {
      logWarning('No tables found to drop');
      return;
    }

    // Drop all tables with CASCADE to handle foreign key constraints
    for (const row of tablesResult.rows) {
      const tableName = row.tablename;
      await client.query(`DROP TABLE IF EXISTS "${tableName}" CASCADE`);
      log(`  Dropped table: ${tableName}`, 'magenta');
    }

    // Drop all functions and triggers
    await client.query(`
      DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
    `);
    
    logSuccess('All database tables dropped successfully');
  } catch (error) {
    logError(`Failed to drop tables: ${error.message}`);
    throw error;
  } finally {
    client.release();
  }
}

async function deleteVideoFiles() {
  logStep('2', 'Deleting all uploaded video files...');
  
  const uploadsDir = path.join(__dirname, '../uploads/videos');
  
  try {
    // Check if uploads directory exists
    try {
      await fs.access(uploadsDir);
    } catch (error) {
      logWarning('Uploads directory does not exist, skipping file deletion');
      return;
    }

    // Read all files in the uploads directory
    const files = await fs.readdir(uploadsDir);
    
    if (files.length === 0) {
      logWarning('No video files found to delete');
      return;
    }

    // Delete each file
    let deletedCount = 0;
    for (const file of files) {
      const filePath = path.join(uploadsDir, file);
      const stats = await fs.stat(filePath);
      
      if (stats.isFile()) {
        await fs.unlink(filePath);
        log(`  Deleted: ${file}`, 'magenta');
        deletedCount++;
      }
    }
    
    logSuccess(`Deleted ${deletedCount} video files`);
  } catch (error) {
    logError(`Failed to delete video files: ${error.message}`);
    throw error;
  }
}

async function recreateDatabase() {
  logStep('3', 'Recreating database schema...');
  
  const client = await pool.connect();
  try {
    // Read and execute initialization SQL
    const sqlPath = path.join(__dirname, '../database/init.sql');
    const sqlContent = await fs.readFile(sqlPath, 'utf8');
    
    await client.query(sqlContent);
    logSuccess('Database schema recreated successfully');
  } catch (error) {
    logError(`Failed to recreate database: ${error.message}`);
    throw error;
  } finally {
    client.release();
  }
}

async function verifyAdminAccount() {
  logStep('4', 'Verifying test admin account...');

  const client = await pool.connect();
  try {
    // Check if admin user exists
    const result = await client.query(`
      SELECT email, first_name, last_name, is_admin, is_active
      FROM users
      WHERE email = $1
    `, ['<EMAIL>']);

    if (result.rows.length === 0) {
      throw new Error('Admin account was not created by database initialization');
    }

    const admin = result.rows[0];
    if (!admin.is_admin || !admin.is_active) {
      throw new Error('Admin account exists but is not properly configured');
    }

    logSuccess('Test admin account verified');
    log('  Email: <EMAIL>', 'green');
    log('  Password: admin123', 'green');
    log(`  Name: ${admin.first_name} ${admin.last_name}`, 'green');
  } catch (error) {
    logError(`Failed to verify admin account: ${error.message}`);
    throw error;
  } finally {
    client.release();
  }
}

async function verifyConnection() {
  logStep('0', 'Verifying database connection...');
  
  try {
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    logSuccess('Database connection verified');
  } catch (error) {
    logError(`Database connection failed: ${error.message}`);
    throw error;
  }
}

async function main() {
  try {
    log('\n🧹 Training System Data Wipe Script', 'bright');
    log('=====================================', 'bright');
    
    // Confirm the action
    const confirmed = await confirmAction();
    if (!confirmed) {
      log('\nOperation cancelled by user.', 'yellow');
      process.exit(0);
    }
    
    log('\nStarting data wipe process...', 'bright');
    
    // Step 0: Verify connection
    await verifyConnection();
    
    // Step 1: Drop all tables
    await dropAllTables();
    
    // Step 2: Delete video files
    await deleteVideoFiles();
    
    // Step 3: Recreate database schema
    await recreateDatabase();
    
    // Step 4: Verify test admin account
    await verifyAdminAccount();
    
    log('\n' + '='.repeat(50), 'green');
    log('🎉 DATA WIPE COMPLETED SUCCESSFULLY! 🎉', 'green');
    log('='.repeat(50), 'green');
    log('\nYou can now:', 'white');
    log('• <NAME_EMAIL> / admin123', 'green');
    log('• Start fresh with a clean database', 'green');
    log('• Upload new training modules and videos', 'green');
    
  } catch (error) {
    log('\n' + '='.repeat(40), 'red');
    log('💥 DATA WIPE FAILED!', 'red');
    log('='.repeat(40), 'red');
    logError(`Error: ${error.message}`);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  log('\n\nOperation cancelled by user.', 'yellow');
  await pool.end();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  log('\n\nOperation terminated.', 'yellow');
  await pool.end();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
