# =============================================================================
# AZURE CONTAINERIZED DEPLOYMENT SCRIPT - OPTIMIZED NETWORKING
# =============================================================================

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,

    [Parameter(Mandatory=$true)]
    [string]$Location = "Germany West Central",

    [Parameter(Mandatory=$true)]
    [string]$AppName,

    [Parameter(Mandatory=$false)]
    [string]$SubscriptionId,

    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild,

    [Parameter(Mandatory=$false)]
    [switch]$UseContainerInstances,

    [Parameter(Mandatory=$false)]
    [string]$ContainerRegistry = "",

    [Parameter(Mandatory=$false)]
    [switch]$SkipDatabase,

    [Parameter(Mandatory=$false)]
    [switch]$SkipStorage
)

# Create Azure-friendly resource names from the app name
$SafeAppName = $AppName -replace '[^a-zA-Z0-9-]', '-' -replace '--+', '-' -replace '^-|-$', ''
$SafeAppName = $SafeAppName.ToLower()
if ($SafeAppName.Length -gt 20) {
    $SafeAppName = $SafeAppName.Substring(0, 20) -replace '-$', ''
}

# For storage accounts (no hyphens, max 24 chars, lowercase only)
$StorageAccountName = ($AppName -replace '[^a-zA-Z0-9]', '').ToLower()
if ($StorageAccountName.Length -gt 24) {
    $StorageAccountName = $StorageAccountName.Substring(0, 24)
}

# Update container registry name to use safe name
if ($ContainerRegistry -eq "$AppName-registry") {
    $ContainerRegistry = "$SafeAppName-registry"
}

$ErrorActionPreference = "Stop"

# Create Azure-friendly resource names with shorter, more reliable naming
# Container Registry: no dashes, lowercase, alphanumeric only, 5-50 chars
# Use shorter names to avoid Azure naming issues
$SafeRegistryName = "tltraining" + (Get-Random -Minimum 100 -Maximum 999)

# Set container registry name if not provided
if ([string]::IsNullOrEmpty($ContainerRegistry)) {
    $ContainerRegistry = $SafeRegistryName
}

# Storage Account: no dashes, lowercase, alphanumeric only, 3-24 chars
$SafeStorageName = "tlstorage" + (Get-Random -Minimum 100 -Maximum 999)

Write-Host "Starting Azure Containerized Deployment..." -ForegroundColor Green
Write-Host "Configuration:" -ForegroundColor Blue
Write-Host "   Resource Group: $ResourceGroupName" -ForegroundColor White
Write-Host "   Location: $Location" -ForegroundColor White
Write-Host "   App Name: $AppName" -ForegroundColor White
Write-Host "   Container Registry: $ContainerRegistry" -ForegroundColor White

# Set subscription if provided
if ($SubscriptionId) {
    Write-Host "Setting Azure subscription..." -ForegroundColor Blue
    az account set --subscription $SubscriptionId
}

# =============================================================================
# STEP 1: CREATE AZURE RESOURCES
# =============================================================================
Write-Host "Creating Azure resources..." -ForegroundColor Blue

# Create resource group
az group create --name $ResourceGroupName --location $Location

# Create Container Registry
Write-Host "Creating Azure Container Registry..." -ForegroundColor Blue
$acrResult = az acr create --resource-group $ResourceGroupName --name $ContainerRegistry --sku Basic --admin-enabled true 2>&1

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to create Container Registry. Error details:" -ForegroundColor Red
    Write-Host $acrResult -ForegroundColor Red
    Write-Host "Please check:" -ForegroundColor Yellow
    Write-Host "1. Container Registry name '$ContainerRegistry' is available" -ForegroundColor Yellow
    Write-Host "2. You have permissions to create Container Registry in this subscription" -ForegroundColor Yellow
    Write-Host "3. The Microsoft.ContainerRegistry provider is registered" -ForegroundColor Yellow
    exit 1
}

# Get ACR login server
$acrLoginServer = az acr show --name $ContainerRegistry --query loginServer --output tsv
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to get Container Registry details" -ForegroundColor Red
    exit 1
}

$acrUsername = az acr credential show --name $ContainerRegistry --query username --output tsv
$acrPassword = az acr credential show --name $ContainerRegistry --query passwords[0].value --output tsv

Write-Host "Container Registry created: $acrLoginServer" -ForegroundColor Green

# Create Virtual Network for secure networking
Write-Host "Creating Virtual Network..." -ForegroundColor Blue
az network vnet create `
    --resource-group $ResourceGroupName `
    --name "$AppName-vnet" `
    --address-prefix 10.0.0.0/16 `
    --subnet-name "$AppName-subnet" `
    --subnet-prefix ********/24

# Create Network Security Group
az network nsg create `
    --resource-group $ResourceGroupName `
    --name "$AppName-nsg"

# Add security rules
az network nsg rule create `
    --resource-group $ResourceGroupName `
    --nsg-name "$AppName-nsg" `
    --name "AllowHTTP" `
    --protocol tcp `
    --priority 1000 `
    --destination-port-range 80 `
    --access allow

az network nsg rule create `
    --resource-group $ResourceGroupName `
    --nsg-name "$AppName-nsg" `
    --name "AllowHTTPS" `
    --protocol tcp `
    --priority 1001 `
    --destination-port-range 443 `
    --access allow

# Associate NSG with subnet
az network vnet subnet update `
    --resource-group $ResourceGroupName `
    --vnet-name "$AppName-vnet" `
    --name "$AppName-subnet" `
    --network-security-group "$AppName-nsg"

if (-not $SkipDatabase) {
    # Create Azure Database for PostgreSQL
    Write-Host "Creating Azure Database for PostgreSQL..." -ForegroundColor Blue
    $dbResult = az postgres flexible-server create `
        --resource-group $ResourceGroupName `
        --name "$AppName-postgres" `
        --location $Location `
        --admin-user training_user `
        --admin-password "SecurePass2024!" `
        --sku-name Standard_B1ms `
        --tier Burstable `
        --storage-size 32 `
        --version 15 2>&1

    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to create PostgreSQL database. Error details:" -ForegroundColor Red
        Write-Host $dbResult -ForegroundColor Red
        Write-Host "Please check:" -ForegroundColor Yellow
        Write-Host "1. The region '$Location' supports PostgreSQL Flexible Server" -ForegroundColor Yellow
        Write-Host "2. Try using 'West Europe' or 'North Europe' instead" -ForegroundColor Yellow
        Write-Host "3. The Microsoft.DBforPostgreSQL provider is registered" -ForegroundColor Yellow
        exit 1
    }

    # Create database
    az postgres flexible-server db create `
        --resource-group $ResourceGroupName `
        --server-name "$AppName-postgres" `
        --database-name training_system
} else {
    Write-Host "Skipping PostgreSQL database creation (will be created manually)..." -ForegroundColor Yellow
}

if (-not $SkipStorage) {
    # Create Storage Account for file uploads
    Write-Host "Creating Storage Account..." -ForegroundColor Blue
    $storageResult = az storage account create `
        --name $SafeStorageName `
        --resource-group $ResourceGroupName `
        --location $Location `
        --sku Standard_LRS 2>&1

    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to create Storage Account. Error details:" -ForegroundColor Red
        Write-Host $storageResult -ForegroundColor Red
        Write-Host "Please check:" -ForegroundColor Yellow
        Write-Host "1. Storage account name '$SafeStorageName' is available globally" -ForegroundColor Yellow
        Write-Host "2. The Microsoft.Storage provider is registered" -ForegroundColor Yellow
        exit 1
    }

    # Create blob container
    $storageKey = az storage account keys list --resource-group $ResourceGroupName --account-name $SafeStorageName --query [0].value --output tsv
    az storage container create `
        --name "training-uploads" `
        --account-name $SafeStorageName `
        --account-key $storageKey
} else {
    Write-Host "Skipping Storage Account creation (will be created manually)..." -ForegroundColor Yellow
    # Set a placeholder storage name for environment variables
    $SafeStorageName = "MANUAL_STORAGE_ACCOUNT"
    $storageKey = "MANUAL_STORAGE_KEY"
}

# =============================================================================
# STEP 2: BUILD AND PUSH CONTAINER IMAGES
# =============================================================================
if (-not $SkipBuild) {
    Write-Host "Building and pushing container images..." -ForegroundColor Blue
    
    # Login to ACR
    az acr login --name $ContainerRegistry
    
    # Build and push backend image
    Write-Host "Building backend image..." -ForegroundColor Yellow
    docker build -f server/Dockerfile.azure -t "$acrLoginServer/$AppName-backend:latest" .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to build backend image" -ForegroundColor Red
        exit 1
    }
    docker push "$acrLoginServer/$AppName-backend:latest"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to push backend image" -ForegroundColor Red
        exit 1
    }

    # Build and push frontend image
    Write-Host "Building frontend image..." -ForegroundColor Yellow
    docker build -f client/Dockerfile.azure --build-arg REACT_APP_API_URL="http://backend:8080/api" -t "$acrLoginServer/$AppName-frontend:latest" .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to build frontend image" -ForegroundColor Red
        exit 1
    }
    docker push "$acrLoginServer/$AppName-frontend:latest"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to push frontend image" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Container images built and pushed" -ForegroundColor Green
}

# =============================================================================
# STEP 3: DEPLOY CONTAINERS
# =============================================================================
if ($UseContainerInstances) {
    Write-Host "Deploying to Azure Container Instances..." -ForegroundColor Blue
    
    # Create container group with optimized networking
    az container create `
        --resource-group $ResourceGroupName `
        --name "$AppName-containers" `
        --image "$acrLoginServer/$AppName-frontend:latest" `
        --registry-login-server $acrLoginServer `
        --registry-username $acrUsername `
        --registry-password $acrPassword `
        --dns-name-label $AppName `
        --ports 80 `
        --cpu 2 `
        --memory 4 `
        --environment-variables `
            NODE_ENV=production `
            DEPLOYMENT_TYPE=azure-container-instances `
            DB_HOST="$AppName-postgres.postgres.database.azure.com" `
            DB_NAME=training_system `
            DB_USER="training_user" `
            DB_PASSWORD="SecurePass2024!" `
            AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=$SafeStorageName;AccountKey=$storageKey;EndpointSuffix=core.windows.net"
    
    $containerFqdn = az container show --resource-group $ResourceGroupName --name "$AppName-containers" --query ipAddress.fqdn --output tsv
    Write-Host "Container Instances deployed: http://$containerFqdn" -ForegroundColor Green

} else {
    Write-Host "Deploying to Azure App Service..." -ForegroundColor Blue
    
    # Create App Service Plan
    az appservice plan create `
        --name "$AppName-plan" `
        --resource-group $ResourceGroupName `
        --sku B1 `
        --is-linux
    
    # Create Web Apps
    az webapp create `
        --resource-group $ResourceGroupName `
        --plan "$AppName-plan" `
        --name "$AppName-frontend" `
        --deployment-container-image-name "$acrLoginServer/$AppName-frontend:latest"
    
    az webapp create `
        --resource-group $ResourceGroupName `
        --plan "$AppName-plan" `
        --name "$AppName-backend" `
        --deployment-container-image-name "$acrLoginServer/$AppName-backend:latest"
    
    # Configure container registry credentials
    az webapp config container set `
        --name "$AppName-frontend" `
        --resource-group $ResourceGroupName `
        --docker-custom-image-name "$acrLoginServer/$AppName-frontend:latest" `
        --docker-registry-server-url "https://$acrLoginServer" `
        --docker-registry-server-user $acrUsername `
        --docker-registry-server-password $acrPassword
    
    az webapp config container set `
        --name "$AppName-backend" `
        --resource-group $ResourceGroupName `
        --docker-custom-image-name "$acrLoginServer/$AppName-backend:latest" `
        --docker-registry-server-url "https://$acrLoginServer" `
        --docker-registry-server-user $acrUsername `
        --docker-registry-server-password $acrPassword
    
    Write-Host "App Services deployed" -ForegroundColor Green
    Write-Host "   Frontend: https://$AppName-frontend.azurewebsites.net" -ForegroundColor Cyan
    Write-Host "   Backend: https://$AppName-backend.azurewebsites.net" -ForegroundColor Cyan
}

# =============================================================================
# STEP 4: FINAL CONFIGURATION
# =============================================================================
Write-Host "Applying final configuration..." -ForegroundColor Blue

# Output important information
Write-Host ""
Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Resource Information:" -ForegroundColor Cyan
Write-Host "   Resource Group: $ResourceGroupName" -ForegroundColor White
Write-Host "   Container Registry: $acrLoginServer" -ForegroundColor White
Write-Host "   Database: $AppName-postgres.postgres.database.azure.com" -ForegroundColor White
Write-Host "   Storage Account: $SafeStorageName" -ForegroundColor White
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Configure your domain name and SSL certificate" -ForegroundColor White
Write-Host "   2. Set up Application Insights for monitoring" -ForegroundColor White
Write-Host "   3. Configure Azure CDN for better performance" -ForegroundColor White
Write-Host "   4. Set up automated backups" -ForegroundColor White
Write-Host "   5. Configure auto-scaling rules" -ForegroundColor White
