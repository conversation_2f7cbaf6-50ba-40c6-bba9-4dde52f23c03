/**
 * Optimized React Query hooks to reduce code duplication
 */

import { useQuery, useMutation, useQueryClient } from 'react-query'
import { message } from 'antd'
import { handleAPIError } from '../services/api'
import { createMutationOptions } from '../utils/performance'

/**
 * Optimized query hook with common patterns
 * @param {string|Array} queryKey - Query key
 * @param {Function} queryFn - Query function
 * @param {Object} options - Additional options
 * @returns {Object} Query result
 */
export const useOptimizedQuery = (queryKey, queryFn, options = {}) => {
  return useQuery(queryKey, queryFn, {
    retry: 1,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options
  })
}

/**
 * Optimized mutation hook with common error handling
 * @param {Function} mutationFn - Mutation function
 * @param {Object} options - Mutation options
 * @returns {Object} Mutation result
 */
export const useOptimizedMutation = (mutationFn, options = {}) => {
  const queryClient = useQueryClient()
  
  const defaultOptions = createMutationOptions({
    invalidateQueries: options.invalidateQueries ? 
      () => {
        if (Array.isArray(options.invalidateQueries)) {
          options.invalidateQueries.forEach(key => queryClient.invalidateQueries(key))
        } else {
          queryClient.invalidateQueries(options.invalidateQueries)
        }
      } : null,
    successMessage: options.successMessage,
    errorMessage: options.errorMessage,
    onSuccess: options.onSuccess,
    onError: options.onError
  })

  return useMutation(mutationFn, {
    ...defaultOptions,
    ...options
  })
}

/**
 * Hook for training module queries
 * @param {string} moduleId - Module ID
 * @returns {Object} Module query result
 */
export const useModuleQuery = (moduleId) => {
  return useOptimizedQuery(
    ['training-module', moduleId],
    () => import('../services/api').then(({ trainingAPI }) => trainingAPI.getModule(moduleId)),
    {
      select: (response) => response.data,
      enabled: !!moduleId
    }
  )
}

/**
 * Hook for training modules list query
 * @returns {Object} Modules query result
 */
export const useModulesQuery = () => {
  return useOptimizedQuery(
    'training-modules',
    () => import('../services/api').then(({ trainingAPI }) => trainingAPI.getModules()),
    {
      select: (response) => response.data.modules
    }
  )
}

/**
 * Hook for user progress query
 * @param {string} moduleId - Module ID
 * @param {string} userEmail - User email
 * @returns {Object} Progress query result
 */
export const useProgressQuery = (moduleId, userEmail) => {
  return useOptimizedQuery(
    ['training-progress', moduleId, userEmail],
    () => import('../services/api').then(({ trainingAPI }) => trainingAPI.getProgress(moduleId)),
    {
      select: (response) => response.data.progress,
      enabled: !!moduleId && !!userEmail
    }
  )
}

/**
 * Hook for progress update mutation
 * @param {string} moduleId - Module ID
 * @param {string} userEmail - User email
 * @returns {Object} Mutation result
 */
export const useProgressMutation = (moduleId, userEmail) => {
  return useOptimizedMutation(
    (progressData) => import('../services/api').then(({ trainingAPI }) => 
      trainingAPI.updateProgress(moduleId, progressData)
    ),
    {
      invalidateQueries: [
        ['training-progress', moduleId, userEmail],
        ['user-profile', userEmail]
      ],
      onSuccess: (_, variables) => {
        if (variables?.showMessage) {
          message.success('Progress saved successfully')
        }
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`Failed to save progress: ${errorInfo.message}`)
      }
    }
  )
}

/**
 * Hook for quiz queries
 * @param {string} moduleId - Module ID
 * @param {string} languageCode - Language code
 * @returns {Object} Quiz query result
 */
export const useQuizQuery = (moduleId, languageCode) => {
  return useOptimizedQuery(
    ['quiz', moduleId, languageCode],
    () => import('../services/api').then(({ quizAPI }) => 
      quizAPI.getQuiz(moduleId, languageCode)
    ),
    {
      select: (response) => response.data,
      enabled: !!moduleId && !!languageCode
    }
  )
}

/**
 * Hook for quiz submission mutation
 * @param {string} moduleId - Module ID
 * @param {string} languageCode - Language code
 * @returns {Object} Mutation result
 */
export const useQuizSubmissionMutation = (moduleId, languageCode) => {
  return useOptimizedMutation(
    (answers) => import('../services/api').then(({ quizAPI }) => 
      quizAPI.submitQuiz(moduleId, languageCode, answers)
    ),
    {
      successMessage: 'Quiz submitted successfully!',
      errorMessage: 'Failed to submit quiz'
    }
  )
}

/**
 * Hook for module creation mutation
 * @returns {Object} Mutation result
 */
export const useCreateModuleMutation = () => {
  return useOptimizedMutation(
    (moduleData) => import('../services/api').then(({ trainingAPI }) => 
      trainingAPI.createModule(moduleData)
    ),
    {
      invalidateQueries: 'training-modules',
      successMessage: 'Module created successfully!',
      errorMessage: 'Failed to create module'
    }
  )
}

/**
 * Hook for module deletion mutation
 * @returns {Object} Mutation result
 */
export const useDeleteModuleMutation = () => {
  return useOptimizedMutation(
    (moduleId) => import('../services/api').then(({ trainingAPI }) => 
      trainingAPI.deleteModule(moduleId)
    ),
    {
      invalidateQueries: 'training-modules',
      successMessage: 'Module deleted successfully!',
      errorMessage: 'Failed to delete module'
    }
  )
}

/**
 * Hook for video deletion mutation
 * @param {string} moduleId - Module ID
 * @returns {Object} Mutation result
 */
export const useDeleteVideoMutation = (moduleId) => {
  return useOptimizedMutation(
    (videoId) => import('../services/api').then(({ trainingAPI }) => 
      trainingAPI.deleteVideo(videoId)
    ),
    {
      invalidateQueries: [
        ['module-details', moduleId],
        'training-modules'
      ],
      successMessage: 'Video deleted successfully!',
      errorMessage: 'Failed to delete video'
    }
  )
}

/**
 * Hook for quiz creation mutation
 * @param {string} moduleId - Module ID
 * @returns {Object} Mutation result
 */
export const useCreateQuizMutation = (moduleId) => {
  return useOptimizedMutation(
    ({ videoId, quizData }) => import('../services/api').then(({ quizAPI }) => 
      quizAPI.createQuiz(Number(videoId), quizData)
    ),
    {
      invalidateQueries: [
        ['module-details', moduleId],
        'training-modules'
      ],
      successMessage: 'Quiz created successfully!',
      errorMessage: 'Failed to create quiz'
    }
  )
}

/**
 * Hook for quiz update mutation
 * @param {string} moduleId - Module ID
 * @returns {Object} Mutation result
 */
export const useUpdateQuizMutation = (moduleId) => {
  return useOptimizedMutation(
    ({ videoId, quizData }) => import('../services/api').then(({ quizAPI }) => 
      quizAPI.updateQuiz(Number(videoId), quizData)
    ),
    {
      invalidateQueries: [
        ['module-details', moduleId],
        'training-modules'
      ],
      successMessage: 'Quiz updated successfully!',
      errorMessage: 'Failed to update quiz'
    }
  )
}

/**
 * Hook for quiz deletion mutation
 * @param {string} moduleId - Module ID
 * @returns {Object} Mutation result
 */
export const useDeleteQuizMutation = (moduleId) => {
  return useOptimizedMutation(
    (videoId) => import('../services/api').then(({ quizAPI }) => 
      quizAPI.deleteQuiz(Number(videoId))
    ),
    {
      invalidateQueries: [
        ['module-details', moduleId],
        'training-modules'
      ],
      successMessage: 'Quiz deleted successfully!',
      errorMessage: 'Failed to delete quiz'
    }
  )
}

/**
 * Hook for user management queries
 * @returns {Object} Users query result
 */
export const useUsersQuery = () => {
  return useOptimizedQuery(
    'users',
    () => import('../services/api').then(({ usersAPI }) => usersAPI.getAll()),
    {
      select: (response) => response.data
    }
  )
}

/**
 * Hook for bulk user creation mutation
 * @returns {Object} Mutation result
 */
export const useBulkCreateUsersMutation = () => {
  return useOptimizedMutation(
    (emails) => import('../services/api').then(({ usersAPI }) => 
      usersAPI.bulkCreate(emails)
    ),
    {
      invalidateQueries: 'users',
      successMessage: 'Users created successfully!',
      errorMessage: 'Failed to create users'
    }
  )
}
