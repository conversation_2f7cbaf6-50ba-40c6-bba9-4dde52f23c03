const { query } = require('../../database/init');

class EmailMigrationService {
  // Run database migrations for email configuration
  async runMigrations() {
    try {
      console.log('Running email configuration migrations...');

      // Check if Reply-To address column exists, add if not
      const replyToCheck = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'email_config'
        AND column_name = 'email_reply_to_address'
      `);

      if (replyToCheck.rows.length === 0) {
        console.log('Adding email_reply_to_address column...');
        await query('ALTER TABLE email_config ADD COLUMN email_reply_to_address VARCHAR(255)');
        console.log('✅ Added email_reply_to_address column');
      } else {
        console.log('✅ email_reply_to_address column already exists');
      }

      // Check if CC addresses column exists, add if not
      const ccCheck = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'email_config'
        AND column_name = 'email_cc_addresses'
      `);

      if (ccCheck.rows.length === 0) {
        console.log('Adding email_cc_addresses column...');
        await query('ALTER TABLE email_config ADD COLUMN email_cc_addresses TEXT');
        console.log('✅ Added email_cc_addresses column');
      } else {
        console.log('✅ email_cc_addresses column already exists');
      }

      // Check if English template columns exist, add if not
      const subjectEnCheck = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'email_config'
        AND column_name = 'email_template_subject_en'
      `);

      if (subjectEnCheck.rows.length === 0) {
        console.log('Adding email_template_subject_en column...');
        await query('ALTER TABLE email_config ADD COLUMN email_template_subject_en VARCHAR(500) DEFAULT \'Your TradeLink Training System Login Credentials\'');
        console.log('✅ Added email_template_subject_en column');
      } else {
        console.log('✅ email_template_subject_en column already exists');
      }

      const bodyEnCheck = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'email_config'
        AND column_name = 'email_template_body_en'
      `);

      if (bodyEnCheck.rows.length === 0) {
        console.log('Adding email_template_body_en column...');
        const defaultTemplateEn = this.getDefaultEmailTemplate('en');
        await query('ALTER TABLE email_config ADD COLUMN email_template_body_en TEXT DEFAULT $1', [defaultTemplateEn]);
        console.log('✅ Added email_template_body_en column');
      } else {
        console.log('✅ email_template_body_en column already exists');
      }

      // Check if Chinese template columns exist, add if not
      const subjectZhCheck = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'email_config'
        AND column_name = 'email_template_subject_zh'
      `);

      if (subjectZhCheck.rows.length === 0) {
        console.log('Adding email_template_subject_zh column...');
        await query('ALTER TABLE email_config ADD COLUMN email_template_subject_zh VARCHAR(500) DEFAULT \'您的TradeLink培训系统登录凭据\'');
        console.log('✅ Added email_template_subject_zh column');
      } else {
        console.log('✅ email_template_subject_zh column already exists');
      }

      const bodyZhCheck = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'email_config'
        AND column_name = 'email_template_body_zh'
      `);

      if (bodyZhCheck.rows.length === 0) {
        console.log('Adding email_template_body_zh column...');
        const defaultTemplateZh = this.getDefaultEmailTemplate('zh');
        await query('ALTER TABLE email_config ADD COLUMN email_template_body_zh TEXT DEFAULT $1', [defaultTemplateZh]);
        console.log('✅ Added email_template_body_zh column');
      } else {
        console.log('✅ email_template_body_zh column already exists');
      }
    } catch (error) {
      console.error('Error running email migrations:', error);
    }
  }

  // Get default email template (duplicated from EmailConfigService for migration purposes)
  getDefaultEmailTemplate(language = 'en') {
    if (language === 'zh') {
      return `亲爱的 {{fullName}}，

您的TradeLink内部培训系统账户已创建。

登录凭据：
邮箱：{{email}}
密码：{{password}}

重要安全信息：
• 您的密码将在8天后过期
• 请及时登录开始您的培训
• 请保护您的凭据安全，不要与他人分享
• 如需帮助请联系您的管理员

如果您有任何问题或需要帮助，请联系您的系统管理员。

此致，
TradeLink培训系统管理员

---
这是一封自动发送的邮件，请勿回复。`;
    }

    // Default English template
    return `Dear {{fullName}},

Your account has been created for the TradeLink Internal Training System.

Login Credentials:
Email: {{email}}
Password: {{password}}

Important Security Information:
• Your password will expire in 8 days
• Please log in promptly to begin your training
• Keep your credentials secure and do not share them
• Contact your administrator if you need assistance

If you have any questions or need assistance, please contact your system administrator.

Best regards,
TradeLink Training System Administration

---
This is an automated message. Please do not reply to this email.`;
  }
}

module.exports = EmailMigrationService;
