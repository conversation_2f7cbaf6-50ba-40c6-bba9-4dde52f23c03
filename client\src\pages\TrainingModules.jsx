import React from 'react'
import { <PERSON>, <PERSON>, Col, Typo<PERSON>, Button, Progress, Tag, Space, Alert, message } from 'antd'
import { BookOutlined, PlayCircleOutlined, CheckCircleOutlined, TrophyOutlined } from '@ant-design/icons'
import { useQuery } from 'react-query'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { trainingAPI, usersAPI, handleAPIError } from '../services/api'
import { useAuth } from '../contexts/AuthContext'
import { useLanguage } from '../contexts/LanguageContext'
import LoadingImage from '../components/LoadingImage'

const { Title, Text, Paragraph } = Typography

function TrainingModules() {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { user } = useAuth()
  const { currentLanguage } = useLanguage()

  // Fetch training modules
  const { data: modulesData, isLoading: modulesLoading, error: modulesError } = useQuery({
    queryKey: ['training-modules'],
    queryFn: trainingAPI.getModules,
    select: (response) => response.data.modules,
  })

  // Fetch user profile with progress
  const { data: profileData, isLoading: profileLoading, error: profileError } = useQuery({
    queryKey: ['user-profile', user?.email],
    queryFn: () => usersAPI.getProfile(),
    select: (response) => response.data,
    enabled: !!user?.email,
  })

  const isLoading = modulesLoading || profileLoading
  const error = modulesError || profileError

  if (isLoading) {
    return <LoadingImage text={t('trainingModules.loadingModules')} />
  }

  if (error) {
    const errorInfo = handleAPIError(error)
    return (
      <Alert
        message={t('trainingModules.errorLoadingModules')}
        description={errorInfo.message}
        type="error"
        showIcon
      />
    )
  }

  const modules = modulesData || []
  const progress = profileData?.progress || []

  // Calculate module progress - only consider the user's current language
  const getModuleProgress = (moduleId) => {
    // Filter progress to only include the user's current language
    const moduleProgress = progress.filter(p =>
      p.module_id === moduleId && p.language_code === currentLanguage
    )

    if (moduleProgress.length === 0) return { completed: 0, total: 1, percentage: 0 }

    // Since we're only looking at one language, there should be at most one progress record
    const progressRecord = moduleProgress[0]

    // Calculate progress based on completion status
    let percentage = 0
    if (progressRecord.video_watched && progressRecord.quiz_completed && progressRecord.quiz_passed) {
      percentage = 100 // Full completion
    } else if (progressRecord.video_watched && !progressRecord.quiz_completed) {
      percentage = 50 // Video completed, quiz not started
    } else if (progressRecord.video_watched && progressRecord.quiz_completed && !progressRecord.quiz_passed) {
      percentage = 75 // Video completed, quiz attempted but not passed
    } else if (progressRecord.video_watched) {
      percentage = 50 // Only video completed
    }
    // If video not watched, no progress (0%)

    const completed = (progressRecord.video_watched && progressRecord.quiz_completed && progressRecord.quiz_passed) ? 1 : 0

    return { completed, total: 1, percentage }
  }

  // Get detailed completion status for a module - only consider current language
  const getModuleCompletionStatus = (moduleId) => {
    // Filter progress to only include the user's current language
    const moduleProgress = progress.filter(p =>
      p.module_id === moduleId && p.language_code === currentLanguage
    )

    if (moduleProgress.length === 0) {
      return { status: 'not-started', message: t('trainingModules.videoAndQuizNotCompleted') }
    }

    // Since we're only looking at one language, there should be at most one progress record
    const progressRecord = moduleProgress[0]

    if (progressRecord.video_watched && progressRecord.quiz_completed && progressRecord.quiz_passed) {
      return { status: 'completed', message: t('dashboard.statusQuizPassed') }
    } else if (progressRecord.video_watched && !progressRecord.quiz_completed) {
      return { status: 'video-completed', message: t('trainingModules.quizNotCompleted') }
    } else if (progressRecord.video_watched && progressRecord.quiz_completed && !progressRecord.quiz_passed) {
      return { status: 'video-completed', message: t('trainingModules.quizNotCompleted') }
    } else if (progressRecord.video_watched) {
      return { status: 'in-progress', message: t('trainingModules.videoAndQuizNotCompleted') }
    } else {
      return { status: 'not-started', message: t('trainingModules.videoAndQuizNotCompleted') }
    }
  }

  // Get module status - use completion status logic instead of just percentage
  const getModuleStatus = (moduleId) => {
    const completionStatus = getModuleCompletionStatus(moduleId)

    // Map completion status to module status
    switch (completionStatus.status) {
      case 'completed':
        return 'completed'
      case 'video-completed':
      case 'in-progress':
        return 'in-progress'
      default:
        return 'not-started'
    }
  }

  // Get status color and text
  const getStatusInfo = (status) => {
    switch (status) {
      case 'completed':
        return { color: 'success', text: t('trainingModules.completed'), icon: <CheckCircleOutlined /> }
      case 'in-progress':
        return { color: 'processing', text: t('trainingModules.inProgress'), icon: <PlayCircleOutlined /> }
      default:
        return { color: 'default', text: t('trainingModules.notStarted'), icon: <BookOutlined /> }
    }
  }

  const handleModuleClick = async (moduleId) => {
    try {
      // Fetch module details to get available languages
      const response = await trainingAPI.getModule(moduleId)
      const { videos } = response.data

      if (!videos || videos.length === 0) {
        message.warning(t('trainingModules.noVideosWarning'))
        return
      }

      // Check if the user's current language is available for this module
      const currentLanguageVideo = videos.find(v => v.language_code === currentLanguage)

      if (currentLanguageVideo) {
        // Use the user's current language if available
        navigate(`/training/${moduleId}/video/${currentLanguage}`)
      } else {
        // Fallback to the first available language if current language is not available
        navigate(`/training/${moduleId}/video/${videos[0].language_code}`)
        message.info(t('trainingModules.languageNotAvailable', {
          language: currentLanguage,
          fallback: videos[0].language_code
        }))
      }
    } catch (error) {
      console.error('Error fetching module details:', error)
      message.error(t('trainingModules.failedToLoadModule'))
    }
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>{t('trainingModules.title')}</Title>
        <Paragraph type="secondary">
          {t('trainingModules.description')}
        </Paragraph>
      </div>

      {modules.length === 0 ? (
        <Card>
          <div style={{ textAlign: 'center', padding: 40 }}>
            <BookOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
            <Title level={4} type="secondary">{t('trainingModules.noModules')}</Title>
            <Text type="secondary">
              {t('trainingModules.noModulesDescription')}
            </Text>
          </div>
        </Card>
      ) : (
        <Row gutter={[16, 16]}>
          {modules.map((module) => {
            const moduleProgress = getModuleProgress(module.id)
            const status = getModuleStatus(module.id)
            const statusInfo = getStatusInfo(status)

            return (
              <Col xs={24} sm={12} lg={8} xl={6} key={module.id}>
                <Card
                  hoverable
                  onClick={() => handleModuleClick(module.id)}
                  style={{ height: '100%' }}
                  actions={[
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleModuleClick(module.id)
                      }}
                    >
                      {status === 'completed' ? t('trainingModules.review') : t('trainingModules.start')}
                    </Button>
                  ]}
                >
                  <div style={{ marginBottom: 16 }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <BookOutlined style={{ fontSize: 24, color: '#1890ff' }} />
                        <Tag color={statusInfo.color} icon={statusInfo.icon}>
                          {statusInfo.text}
                        </Tag>
                      </div>
                      
                      <Title level={4} style={{ margin: 0 }}>
                        {module.title}
                      </Title>
                      
                      {module.description && (
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {module.description}
                        </Text>
                      )}
                    </Space>
                  </div>

                  <div style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                      <Text strong>{t('trainingModules.progress')}</Text>
                      <Text>{moduleProgress.percentage}%</Text>
                    </div>
                    <Progress 
                      percent={moduleProgress.percentage} 
                      size="small"
                      status={status === 'completed' ? 'success' : 'active'}
                    />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {getModuleCompletionStatus(module.id).message}
                    </Text>
                  </div>

                  {status === 'completed' && (
                    <div style={{ textAlign: 'center', marginTop: 8 }}>
                      <TrophyOutlined style={{ color: '#faad14', marginRight: 4 }} />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {t('trainingModules.certificateAvailable')}
                      </Text>
                    </div>
                  )}
                </Card>
              </Col>
            )
          })}
        </Row>
      )}
    </div>
  )
}

export default TrainingModules
