import React, { useState } from 'react'
import { Card, Table, Button, Space, Typography, Modal, Form, Input, message, Popconfirm, Tag, Tooltip, Switch } from 'antd'
import { UserAddOutlined, EditOutlined, DeleteOutlined, ReloadOutlined, CopyOutlined, MailOutlined } from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import { usersAPI, handleAPIError } from '../../services/api'
import systemConfigAPI from '../../services/systemConfigAPI'
import { copyToClipboard } from '../../utils/clipboard'

const { Title, Text } = Typography
const { TextArea } = Input

function UserManagement() {
  const { t } = useTranslation()
  const [bulkCreateVisible, setBulkCreateVisible] = useState(false)
  const [bulkCreateForm] = Form.useForm()
  const [createdUsers, setCreatedUsers] = useState([])
  const [showCreatedUsers, setShowCreatedUsers] = useState(false)
  const queryClient = useQueryClient()

  // Fetch system configuration
  const { data: systemConfig } = useQuery(
    'system-config',
    systemConfigAPI.getSystemConfig,
    {
      select: (response) => response.data.data,
    }
  )

  // Fetch all users
  const { data: usersData, isLoading, error } = useQuery(
    'all-users',
    usersAPI.getAll,
    {
      select: (response) => {
        // Ensure users are sorted by creation date (newest first)
        const users = response.data.users || []
        return users.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      },
    }
  )

  // Bulk create users mutation
  const bulkCreateMutation = useMutation(
    (emails) => usersAPI.bulkCreate(emails),
    {
      onSuccess: (response) => {
        const { createdUsers, existingUsers, errors } = response.data
        setCreatedUsers(createdUsers)
        setShowCreatedUsers(true)
        setBulkCreateVisible(false)
        bulkCreateForm.resetFields()
        queryClient.invalidateQueries('all-users')
        
        message.success(`Created ${createdUsers.length} users successfully`)
        if (existingUsers.length > 0) {
          message.warning(`${existingUsers.length} users already existed`)
        }
        if (errors.length > 0) {
          message.error(`${errors.length} users failed to create`)
        }
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`Failed to create users: ${errorInfo.message}`)
      }
    }
  )

  // Reset password mutation
  const resetPasswordMutation = useMutation(
    (email) => usersAPI.resetPassword(email),
    {
      onSuccess: (response) => {
        const { email, newPassword } = response.data
        Modal.info({
          title: t('userManagement.passwordResetSuccessful'),
          content: (
            <div>
              <p>{t('userManagement.newPasswordFor')} <strong>{email}</strong>:</p>
              <div style={{
                background: '#f5f5f5',
                padding: 12,
                borderRadius: 4,
                fontFamily: 'monospace',
                fontSize: 16,
                textAlign: 'center',
                margin: '12px 0'
              }}>
                {newPassword}
              </div>
              <p style={{ color: '#8c8c8c', fontSize: 12 }}>
                {t('userManagement.copyPasswordSecurely')}
              </p>
            </div>
          ),
          width: 500,
        })
        message.success(t('userManagement.passwordResetSuccessful'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('userManagement.failedToResetPassword')}: ${errorInfo.message}`)
      }
    }
  )

  // Generate login email mutation
  const generateLoginEmailMutation = useMutation(
    (email) => usersAPI.sendLoginEmail(email),
    {
      onSuccess: (response) => {
        const { email, newPassword, emailTemplate, emailSent, emailError, emailMethod } = response.data

        if (emailSent) {
          message.success(t('userManagement.emailSentSuccessfully'))
        } else if (emailMethod === 'smtp' && emailError) {
          message.warning(`${t('userManagement.emailFailedFallbackToClipboard')}: ${emailError}`)
          // Copy to clipboard as fallback
          copyToClipboard(emailTemplate).then(() => {
            message.info(t('userManagement.emailCopiedToClipboard'))
          }).catch(() => {
            message.error(t('userManagement.failedToCopyToClipboard'))
          })
        } else {
          // Clipboard method or fallback
          copyToClipboard(emailTemplate).then(() => {
            message.success(t('userManagement.passwordResetAndEmailCopied'))
          }).catch(() => {
            message.error(t('userManagement.passwordResetSuccessfulButCopyFailed'))
          })
        }

        queryClient.invalidateQueries('all-users')
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('userManagement.failedToGenerateLoginEmail')}: ${errorInfo.message}`)
      }
    }
  )

  // Delete user mutation
  const deleteUserMutation = useMutation(
    (email) => usersAPI.delete(email),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('all-users')
        message.success(t('userManagement.userDeletedSuccessfully'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('userManagement.failedToDeleteUser')}: ${errorInfo.message}`)
      }
    }
  )

  // Update user language mutation
  const updateLanguageMutation = useMutation(
    ({ email, language }) => usersAPI.updateLanguage(email, language),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('all-users')
        message.success(t('userManagement.languageUpdatedSuccessfully'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('userManagement.failedToUpdateLanguage')}: ${errorInfo.message}`)
      }
    }
  )

  const generateEmailTemplate = (userEmail, password) => {
    const template = `To: ${userEmail}
Subject: ${t('userManagement.emailTemplate.subject')}

${t('userManagement.emailTemplate.greeting')}

${t('userManagement.emailTemplate.intro')}

${t('userManagement.emailTemplate.instruction')}

${t('userManagement.emailTemplate.credentialsHeader')}

${t('userManagement.emailTemplate.email')}: ${userEmail}
${t('userManagement.emailTemplate.password')}: ${password}

${t('userManagement.emailTemplate.securityNoticeHeader')}
• ${t('userManagement.emailTemplate.passwordExpiry')}
• ${t('userManagement.emailTemplate.contactAdmin')}
• ${t('userManagement.emailTemplate.keepSecure')}
• ${t('userManagement.emailTemplate.loginPromptly')}

${t('userManagement.emailTemplate.supportText')}

${t('userManagement.emailTemplate.signature')}

---
${t('userManagement.emailTemplate.securityFooter')}`

    return template
  }

  const handleBulkCreate = (values) => {
    bulkCreateMutation.mutate(values.emails)
  }

  const handleResetPassword = (email, isDefaultAdmin) => {
    // Show warning for default admin but allow manual password change
    if (isDefaultAdmin) {
      Modal.warning({
        title: t('userManagement.cannotResetDefaultAdminPassword'),
        content: t('userManagement.defaultAdminPasswordResetDisabled'),
        okText: t('common.ok')
      })
      return
    }
    resetPasswordMutation.mutate(email)
  }

  const handleGenerateLoginEmail = (email, isDefaultAdmin) => {
    // Prevent email generation for default admin
    if (isDefaultAdmin) {
      Modal.warning({
        title: t('userManagement.cannotGenerateEmailForDefaultAdmin'),
        content: t('userManagement.defaultAdminEmailDisabled'),
        okText: t('common.ok')
      })
      return
    }

    Modal.confirm({
      title: t('userManagement.generateLoginEmail'),
      content: (
        <div>
          <p>{t('userManagement.generateLoginEmailConfirm')}</p>
          <ul>
            <li>{t('userManagement.generateNewPassword')} <strong>{email}</strong></li>
            <li>{t('userManagement.resetPasswordExpiration')}</li>
            <li>{t('userManagement.createProfessionalEmailTemplate')}</li>
            <li>{t('userManagement.copyCompleteEmailToClipboard')}</li>
          </ul>
          <p style={{ color: '#fa8c16', marginTop: 12 }}>
            <strong>{t('common.note')}:</strong> {t('userManagement.currentPasswordInvalidated')}
          </p>
        </div>
      ),
      okText: t('userManagement.generateEmail'),
      cancelText: t('common.cancel'),
      onOk: () => {
        generateLoginEmailMutation.mutate(email)
      }
    })
  }

  const handleDeleteUser = (email) => {
    deleteUserMutation.mutate(email)
  }

  const handleLanguageChange = (email, language) => {
    updateLanguageMutation.mutate({ email, language })
  }

  const copyPassword = (password) => {
    copyToClipboard(password).then(() => {
      message.success(t('userManagement.passwordCopiedToClipboard'))
    }).catch(() => {
      message.error(t('userManagement.failedToCopyPassword'))
    })
  }

  const columns = [
    {
      title: t('userManagement.email'),
      dataIndex: 'email',
      key: 'email',
      render: (email) => <Text strong>{email}</Text>
    },
    {
      title: t('userManagement.name'),
      key: 'name',
      render: (_, record) => (
        record.first_name || record.last_name
          ? `${record.first_name || ''} ${record.last_name || ''}`.trim()
          : '-'
      )
    },
    {
      title: t('userManagement.role'),
      dataIndex: 'is_admin',
      key: 'role',
      render: (isAdmin) => (
        <Tag color={isAdmin ? 'gold' : 'blue'}>
          {isAdmin ? t('userManagement.admin') : t('userManagement.user')}
        </Tag>
      )
    },
    {
      title: t('userManagement.language'),
      dataIndex: 'language',
      key: 'language',
      render: (language, record) => (
        <Space>
          <Switch
            checked={language === 'zh'}
            onChange={(checked) => handleLanguageChange(record.email, checked ? 'zh' : 'en')}
            loading={updateLanguageMutation.isLoading}
            checkedChildren="中文"
            unCheckedChildren="EN"
            size="small"
          />
          <span style={{ fontSize: 12, color: '#666' }}>
            {language === 'zh' ? '中文' : 'English'}
          </span>
        </Space>
      )
    },
    {
      title: t('userManagement.status'),
      dataIndex: 'is_active',
      key: 'status',
      render: (isActive) => (
        <Tag color={isActive ? 'success' : 'error'}>
          {isActive ? t('userManagement.active') : t('userManagement.inactive')}
        </Tag>
      )
    },
    {
      title: t('userManagement.completedTrainings'),
      dataIndex: 'completed_trainings',
      key: 'completed_trainings',
      render: (count) => count || 0
    },
    {
      title: t('userManagement.created'),
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: (a, b) => new Date(b.created_at) - new Date(a.created_at),
      defaultSortOrder: 'ascend',
      render: (date) => (
        <div>
          <div>{new Date(date).toLocaleDateString()}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
            {new Date(date).toLocaleTimeString()}
          </div>
        </div>
      )
    },
    {
      title: t('userManagement.passwordStatus'),
      key: 'password_status',
      render: (_, record) => {
        if (record.is_admin) {
          return <Tag color="blue">{t('userManagement.noExpiry')}</Tag>
        }

        if (record.password_expired) {
          return (
            <Tag color="error">
              {t('userManagement.expired')} ({record.password_age_days || 0} {t('userManagement.days')})
            </Tag>
          )
        }

        const passwordExpirationDays = systemConfig?.password_expiration_days || 8
        const daysLeft = passwordExpirationDays - (record.password_age_days || 0)
        const color = daysLeft <= 2 ? 'warning' : 'success'

        return (
          <Tag color={color}>
            {daysLeft} {t('userManagement.daysLeft')}
          </Tag>
        )
      }
    },
    {
      title: t('userManagement.lastLogin'),
      dataIndex: 'last_login',
      key: 'last_login',
      render: (date) => date ? new Date(date).toLocaleDateString() : t('userManagement.never')
    },
    {
      title: t('userManagement.actions'),
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title={record.is_default_admin ? t('userManagement.emailDisabledForDefaultAdmin') : t('userManagement.generateLoginEmail')}>
            <Button
              size="small"
              type="primary"
              icon={<MailOutlined />}
              onClick={() => handleGenerateLoginEmail(record.email, record.is_default_admin)}
              loading={generateLoginEmailMutation.isLoading}
              disabled={record.is_default_admin}
            />
          </Tooltip>

          <Tooltip title={record.is_default_admin ? t('userManagement.passwordResetDisabledForDefaultAdmin') : t('userManagement.resetPassword')}>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => handleResetPassword(record.email, record.is_default_admin)}
              loading={resetPasswordMutation.isLoading}
              disabled={record.is_default_admin}
            />
          </Tooltip>

          {record.email !== '<EMAIL>' && (
            <Popconfirm
              title={t('userManagement.deleteUserConfirm')}
              onConfirm={() => handleDeleteUser(record.email)}
              okText={t('common.yes')}
              cancelText={t('common.no')}
            >
              <Tooltip title={t('userManagement.deleteUser')}>
                <Button
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  loading={deleteUserMutation.isLoading}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  const createdUsersColumns = [
    {
      title: t('userManagement.email'),
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: t('userManagement.password'),
      dataIndex: 'password',
      key: 'password',
      render: (password) => (
        <Space>
          <Text code>{password}</Text>
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={() => copyPassword(password)}
          />
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>{t('userManagement.title')}</Title>
        <Button
          type="primary"
          icon={<UserAddOutlined />}
          onClick={() => setBulkCreateVisible(true)}
        >
          {t('userManagement.bulkCreate')}
        </Button>
      </div>

      <Card>
        <div style={{ marginBottom: 16, padding: 12, background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
          <Text strong style={{ color: '#389e0d' }}>💡 {t('userManagement.quickActions')}:</Text>
          <div style={{ marginTop: 4, fontSize: 12, color: '#52c41a' }}>
            • <strong>{t('userManagement.generateLoginEmail')}</strong>: {t('userManagement.generateLoginEmailDescription')}
            <br />
            • <strong>{t('userManagement.resetPassword')}</strong>: {t('userManagement.resetPasswordDescription')}
            <br />
            • {t('userManagement.passwordExpiryNote')}
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={usersData || []}
          rowKey="email"
          loading={isLoading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => t('userManagement.totalUsers', { total })
          }}
        />
      </Card>

      {/* Bulk Create Modal */}
      <Modal
        title={t('userManagement.bulkCreateTitle')}
        open={bulkCreateVisible}
        onCancel={() => setBulkCreateVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={bulkCreateForm}
          layout="vertical"
          onFinish={handleBulkCreate}
        >
          <Form.Item
            name="emails"
            label={t('userManagement.emailAddresses')}
            rules={[
              { required: true, message: t('userManagement.pleaseEnterEmailAddresses') }
            ]}
          >
            <TextArea
              rows={8}
              placeholder={t('userManagement.emailPlaceholder')}
            />
          </Form.Item>

          <div style={{ color: '#8c8c8c', fontSize: 12, marginBottom: 16 }}>
            • {t('userManagement.emailSeparatorNote')}
            <br />
            • {t('userManagement.randomPasswordsNote')}
            <br />
            • {t('userManagement.existingUsersSkipped')}
          </div>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={bulkCreateMutation.isLoading}
              >
                {t('userManagement.bulkCreate')}
              </Button>
              <Button onClick={() => setBulkCreateVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Created Users Modal */}
      <Modal
        title={t('userManagement.usersCreatedSuccessfully')}
        open={showCreatedUsers}
        onCancel={() => setShowCreatedUsers(false)}
        footer={[
          <Button key="close" onClick={() => setShowCreatedUsers(false)}>
            {t('common.close')}
          </Button>
        ]}
        width={800}
      >
        <div style={{ marginBottom: 16 }}>
          <Text strong>
            {t('userManagement.usersCreatedSuccessfullyMessage', { count: createdUsers.length })}
          </Text>
        </div>
        
        <Table
          columns={createdUsersColumns}
          dataSource={createdUsers}
          rowKey="email"
          pagination={false}
          size="small"
        />
      </Modal>
    </div>
  )
}

export default UserManagement
