# Database Reset Script for Internal Training System
# This script completely wipes the database and resets to defaults

Write-Host "🔄 Starting database reset process..." -ForegroundColor Yellow

# Stop all containers
Write-Host "📦 Stopping containers..." -ForegroundColor Blue
docker-compose down

# Remove the database volume (this deletes all data)
Write-Host "🗑️ Removing database volume..." -ForegroundColor Red
docker volume rm internaltrainingsystem_postgres_data

# Remove uploaded files (optional)
Write-Host "📁 Cleaning uploaded files..." -ForegroundColor Blue
if (Test-Path ".\server\uploads\videos") {
    Remove-Item -Recurse -Force ".\server\uploads\videos\*" -ErrorAction SilentlyContinue
    Write-Host "✅ Uploaded videos cleared" -ForegroundColor Green
}

# Start everything fresh
Write-Host "🚀 Starting fresh containers..." -ForegroundColor Blue
docker-compose up -d

# Wait for services to be healthy
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep 15

# Check status
Write-Host "📊 Checking container status..." -ForegroundColor Blue
docker-compose ps

Write-Host ""
Write-Host "✅ Database reset complete!" -ForegroundColor Green
Write-Host ""
Write-Host "🔑 Default Admin Credentials:" -ForegroundColor Cyan
Write-Host "   Email: <EMAIL>" -ForegroundColor White
Write-Host "   Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Access the application at: http://localhost" -ForegroundColor Cyan
