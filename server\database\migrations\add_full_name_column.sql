-- Migration: Add full_name column to users table and populate it
-- This migration adds a full_name column and populates it using email extraction logic

-- Add full_name column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'full_name'
    ) THEN
        ALTER TABLE users ADD COLUMN full_name VARCHAR(200);
    END IF;
END $$;

-- Function to extract names from email and return as record
CREATE OR REPLACE FUNCTION extract_names_from_email(email_address TEXT)
RETURNS TABLE(first_name TEXT, last_name TEXT, full_name TEXT) AS $$
DECLARE
    local_part TEXT;
    parts TEXT[];
    fname TEXT;
    lname TEXT;
BEGIN
    -- Get the part before @ symbol
    local_part := split_part(email_address, '@', 1);

    -- Handle common email patterns
    IF position('.' in local_part) > 0 THEN
        -- <EMAIL> -> First: John, Last: Doe
        parts := string_to_array(local_part, '.');
        fname := initcap(parts[1]);
        lname := CASE WHEN array_length(parts, 1) > 1 THEN initcap(parts[2]) ELSE '' END;
    ELSIF position('_' in local_part) > 0 THEN
        -- <EMAIL> -> First: John, Last: Doe
        parts := string_to_array(local_part, '_');
        fname := initcap(parts[1]);
        lname := CASE WHEN array_length(parts, 1) > 1 THEN initcap(parts[2]) ELSE '' END;
    ELSE
        -- <EMAIL> -> First: '', Last: Kelvin21706
        fname := '';
        lname := initcap(local_part);
    END IF;

    -- Return the extracted names
    first_name := fname;
    last_name := lname;
    full_name := trim(CASE WHEN fname != '' THEN fname || ' ' || lname ELSE lname END);

    RETURN NEXT;
EXCEPTION
    WHEN OTHERS THEN
        first_name := '';
        last_name := 'User';
        full_name := 'User';
        RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function to automatically set names
CREATE OR REPLACE FUNCTION set_user_names()
RETURNS TRIGGER AS $$
DECLARE
    extracted_names RECORD;
BEGIN
    -- If first_name and last_name are provided, use them
    IF NEW.first_name IS NOT NULL AND NEW.last_name IS NOT NULL AND
       (trim(NEW.first_name) != '' OR trim(NEW.last_name) != '') THEN
        NEW.full_name := trim(NEW.first_name || ' ' || NEW.last_name);
    ELSE
        -- Extract names from email
        SELECT * INTO extracted_names FROM extract_names_from_email(NEW.email);

        -- Only update if the fields are empty
        IF NEW.first_name IS NULL OR trim(NEW.first_name) = '' THEN
            NEW.first_name := extracted_names.first_name;
        END IF;

        IF NEW.last_name IS NULL OR trim(NEW.last_name) = '' THEN
            NEW.last_name := extracted_names.last_name;
        END IF;

        NEW.full_name := extracted_names.full_name;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update existing users' names
DO $$
DECLARE
    user_record RECORD;
    extracted_names RECORD;
BEGIN
    FOR user_record IN SELECT email, first_name, last_name FROM users LOOP
        -- Extract names from email
        SELECT * INTO extracted_names FROM extract_names_from_email(user_record.email);

        -- Update the user with extracted names
        UPDATE users
        SET
            first_name = COALESCE(NULLIF(user_record.first_name, ''), extracted_names.first_name),
            last_name = COALESCE(NULLIF(user_record.last_name, ''), extracted_names.last_name),
            full_name = extracted_names.full_name
        WHERE email = user_record.email;
    END LOOP;
END $$;

-- Create trigger for INSERT and UPDATE
DROP TRIGGER IF EXISTS set_user_names_trigger ON users;
CREATE TRIGGER set_user_names_trigger
    BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION set_user_names();
