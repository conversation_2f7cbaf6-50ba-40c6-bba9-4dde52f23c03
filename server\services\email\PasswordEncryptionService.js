const bcrypt = require('bcryptjs');

class PasswordEncryptionService {
  // Hash SMTP password for secure storage
  async hashPassword(password) {
    if (!password) return '';
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    return await bcrypt.hash(password, saltRounds);
  }

  // For SMTP passwords, we need to store them in a way that can be decrypted
  // Since SMTP requires the actual password, we'll use a simple encryption
  // This is more secure than plain text but allows decryption for SMTP use
  encryptPassword(password) {
    if (!password) return '';

    // Check if password is already encrypted (contains salt prefix after base64 decode)
    if (this.isPasswordEncrypted(password)) {
      console.log('Password is already encrypted, returning as-is');
      return password;
    }

    try {
      // Simple base64 encoding with a salt - not the most secure but better than plain text
      // In production, consider using proper encryption like AES
      const salt = process.env.SESSION_SECRET || 'default_salt';
      const combined = salt + ':' + password;
      const encrypted = Buffer.from(combined, 'utf8').toString('base64');

      console.log('Encrypting new password:', {
        originalLength: password.length,
        encryptedLength: encrypted.length,
        isAlreadyEncrypted: false,
        canDecrypt: this.decryptPassword(encrypted) === password // Verify encryption works
      });

      return encrypted;
    } catch (error) {
      console.error('Password encryption failed:', error);
      // If encryption fails, return the original password (less secure but functional)
      console.warn('⚠️  Falling back to plain text password storage due to encryption error');
      return password;
    }
  }

  // Check if a password is already encrypted
  isPasswordEncrypted(password) {
    if (!password) return false;
    try {
      const currentSalt = process.env.SESSION_SECRET || 'default_salt';
      const decoded = Buffer.from(password, 'base64').toString('utf8');

      // Check current salt format
      if (decoded.startsWith(currentSalt + ':')) {
        return true;
      }

      // Check legacy salt formats for backward compatibility
      const legacySalts = [
        'your_super_secret_session_key_change_in_production',
        'default_salt'
      ];

      for (const legacySalt of legacySalts) {
        if (decoded.startsWith(legacySalt + ':')) {
          return true;
        }
      }

      return false;
    } catch (error) {
      // If base64 decode fails, it's probably plain text
      return false;
    }
  }

  decryptPassword(encryptedPassword) {
    if (!encryptedPassword) return '';

    // Debug info without sensitive data
    console.log('Decrypting sensitive data:', {
      length: encryptedPassword.length,
      containsAt: encryptedPassword.includes('@'),
      containsEquals: encryptedPassword.includes('='),
      isAlreadyEncrypted: this.isPasswordEncrypted(encryptedPassword)
    });

    // If it looks like a plain text email (contains @), return as-is
    if (encryptedPassword.includes('@')) {
      console.log('Detected email address, returning as plain text');
      return encryptedPassword;
    }

    // If it's not encrypted, return as plain text
    if (!this.isPasswordEncrypted(encryptedPassword)) {
      console.log('Not encrypted format, returning as plain text');
      return encryptedPassword;
    }

    try {
      const currentSalt = process.env.SESSION_SECRET || 'default_salt';
      const decoded = Buffer.from(encryptedPassword, 'base64').toString('utf8');

      const parts = decoded.split(':');
      if (parts.length >= 2) {
        const storedSalt = parts[0];

        // Check current salt format first
        if (storedSalt === currentSalt) {
          const decrypted = parts.slice(1).join(':'); // Handle passwords that contain ':'
          console.log('Successfully decrypted sensitive data (current format):', {
            originalLength: encryptedPassword.length,
            decryptedLength: decrypted.length
          });
          return decrypted;
        }

        // Check legacy salt formats for backward compatibility
        const legacySalts = [
          'your_super_secret_session_key_change_in_production',
          'default_salt'
        ];

        for (const legacySalt of legacySalts) {
          if (storedSalt === legacySalt) {
            const decrypted = parts.slice(1).join(':'); // Handle passwords that contain ':'
            console.log('Successfully decrypted sensitive data (legacy format):', {
              originalLength: encryptedPassword.length,
              decryptedLength: decrypted.length,
              legacySalt: legacySalt
            });
            return decrypted;
          }
        }
      }
      console.warn('Decryption failed: Invalid salt or format');
      return ''; // Invalid format
    } catch (error) {
      console.error('Error decrypting password:', error.message);
      // If decryption fails, it might be plain text - return original if it looks valid
      if (encryptedPassword.length < 100 && !encryptedPassword.includes('=')) {
        console.log('Treating as plain text (decryption failed)');
        return encryptedPassword;
      }
      return '';
    }
  }

  // Migrate existing plain text sensitive data to encrypted format
  async migrateSensitiveData(getEmailConfigCallback, updateCallback) {
    try {
      const config = await getEmailConfigCallback();
      let needsUpdate = false;
      let encryptedPassword = config.smtp_pass;
      let encryptedFromAddress = config.email_from_address;

      console.log('=== MIGRATION DEBUG ===');
      console.log('Current config status:', {
        smtp_pass: config.smtp_pass ? `[${config.smtp_pass.length} chars] PROVIDED` : 'NOT_SET',
        email_from_address: config.email_from_address ? 'PROVIDED' : 'NOT_SET'
      });

      // Check if password needs encryption or is double-encrypted
      if (config.smtp_pass) {
        if (this.isPasswordEncrypted(config.smtp_pass)) {
          // Try to decrypt and see if result is still encrypted (double encryption)
          const decrypted = this.decryptPassword(config.smtp_pass);
          if (decrypted && this.isPasswordEncrypted(decrypted)) {
            console.log('⚠️  Detected double-encrypted password, fixing...');
            encryptedPassword = decrypted; // Use the once-decrypted version
            needsUpdate = true;
          } else {
            console.log('✅ Password is properly encrypted');
          }
        } else {
          console.log('📝 Migrating plain text SMTP password to encrypted format...');
          encryptedPassword = this.encryptPassword(config.smtp_pass);
          needsUpdate = true;
        }
      }

      // Check if from address needs encryption or is double-encrypted
      if (config.email_from_address) {
        if (config.email_from_address.includes('@')) {
          console.log('📝 Migrating plain text from address to encrypted format...');
          encryptedFromAddress = this.encryptPassword(config.email_from_address);
          needsUpdate = true;
        } else if (this.isPasswordEncrypted(config.email_from_address)) {
          // Try to decrypt and see if result is still encrypted (double encryption)
          const decrypted = this.decryptPassword(config.email_from_address);
          if (decrypted && this.isPasswordEncrypted(decrypted)) {
            console.log('⚠️  Detected double-encrypted from address, fixing...');
            encryptedFromAddress = decrypted; // Use the once-decrypted version
            needsUpdate = true;
          } else {
            console.log('✅ From address is properly encrypted');
          }
        }
      }

      if (needsUpdate) {
        console.log('🔄 Updating database with corrected encryption...');
        await updateCallback(encryptedPassword, encryptedFromAddress);
        console.log('✅ Sensitive data migration/fix completed');
      } else {
        console.log('✅ No migration needed - data is properly encrypted');
      }
      console.log('=== END MIGRATION DEBUG ===');
    } catch (error) {
      console.error('Error migrating sensitive data:', error);
    }
  }
}

module.exports = PasswordEncryptionService;
