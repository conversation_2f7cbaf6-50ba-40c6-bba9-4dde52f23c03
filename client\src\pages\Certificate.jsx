import React from 'react'
import { <PERSON>, Button, Typography, Alert } from 'antd'
import { ArrowLeftOutlined, PrinterOutlined } from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import { usersAPI, handleAPIError } from '../services/api'
import { useAuth } from '../contexts/AuthContext'
import tradeLinkLogoColor from '../assets/tradelink-logo-color.png'
import LoadingImage from '../components/LoadingImage'

const { Title, Text } = Typography

function Certificate() {
  const { t } = useTranslation()
  const { moduleId, languageCode } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()

  // Fetch certificate data directly
  const { data: certificateData, isLoading, error } = useQuery(
    ['certificate', moduleId, languageCode, user?.email],
    () => usersAPI.getCertificate(moduleId, languageCode),
    {
      select: (response) => response.data.certificate,
      enabled: !!moduleId && !!languageCode && !!user?.email,
    }
  )

  const handlePrint = () => {
    window.print()
  }



  if (isLoading) {
    return <LoadingImage text={t('certificate.loadingCertificate')} />
  }

  if (error) {
    const errorInfo = handleAPIError(error)
    return (
      <Alert
        message={t('certificate.errorLoadingCertificate')}
        description={errorInfo.message}
        type="error"
        showIcon
        action={
          <Button onClick={() => navigate('/training-modules')}>
            {t('quiz.backToModules')}
          </Button>
        }
      />
    )
  }

  if (!certificateData) {
    return (
      <Alert
        message={t('certificate.certificateNotAvailable')}
        description={t('certificate.certificateNotAvailableDescription')}
        type="warning"
        showIcon
        action={
          <Button onClick={() => navigate('/training-modules')}>
            {t('quiz.backToModules')}
          </Button>
        }
      />
    )
  }

  // Parse certificate data
  const certificateInfo = typeof certificateData.certificate_data === 'string'
    ? JSON.parse(certificateData.certificate_data)
    : certificateData.certificate_data

  // Use the userName from certificate data (which comes from database full_name)
  // or fallback to user's full_name from the current user context
  const userName = certificateInfo?.userName || user?.fullName || 'Training Participant'

  const completionDate = new Date(certificateInfo?.completionDate || certificateData.quiz_completed_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/training-modules')}
          style={{ marginBottom: 16 }}
        >
          {t('quiz.backToModules')}
        </Button>

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2}>{t('certificate.trainingCertificate')}</Title>
          <Button
            icon={<PrinterOutlined />}
            onClick={handlePrint}
          >
            {t('certificate.print')}
          </Button>
        </div>
      </div>

      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <div
          id="certificate-content"
          className="certificate-container"
          style={{
            background: 'white',
            border: '3px solid #1890ff',
            borderRadius: 12,
            padding: 60,
            maxWidth: 800,
            width: '100%',
            textAlign: 'center',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Decorative border */}
          <div style={{
            position: 'absolute',
            top: 20,
            left: 20,
            right: 20,
            bottom: 20,
            border: '2px solid #f0f0f0',
            borderRadius: 8,
            pointerEvents: 'none'
          }} />

          {/* Certificate content */}
          <div style={{ position: 'relative', zIndex: 1 }}>
            {/* TradeLink Logo */}
            <div style={{ marginBottom: 20, textAlign: 'center' }}>
              <img
                src={tradeLinkLogoColor}
                alt="TradeLink Logo"
                style={{
                  height: 60,
                  width: 'auto',
                  opacity: 0.9
                }}
              />
            </div>

            <div style={{ marginBottom: 30 }}>
              <Text style={{
                fontSize: 16,
                color: '#8c8c8c',
                letterSpacing: 2,
                textTransform: 'uppercase'
              }}>
                {t('certificate.certificateOfCompletion')}
              </Text>
            </div>

            <div className="certificate-header" style={{
              fontSize: 36,
              fontWeight: 'bold',
              color: '#1890ff',
              marginBottom: 30,
              fontFamily: 'serif'
            }}>
              {t('certificate.cybersecurityTraining')}
            </div>

            <div style={{ marginBottom: 40 }}>
              <Text style={{ fontSize: 18, color: '#595959' }}>
                {t('certificate.thisIsToCertifyThat')}
              </Text>
            </div>

            <div className="certificate-recipient" style={{
              fontSize: 32,
              fontWeight: 'bold',
              color: '#262626',
              marginBottom: 40,
              borderBottom: '2px solid #1890ff',
              paddingBottom: 10,
              display: 'inline-block',
              minWidth: 300
            }}>
              {userName}
            </div>

            <div style={{ marginBottom: 30 }}>
              <Text style={{ fontSize: 18, color: '#595959' }}>
                {t('certificate.hasSuccessfullyCompleted')}
              </Text>
            </div>

            <div className="certificate-title" style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#1890ff',
              marginBottom: 30,
              fontStyle: 'italic'
            }}>
              "{certificateInfo?.moduleTitle || certificateData.module_title || 'Cybersecurity Fundamentals'}"
            </div>

            <div style={{ marginBottom: 40 }}>
              <Text style={{ fontSize: 16, color: '#595959' }}>
                {t('certificate.withAScoreOf')} <strong style={{ color: '#52c41a' }}>{certificateInfo?.score || certificateData.quiz_score}%</strong>
              </Text>
            </div>

            {/* Text Signature Section
            <div style={{
              marginBottom: 30,
              padding: 20,
              backgroundColor: '#fafafa',
              borderRadius: 8,
              border: '1px solid #f0f0f0'
            }}>
              <Text style={{
                fontSize: 14,
                color: '#595959',
                fontStyle: 'italic',
                lineHeight: 1.6
              }}>
                "This certificate validates the successful completion of cybersecurity training requirements
                and demonstrates the recipient's commitment to maintaining security awareness and best practices
                in their professional responsibilities."
              </Text>
              <div style={{ marginTop: 10, textAlign: 'right' }}>
                <Text style={{
                  fontSize: 12,
                  color: '#8c8c8c',
                  fontWeight: 'bold'
                }}>
                  — TradeLink Training System
                </Text>
              </div>
            </div> */}

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'end',
              marginTop: 40,
              paddingTop: 30,
              borderTop: '1px solid #f0f0f0'
            }}>
              <div style={{ textAlign: 'left' }}>
                <div style={{
                  borderTop: '1px solid #d9d9d9',
                  paddingTop: 8,
                  minWidth: 200
                }}>
                  <Text style={{ fontSize: 14, color: '#8c8c8c' }}>
                    {t('certificate.systemAdministrator')}
                  </Text>
                </div>
              </div>

              <div style={{ textAlign: 'center' }}>
                <div style={{ marginBottom: 8 }}>
                  <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#1890ff' }}>
                    {completionDate}
                  </Text>
                </div>
                <div style={{
                  borderTop: '1px solid #d9d9d9',
                  paddingTop: 8,
                  minWidth: 150
                }}>
                  <Text style={{ fontSize: 14, color: '#8c8c8c' }}>
                    {t('certificate.dateOfCompletion')}
                  </Text>
                </div>
              </div>

              <div style={{ textAlign: 'right' }}>
                <div style={{ marginBottom: 8 }}>
                  <Text style={{
                    fontSize: 12,
                    color: '#8c8c8c',
                    fontFamily: 'monospace'
                  }}>
                    {certificateData.certificate_number}
                  </Text>
                </div>
                <div style={{
                  borderTop: '1px solid #d9d9d9',
                  paddingTop: 8,
                  minWidth: 150
                }}>
                  <Text style={{ fontSize: 14, color: '#8c8c8c' }}>
                    {t('certificate.certificateId')}
                  </Text>
                </div>
              </div>
            </div>

            {/* Bottom Logo Signature */}
            <div style={{
              marginTop: 30,
              textAlign: 'center',
              paddingTop: 20,
              borderTop: '1px solid #f0f0f0'
            }}>
              <img
                src={tradeLinkLogoColor}
                alt="TradeLink Logo"
                style={{
                  height: 30,
                  width: 'auto',
                  opacity: 0.6
                }}
              />
              <div style={{ marginTop: 5 }}>
                <Text style={{
                  fontSize: 10,
                  color: '#bfbfbf',
                  letterSpacing: 1
                }}>
                  TRADELINK INTERNAL TRAINING SYSTEM
                </Text>
              </div>
            </div>
          </div>

          {/* Decorative elements */}
          <div style={{
            position: 'absolute',
            top: 30,
            left: 30,
            width: 60,
            height: 60,
            background: 'linear-gradient(45deg, #1890ff, #40a9ff)',
            borderRadius: '50%',
            opacity: 0.1
          }} />
          
          <div style={{
            position: 'absolute',
            bottom: 30,
            right: 30,
            width: 80,
            height: 80,
            background: 'linear-gradient(45deg, #52c41a, #73d13d)',
            borderRadius: '50%',
            opacity: 0.1
          }} />
        </div>
      </div>

      <div style={{
        textAlign: 'center',
        marginTop: 24,
        padding: 16,
        background: '#f8f9fa',
        borderRadius: 6
      }}>
        <Text type="secondary" style={{ fontSize: 12 }}>
          {t('certificate.certificateVerificationText')}
          <br />
          {t('certificate.verificationContactText')}
        </Text>
      </div>

      <style jsx>{`
        @media print {
          /* Hide everything except certificate */
          body * {
            visibility: hidden;
          }
          #certificate-content, #certificate-content * {
            visibility: visible;
          }

          /* Reset page margins and setup for PDF */
          @page {
            margin: 0.5in;
            size: A4;
          }

          body {
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
            height: auto !important;
          }

          /* Certificate container for print - position absolutely to control placement */
          #certificate-content {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            max-width: none !important;
            height: auto !important;
            margin: 0 !important;
            padding: 40px !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            page-break-inside: avoid;
            transform: none !important;
            display: block !important;
          }

          /* Ensure certificate fits on one page */
          .certificate-container {
            max-width: none !important;
            width: 100% !important;
            margin: 0 !important;
            padding: 30px !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            page-break-inside: avoid;
            border: 2px solid #1890ff !important;
          }

          /* Adjust font sizes for print to fit better */
          .certificate-header {
            font-size: 26px !important;
            margin-bottom: 20px !important;
          }

          .certificate-recipient {
            font-size: 28px !important;
            margin-bottom: 30px !important;
          }

          .certificate-title {
            font-size: 20px !important;
            margin-bottom: 20px !important;
          }

          /* Hide decorative elements that might cause issues */
          div[style*="position: absolute"] {
            display: none !important;
          }

          /* Ensure proper spacing for print */
          div[style*="marginBottom: 30"] {
            margin-bottom: 15px !important;
          }

          div[style*="marginBottom: 40"] {
            margin-bottom: 20px !important;
          }
        }
      `}</style>
    </div>
  )
}

export default Certificate
