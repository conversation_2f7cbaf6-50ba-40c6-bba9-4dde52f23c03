/**
 * Question Renderer Component
 * Extracted from Quiz.jsx for better modularity
 */

import React from 'react'
import { Card, Radio, Checkbox, Input, Alert, Button } from 'antd'
import { EyeOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const { TextArea } = Input

// Helper function to ensure data URL format for images
const ensureDataURL = (imageData) => {
  if (!imageData) return null
  if (imageData.startsWith('data:')) return imageData
  return `data:image/jpeg;base64,${imageData}`
}

// Helper function to open image in new tab
const openImageInNewTab = (imageData) => {
  if (!imageData) return
  const dataURL = ensureDataURL(imageData)
  const newWindow = window.open()
  newWindow.document.write(`<img src="${dataURL}" style="max-width: 100%; height: auto;" />`)
}

function QuestionRenderer({
  question,
  questionIndex,
  answer,
  onAnswerChange,
  showValidation = false
}) {
  const { t } = useTranslation()



  const handleAnswerChange = (value) => {
    onAnswerChange?.(question.id, value)
  }

  const handleMultiChoiceChange = (checkedValues) => {
    onAnswerChange?.(question.id, checkedValues)
  }

  const renderQuestionImage = () => {
    if (!question.question_image) return null

    return (
      <div style={{ margin: '16px 0' }}>
        <img
          src={ensureDataURL(question.question_image)}
          alt="Question"
          style={{
            maxWidth: '100%',
            height: 'auto',
            border: '1px solid #d9d9d9',
            borderRadius: 4
          }}
        />
        <div style={{ marginTop: 8 }}>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => openImageInNewTab(question.question_image)}
          >
            {t('quiz.openImageInNewTab')}
          </Button>
        </div>
      </div>
    )
  }

  const renderOptionImage = (option) => {
    const optionImage = option.optionImage || option.option_image;
    if (!optionImage) return null

    return (
      <div style={{ marginTop: 8 }}>
        <img
          src={ensureDataURL(optionImage)}
          alt="Option"
          style={{
            maxWidth: '200px',
            height: 'auto',
            border: '1px solid #d9d9d9',
            borderRadius: 4
          }}
        />
      </div>
    )
  }

  const renderImageButton = (option) => {
    const optionImage = option.optionImage || option.option_image;
    if (!optionImage) return null

    return (
      <div style={{ marginTop: 4 }}>
        <Button
          size="small"
          icon={<EyeOutlined />}
          onClick={(e) => {
            e.stopPropagation();
            openImageInNewTab(optionImage);
          }}
        >
          {t('quiz.openImageInNewTab')}
        </Button>
      </div>
    )
  }

  const renderQuestionContent = () => {
    switch (question.question_type) {
      case 'multiple_choice':
        return (
          <div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {question.options.map((option) => (
                <div key={option.id}>
                  <div
                    style={{
                      border: '1px solid #f0f0f0',
                      borderRadius: '4px',
                      padding: '12px',
                      backgroundColor: '#fafafa',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      ...(answer === option.id ? {
                        borderColor: '#1890ff',
                        backgroundColor: '#e6f7ff'
                      } : {})
                    }}
                    onClick={() => handleAnswerChange(option.id)}
                  >
                    <Radio
                      value={option.id}
                      checked={answer === option.id}
                      style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        width: '100%',
                        pointerEvents: 'none'
                      }}
                    >
                      <div style={{ marginLeft: '8px', flex: 1 }}>
                        <div style={{ fontSize: '14px', lineHeight: '1.5' }}>
                          {option.optionText || option.option_text}
                        </div>
                        {renderOptionImage(option)}
                      </div>
                    </Radio>
                  </div>
                  {renderImageButton(option)}
                </div>
              ))}
            </div>
          </div>
        )

      case 'multi_choice':
        return (
          <div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {question.options.map((option) => (
                <div key={option.id}>
                  <div
                    style={{
                      border: '1px solid #f0f0f0',
                      borderRadius: '4px',
                      padding: '12px',
                      backgroundColor: '#fafafa'
                    }}
                  >
                    <Checkbox
                      value={option.id}
                      checked={(answer || []).includes(option.id)}
                      onChange={(e) => {
                        const currentAnswers = answer || [];
                        let newAnswers;
                        if (e.target.checked) {
                          newAnswers = [...currentAnswers, option.id];
                        } else {
                          newAnswers = currentAnswers.filter(id => id !== option.id);
                        }
                        handleMultiChoiceChange(newAnswers);
                      }}
                      style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        width: '100%'
                      }}
                    >
                      <div style={{ marginLeft: '8px', flex: 1 }}>
                        <div style={{ fontSize: '14px', lineHeight: '1.5' }}>
                          {option.optionText || option.option_text}
                        </div>
                        {renderOptionImage(option)}
                      </div>
                    </Checkbox>
                  </div>
                  {renderImageButton(option)}
                </div>
              ))}
            </div>
            <Alert
              message={t('quiz.multiChoiceNote')}
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />
          </div>
        )

      case 'true_false':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <div
              style={{
                border: '1px solid #f0f0f0',
                borderRadius: '4px',
                padding: '12px',
                backgroundColor: '#fafafa',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                ...(answer === 'true' ? {
                  borderColor: '#1890ff',
                  backgroundColor: '#e6f7ff'
                } : {})
              }}
              onClick={() => handleAnswerChange('true')}
            >
              <Radio
                value="true"
                checked={answer === 'true'}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  pointerEvents: 'none'
                }}
              >
                <span style={{ marginLeft: '8px', fontSize: '14px' }}>
                  {t('quiz.true')}
                </span>
              </Radio>
            </div>
            <div
              style={{
                border: '1px solid #f0f0f0',
                borderRadius: '4px',
                padding: '12px',
                backgroundColor: '#fafafa',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                ...(answer === 'false' ? {
                  borderColor: '#1890ff',
                  backgroundColor: '#e6f7ff'
                } : {})
              }}
              onClick={() => handleAnswerChange('false')}
            >
              <Radio
                value="false"
                checked={answer === 'false'}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  pointerEvents: 'none'
                }}
              >
                <span style={{ marginLeft: '8px', fontSize: '14px' }}>
                  {t('quiz.false')}
                </span>
              </Radio>
            </div>
          </div>
        )

      case 'short_answer':
        return (
          <div>
            <TextArea
              rows={4}
              value={answer || ''}
              onChange={(e) => handleAnswerChange(e.target.value)}
              placeholder={t('quiz.enterAnswerPlaceholder')}
            />
            <Alert
              message={t('quiz.shortAnswerNote')}
              type="warning"
              showIcon
              style={{ marginTop: 8 }}
            />
          </div>
        )

      default:
        return <div>Unknown question type</div>
    }
  }

  return (
    <Card 
      className="quiz-question"
      style={{
        marginBottom: '16px',
        border: showValidation && !answer ? '1px solid #ff4d4f' : undefined
      }}
    >
      <div className="quiz-question-title" style={{ 
        fontSize: '16px',
        fontWeight: 'bold',
        marginBottom: '16px',
        color: '#262626'
      }}>
        {t('quiz.question')} {questionIndex + 1}: {question.question_text}
      </div>

      {renderQuestionImage()}
      {renderQuestionContent()}

      {showValidation && !answer && (
        <Alert
          message={t('quiz.answerAllQuestions')}
          type="error"
          showIcon
          style={{ marginTop: 8 }}
        />
      )}
    </Card>
  )
}

export default QuestionRenderer
