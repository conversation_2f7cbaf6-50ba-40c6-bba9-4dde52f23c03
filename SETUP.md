# Internal Training System - Azure Containerized Deployment

## Overview

The Internal Training System is optimized for **Azure containerized deployment** with production-ready networking, security, and scalability configurations.

## Quick Start

### 1. Environment Setup

```bash
# Copy the Azure environment file
copy .env.azure .env

# Edit the .env file with your Azure-specific configuration
# Update database connection, storage account, and other Azure resources
```

### 2. Azure Container Deployment (Recommended)

```bash
# Deploy to Azure Container Instances
npm run deploy:azure-containers

# Or deploy to Azure App Service
npm run deploy:azure

# Or manually with PowerShell
./deploy-azure-optimized.ps1 -ResourceGroupName "training-system-rg" -AppName "training-system"
```

**Access URLs:**
- **Frontend**: https://your-app-name.azurecontainer.io
- **Backend API**: https://your-app-name.azurecontainer.io/api
- **Database**: your-postgres-server.postgres.database.azure.com:5432

### 3. Alternative Deployment Methods

```bash
# Local development
npm run deploy:local

# Docker deployment
npm run deploy:docker

# Azure deployment
npm run deploy:azure

# Auto-deploy based on DEPLOYMENT_TYPE in .env
npm run deploy
```

## Configuration

### Port Configuration

- **Frontend**: Port 8080 (Azure standard port)
- **Backend**: Port 8080 (Azure standard port)
- **Database**: Port 5432 (Azure Database for PostgreSQL)
- **Load Balancer**: Port 80/443 (public access)

### Environment Variables

All configuration is centralized in the root `.env` file:

```bash
# Deployment
NODE_ENV=production
DEPLOYMENT_TYPE=docker

# Ports
FRONTEND_PORT=80
BACKEND_PORT=3002

# API URL
REACT_APP_API_URL=http://localhost:3002/api

# Database (Docker service names)
DB_HOST=postgres
DB_NAME=training_system
DB_USER=training_user
DB_PASSWORD=training_password

# Security
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here

# Email (optional)
EMAIL_ENABLED=false
EMAIL_METHOD=clipboard

# Features
FEATURE_EMAIL_NOTIFICATIONS=true
FEATURE_VIDEO_CONTROLS=true
FEATURE_QUIZ_GENERATION=true
```

## Validation

### Check Configuration

```bash
# Validate environment configuration
npm run config:validate

# Check if configuration loads successfully
npm run config:check
```

### Health Checks

```bash
# Perform health checks on deployed services
npm run deploy health
```

## Development

### Local Development

```bash
# Start development servers
npm run dev

# Frontend: http://localhost:3000
# Backend: http://localhost:3002
```

### Building

```bash
# Build frontend only
npm run build

# Build all applications
node scripts/deploy.js build
```

## Database Management

### Reset Database

```bash
# PowerShell script (Windows)
.\scripts\reset-database.ps1

# Or manually
npm run data:wipe
```

### Default Admin Account

- **Email**: <EMAIL>
- **Password**: admin123

## Troubleshooting

### Common Issues

1. **Port 80 Permission Issues**
   - On Linux/Mac, you may need to run with sudo or use a different port
   - Update `FRONTEND_PORT=3001` in `.env` if needed

2. **Database Connection Issues**
   - Ensure PostgreSQL container is running: `docker-compose ps`
   - Check database logs: `docker-compose logs postgres`

3. **CORS Issues**
   - Update `CORS_ORIGIN` in `.env` to include your frontend URL
   - Default: `http://localhost:3000,http://localhost:80,http://localhost`

4. **Environment Variables Not Loading**
   - Ensure `.env` file exists in root directory
   - Check file permissions and encoding
   - Restart containers after changes: `npm run docker:restart`

### Logs

```bash
# View all container logs
npm run docker:logs

# View specific service logs
docker-compose logs frontend
docker-compose logs backend
docker-compose logs postgres
```

## Migration from Individual .env Files

If you have existing `.env` files in `client/` and `server/` directories:

1. **Backup existing files**:
   ```bash
   cp client/.env client/.env.backup
   cp server/.env server/.env.backup
   ```

2. **Merge configurations** into the root `.env` file

3. **Remove old files** (optional):
   ```bash
   rm client/.env server/.env
   ```

4. **Test the deployment**:
   ```bash
   npm run docker:restart
   ```

## Azure Deployment

For Azure deployment, update these variables in `.env`:

```bash
DEPLOYMENT_TYPE=azure-app-service
REACT_APP_API_URL=https://your-backend-app.azurewebsites.net/api
DB_HOST=your-postgres-server.postgres.database.azure.com
# ... other Azure-specific settings
```

Then run:
```bash
npm run deploy:azure
```

## Support

For issues or questions:
1. Check the logs using `npm run docker:logs`
2. Validate configuration with `npm run config:validate`
3. Review this setup guide
4. Check the main documentation in `docs/`
