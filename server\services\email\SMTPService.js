const nodemailer = require('nodemailer');
const PasswordEncryptionService = require('./PasswordEncryptionService');
const EmailTemplateService = require('./EmailTemplateService');

class SMTPService {
  constructor() {
    this.transporter = null;
    this.passwordService = new PasswordEncryptionService();
    this.templateService = new EmailTemplateService();
  }

  // Initialize SMTP transporter with configuration
  async initializeTransporter(config) {
    try {
      if (config.email_enabled && config.email_method === 'smtp') {
        // Decrypt sensitive data for SMTP use
        const decryptedPassword = this.passwordService.decryptPassword(config.smtp_pass);
        const decryptedFromAddress = this.passwordService.decryptPassword(config.email_from_address);

        // Store decrypted values for use in email sending
        config.decrypted_smtp_pass = decryptedPassword;
        config.decrypted_email_from_address = decryptedFromAddress;

        this.transporter = nodemailer.createTransport({
          host: config.smtp_host,
          port: parseInt(config.smtp_port) || 587,
          secure: config.smtp_secure === 'true', // true for 465, false for other ports
          auth: config.smtp_user && config.decrypted_smtp_pass ? {
            user: config.smtp_user,
            pass: config.decrypted_smtp_pass
          } : undefined,
          tls: {
            rejectUnauthorized: false // Allow self-signed certificates
          },
          connectionTimeout: 10000, // 10 seconds
          greetingTimeout: 10000, // 10 seconds
          socketTimeout: 10000 // 10 seconds
        });

        // Verify connection with timeout
        await Promise.race([
          this.transporter.verify(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('SMTP verification timeout')), 15000)
          )
        ]);
        console.log('SMTP service initialized successfully');
        return true;
      } else {
        console.log('SMTP not enabled or method not set to smtp, skipping transporter initialization');
        this.transporter = null;
        return false;
      }
    } catch (error) {
      console.error('Failed to initialize SMTP service:', error);
      this.transporter = null;
      // Don't throw error, just log it and continue with clipboard fallback
      return false;
    }
  }

  // Test email configuration
  async testEmailConfig(config) {
    try {
      // Decrypt sensitive data if encrypted (from database)
      // If it's plain data (from test form), use as-is
      let testPassword = config.smtp_pass;
      let testFromAddress = config.email_from_address;

      if (config.smtp_pass && config.smtp_pass.length > 20) {
        // Likely encrypted, try to decrypt
        const decrypted = this.passwordService.decryptPassword(config.smtp_pass);
        if (decrypted) {
          testPassword = decrypted;
        }
      }

      if (config.email_from_address && config.email_from_address.includes(':') && !config.email_from_address.includes('@')) {
        // Likely encrypted, try to decrypt
        const decrypted = this.passwordService.decryptPassword(config.email_from_address);
        if (decrypted) {
          testFromAddress = decrypted;
        }
      }

      console.log('Creating test transporter with config:', {
        host: config.smtp_host,
        port: parseInt(config.smtp_port) || 587,
        secure: config.smtp_secure === 'true' || config.smtp_secure === true,
        hasAuth: !!(config.smtp_user && testPassword),
        fromAddress: testFromAddress ? 'PROVIDED' : 'NOT_PROVIDED'
      });

      const testTransporter = nodemailer.createTransport({
        host: config.smtp_host,
        port: parseInt(config.smtp_port) || 587,
        secure: config.smtp_secure === 'true' || config.smtp_secure === true,
        auth: config.smtp_user && testPassword ? {
          user: config.smtp_user,
          pass: testPassword
        } : undefined,
        tls: {
          rejectUnauthorized: false
        },
        debug: true, // Enable debug logging
        logger: true // Enable logger
      });

      console.log('Testing SMTP connection...');
      await testTransporter.verify();
      console.log('SMTP connection test successful');
      return { success: true, message: 'SMTP configuration is valid' };
    } catch (error) {
      console.error('SMTP test error:', {
        message: error.message,
        code: error.code,
        command: error.command,
        response: error.response
      });
      return { success: false, message: error.message, code: error.code };
    }
  }

  // Test email configuration and send test email (server-side)
  async testEmailConfigAndSend(config) {
    try {
      console.log('=== SERVER-SIDE EMAIL TEST ===');

      // Decrypt sensitive data if encrypted (from database)
      let testPassword = config.smtp_pass;
      let testFromAddress = config.email_from_address;

      console.log('=== PASSWORD DEBUG INFO ===');
      console.log('Password config status:', config.smtp_pass ? `[${config.smtp_pass.length} chars] PROVIDED` : 'NOT_PROVIDED');

      if (config.smtp_pass && config.smtp_pass.length > 20) {
        const decrypted = this.passwordService.decryptPassword(config.smtp_pass);
        if (decrypted) {
          testPassword = decrypted;
          console.log('✅ Password decryption successful');
          console.log('Using decrypted password for SMTP authentication');
        } else {
          console.log('❌ Password decryption failed - using original');
        }
      } else {
        console.log('📝 Password appears to be plain text (short length)');
      }
      console.log('=== END PASSWORD DEBUG ===');

      console.log('=== FROM ADDRESS DEBUG INFO ===');
      console.log('From address status:', config.email_from_address ? 'PROVIDED' : 'NOT_PROVIDED');

      if (config.email_from_address) {
        const decrypted = this.passwordService.decryptPassword(config.email_from_address);
        if (decrypted && decrypted !== config.email_from_address) {
          testFromAddress = decrypted;
          console.log('✅ From address decryption successful');
          console.log('Using decrypted from address for test');
        } else {
          console.log('📝 From address appears to be plain text or decryption failed');
        }
      }
      console.log('=== END FROM ADDRESS DEBUG ===');

      console.log('=== SMTP TRANSPORTER DEBUG INFO ===');
      console.log('Creating server-side test transporter with:', {
        host: config.smtp_host,
        port: parseInt(config.smtp_port) || 587,
        secure: config.smtp_secure === 'true' || config.smtp_secure === true,
        user: config.smtp_user || 'NOT_PROVIDED',
        password: testPassword ? `[${testPassword.length} chars] PROVIDED` : 'NOT_PROVIDED',
        hasAuth: !!(config.smtp_user && testPassword),
        fromAddress: testFromAddress ? 'PROVIDED' : 'NOT_PROVIDED'
      });
      console.log('=== END SMTP TRANSPORTER DEBUG ===');

      const testTransporter = nodemailer.createTransport({
        host: config.smtp_host,
        port: parseInt(config.smtp_port) || 587,
        secure: config.smtp_secure === 'true' || config.smtp_secure === true,
        auth: config.smtp_user && testPassword ? {
          user: config.smtp_user,
          pass: testPassword
        } : undefined,
        tls: {
          rejectUnauthorized: false
        },
        debug: true,
        logger: true
      });

      console.log('Testing SMTP connection...');
      await testTransporter.verify();
      console.log('SMTP connection test successful, sending test email...');

      // Generate test email content
      const testEmailHTML = this.generateTestEmailHTML({
        smtp_host: config.smtp_host,
        smtp_port: config.smtp_port,
        smtp_secure: config.smtp_secure,
        email_from_name: config.email_from_name,
        email_from_address: testFromAddress
      });

      // Parse CC addresses for test
      let ccAddresses = null;
      if (config.email_cc_addresses) {
        ccAddresses = config.email_cc_addresses
          .split(',')
          .map(email => email.trim())
          .filter(email => email.length > 0);
      }

      // Get logo attachment for better email client compatibility
      const logoAttachment = this.templateService.getTradeLinkLogoAttachment();

      const mailOptions = {
        from: `"${config.email_from_name}" <${testFromAddress}>`,
        to: config.test_email,
        subject: 'Training System - Email Configuration Test',
        html: testEmailHTML,
        attachments: logoAttachment ? [logoAttachment] : []
      };

      // Add Reply-To if configured
      if (config.email_reply_to_address) {
        mailOptions.replyTo = config.email_reply_to_address;
        console.log('Test email Reply-To address:', config.email_reply_to_address);
      }

      // Add CC if configured
      if (ccAddresses && ccAddresses.length > 0) {
        mailOptions.cc = ccAddresses;
        console.log('Test email CC recipients:', ccAddresses);
      }

      const result = await testTransporter.sendMail(mailOptions);

      console.log('Server-side test email sent successfully:', {
        messageId: result.messageId,
        accepted: result.accepted,
        rejected: result.rejected
      });

      return {
        success: true,
        message: 'Test email sent successfully via server',
        messageId: result.messageId
      };

    } catch (error) {
      console.error('Server-side email test error:', {
        message: error.message,
        code: error.code,
        command: error.command,
        response: error.response
      });
      return {
        success: false,
        message: `Server-side test failed: ${error.message}`
      };
    }
  }

  // Generate test email HTML template
  generateTestEmailHTML(config) {
    // Use CID reference for attachment
    const logoSrc = 'cid:tradelink-logo';

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Configuration Test</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; text-align: center; }
        .success { background-color: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin: 20px 0; }
        .config-details { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; text-align: center; }
        .logo-signature { margin-top: 20px; text-align: center; }
        .logo-signature img { height: 60px; width: auto; max-width: 200px; }
        ul { padding-left: 20px; }
        li { margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h2>🎉 Email Configuration Test</h2>
        <p>TradeLink Training System Server-Side Email Test</p>
    </div>

    <div class="success">
        <h3>✅ Success!</h3>
        <p>This email was sent successfully using your SMTP configuration. Your email system is working correctly!</p>
    </div>

    <p>This test email confirms that your TradeLink Training System can send emails using the configured SMTP settings.</p>

    <div class="config-details">
        <h3>📧 Configuration Details:</h3>
        <ul>
            <li><strong>SMTP Host:</strong> ${config.smtp_host}</li>
            <li><strong>SMTP Port:</strong> ${config.smtp_port}</li>
            <li><strong>Secure Connection:</strong> ${config.smtp_secure ? 'Yes (SSL/TLS)' : 'No (STARTTLS)'}</li>
            <li><strong>From Name:</strong> ${config.email_from_name}</li>
            <li><strong>From Address:</strong> ${config.email_from_address}</li>
            ${config.email_reply_to_address ? `<li><strong>Reply-To Address:</strong> ${config.email_reply_to_address}</li>` : ''}
            ${config.email_cc_addresses ? `<li><strong>CC Recipients:</strong> ${config.email_cc_addresses}</li>` : ''}
        </ul>
    </div>

    <p><strong>What this means:</strong></p>
    <ul>
        <li>✅ SMTP connection established successfully</li>
        <li>✅ Authentication (if configured) working</li>
        <li>✅ Email delivery functional</li>
        <li>✅ Server-side email system operational</li>
    </ul>

    <p>You can now use the email system for sending login credentials, notifications, and other automated emails.</p>

    <div class="footer">
        <p>This is an automated test message from the TradeLink Training System.</p>
        <div class="logo-signature">
            <img src="${logoSrc}" alt="TradeLink Logo" style="height: 60px; width: auto; max-width: 200px;" onerror="this.style.display='none'" />
        </div>
    </div>
</body>
</html>
    `.trim();
  }

  // Send email
  async sendEmail(config, to, subject, htmlContent, textContent = null, ccAddresses = null) {
    try {
      if (!config.email_enabled || config.email_method !== 'smtp') {
        return {
          success: false,
          message: 'Email sending is not enabled or configured for SMTP'
        };
      }

      if (!this.transporter) {
        return {
          success: false,
          message: 'Email transporter is not initialized - SMTP connection failed during startup'
        };
      }

      // Parse CC addresses from config or parameter
      let cc = ccAddresses;
      if (!cc && config.email_cc_addresses) {
        // Parse comma-separated CC addresses from config
        cc = config.email_cc_addresses
          .split(',')
          .map(email => email.trim())
          .filter(email => email.length > 0);
      }

      // Get logo attachment for better email client compatibility
      const logoAttachment = this.templateService.getTradeLinkLogoAttachment();

      const mailOptions = {
        from: `"${config.email_from_name}" <${config.decrypted_email_from_address || config.email_from_address}>`,
        to: to,
        subject: subject,
        html: htmlContent,
        text: textContent || htmlContent.replace(/<[^>]*>/g, ''), // Strip HTML for text version
        attachments: logoAttachment ? [logoAttachment] : []
      };

      // Add Reply-To if configured
      if (config.email_reply_to_address) {
        mailOptions.replyTo = config.email_reply_to_address;
        console.log('Adding Reply-To address:', config.email_reply_to_address);
      }

      // Add CC if provided
      if (cc && cc.length > 0) {
        mailOptions.cc = Array.isArray(cc) ? cc : [cc];
        console.log('Adding CC recipients:', mailOptions.cc);
      }

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId,
        message: 'Email sent successfully'
      };
    } catch (error) {
      console.error('Error sending email:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // Check if SMTP service is available
  isAvailable() {
    return !!this.transporter;
  }
}

module.exports = SMTPService;
