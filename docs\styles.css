/* Internal Training System Documentation Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr;
    grid-template-areas: 
        "header header"
        "nav content";
    gap: 30px;
    min-height: 100vh;
}

/* Header Styles */
.header {
    grid-area: header;
    text-align: center;
    padding: 40px 0;
    background: linear-gradient(135deg, #1890ff, #096dd9);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Navigation Styles */
.nav {
    grid-area: nav;
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.nav h3 {
    color: #1890ff;
    margin-bottom: 20px;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.nav ul {
    list-style: none;
}

.nav li {
    margin-bottom: 8px;
}

.nav a {
    color: #666;
    text-decoration: none;
    padding: 8px 12px;
    display: block;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.nav a:hover {
    background-color: #e6f7ff;
    color: #1890ff;
    transform: translateX(4px);
}

/* Content Styles */
.content {
    grid-area: content;
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section {
    margin-bottom: 60px;
}

.section:last-child {
    margin-bottom: 0;
}

/* Typography */
h2 {
    color: #1890ff;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 3px solid #e6f7ff;
}

h3 {
    color: #262626;
    font-size: 1.4rem;
    font-weight: 600;
    margin: 30px 0 15px 0;
}

h4 {
    color: #595959;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 25px 0 12px 0;
}

h5 {
    color: #8c8c8c;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 20px 0 10px 0;
}

p {
    margin-bottom: 15px;
    color: #595959;
}

ul, ol {
    margin: 15px 0 15px 25px;
    color: #595959;
}

li {
    margin-bottom: 8px;
}

/* Code Styles */
code {
    background-color: #f6f8fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #d73a49;
}

pre {
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

pre code {
    background: none;
    padding: 0;
    color: #24292e;
}

/* Special Boxes */
.info-box, .warning-box {
    padding: 20px;
    border-radius: 8px;
    margin: 25px 0;
    border-left: 4px solid;
}

.info-box {
    background-color: #e6f7ff;
    border-left-color: #1890ff;
}

.info-box h4 {
    color: #1890ff;
    margin-top: 0;
}

.warning-box {
    background-color: #fff2e8;
    border-left-color: #fa8c16;
}

.warning-box h4 {
    color: #fa8c16;
    margin-top: 0;
}

/* Technology Stack Grid */
.tech-stack {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin: 25px 0;
}

.tech-category {
    background-color: #fafafa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
}

.tech-category h4 {
    color: #1890ff;
    margin-top: 0;
    margin-bottom: 15px;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

th {
    background-color: #fafafa;
    font-weight: 600;
    color: #262626;
}

tr:hover {
    background-color: #fafafa;
}

/* Architecture Diagram */
.architecture-diagram {
    background-color: #fafafa;
    padding: 25px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e8e8e8;
}

.architecture-diagram pre {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85em;
}

/* Property Lists */
.property-list ul {
    background-color: #fafafa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
}

/* Troubleshooting Items */
.troubleshoot-item {
    background-color: #fafafa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border-left: 4px solid #52c41a;
}

.troubleshoot-item h5 {
    color: #52c41a;
    margin-top: 0;
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "header"
            "nav"
            "content";
        gap: 20px;
        padding: 15px;
    }
    
    .nav {
        position: static;
    }
    
    .content {
        padding: 25px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .tech-stack {
        grid-template-columns: 1fr;
    }
    
    pre {
        font-size: 0.8em;
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 25px 0;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .content {
        padding: 20px;
    }
    
    h2 {
        font-size: 1.6rem;
    }
    
    h3 {
        font-size: 1.3rem;
    }
}

/* Print Styles */
@media print {
    .nav {
        display: none;
    }
    
    .container {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "header"
            "content";
    }
    
    .header {
        background: #1890ff !important;
        -webkit-print-color-adjust: exact;
    }
    
    pre {
        page-break-inside: avoid;
    }
    
    .section {
        page-break-inside: avoid;
    }
}
