const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const { query, getClient } = require('../database/init');
const { requireAuth, requireAdmin } = require('../middleware/auth');
const { getVideoDurationInSeconds } = require('get-video-duration');
const { upload, handleUploadError } = require('../modules/training/upload');
const {
  chunkUpload,
  initiateUpload,
  uploadChunk,
  finalizeUpload,
  cleanupUpload,
  cancelUpload,
  pruneOrphanedChunks,
  getUploadStatus
} = require('../modules/training/chunkedUpload');
const { createModuleSchema, uploadVideoSchema, updateProgressSchema } = require('../modules/training/schemas');
const { handleVideoOptions, streamVideo } = require('../modules/training/streaming');
const {
  getAllModules,
  getAllModulesForAdmin,
  getModuleById,
  createModule,
  getVideoByModuleAndLanguage,
  upsertVideo,
  getUserProgress,
  upsertUserProgress,
  getModulesWithProgress,
  checkVideoExists,
  deleteVideo,
  getVideoByFilename,
  toggleModuleStatus,
  updateVideoLanguageName
} = require('../modules/training/queries');

const router = express.Router();



// Get all training modules
router.get('/modules', requireAuth, async (req, res) => {
  try {
    // Admin users see all modules (including inactive), regular users see only active modules
    const result = req.user.isAdmin
      ? await getAllModulesForAdmin()
      : await getAllModules();
    res.json({ modules: result.rows });
  } catch (error) {
    console.error('Get modules error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all training modules with user progress
router.get('/modules/progress', requireAuth, async (req, res) => {
  try {
    const userEmail = req.user.email;
    const result = await getModulesWithProgress(userEmail);
    res.json({ modules: result.rows });
  } catch (error) {
    console.error('Get modules with progress error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get specific module with videos and languages
router.get('/modules/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Get module details
    const moduleResult = await query(
      'SELECT * FROM training_modules WHERE id = $1 AND is_active = true',
      [id]
    );

    if (moduleResult.rows.length === 0) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    // Get available videos for this module with quiz information
    const videosResult = await query(`
      SELECT
        tv.id,
        tv.module_id,
        tv.language_code,
        tv.language_name,
        tv.video_filename,
        tv.video_url,
        tv.duration_seconds,
        tv.created_at,
        q.id as quiz_id,
        q.title as quiz_title,
        q.description as quiz_description,
        q.passing_score,
        q.time_limit_minutes,
        q.created_at as quiz_created_at
      FROM training_videos tv
      LEFT JOIN quizzes q ON tv.id = q.video_id AND q.is_active = true
      WHERE tv.module_id = $1
      ORDER BY tv.language_code
    `, [id]);

    res.json({
      module: moduleResult.rows[0],
      videos: videosResult.rows,
      quizzes: [], // Kept for backward compatibility, quiz data is now in videos
    });
  } catch (error) {
    console.error('Get module error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user's progress for a specific module
router.get('/modules/:id/progress', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const userEmail = req.user.email;

    const result = await query(`
      SELECT * FROM user_progress 
      WHERE user_email = $1 AND module_id = $2
    `, [userEmail, id]);

    res.json({ progress: result.rows });
  } catch (error) {
    console.error('Get progress error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new training module (admin only)
router.post('/modules', requireAdmin, async (req, res) => {
  try {
    // Validate input
    const { error, value } = createModuleSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    const { title, description } = value;
    const result = await createModule(title, description);

    res.status(201).json({
      message: 'Training module created successfully',
      module: result.rows[0]
    });
  } catch (error) {
    console.error('Create module error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Upload video for a module (admin only)
router.post('/modules/:id/videos', requireAdmin, upload.single('video'), handleUploadError, async (req, res) => {
  try {
    const { id } = req.params;

    if (!req.file) {
      return res.status(400).json({ error: 'No video file uploaded' });
    }

    console.log('=== UPLOAD SUCCESS ===');
    console.log('Uploaded file:', {
      originalname: req.file.originalname,
      filename: req.file.filename,
      mimetype: req.file.mimetype,
      size: `${(req.file.size / 1024 / 1024).toFixed(2)}MB`,
      path: req.file.path
    });

    // Validate other fields
    const { error, value } = uploadVideoSchema.validate(req.body);
    if (error) {
      // Clean up uploaded file
      await fs.unlink(req.file.path).catch(console.error);
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: error.details[0].message 
      });
    }

    const { moduleId, languageCode, languageName } = value;

    // Verify module exists
    const module = await getModuleById(id);
    if (!module) {
      await fs.unlink(req.file.path).catch(console.error);
      return res.status(404).json({ error: 'Training module not found' });
    }

    // Check if video already exists for this module and language
    const existingVideo = await checkVideoExists(id, languageCode.toLowerCase());
    let oldVideoFilename = null;

    if (existingVideo) {
      oldVideoFilename = existingVideo.filename;
      console.log(`Replacing existing video for module ${id}, language ${languageCode}`);
    }

    // Extract video duration
    let durationSeconds = null;
    try {
      const duration = await getVideoDurationInSeconds(req.file.path);
      durationSeconds = Math.round(duration); // Round to nearest integer for database
      console.log(`Video duration extracted: ${durationSeconds} seconds (${duration.toFixed(2)} exact)`);
    } catch (durationError) {
      console.warn('Failed to extract video duration:', durationError.message);
      // Continue without duration - it's not critical for functionality
    }

    // Use the upsert function from queries module
    const result = await upsertVideo(
      id,
      languageCode.toLowerCase(),
      languageName,
      req.file.filename,
      req.file.originalname,
      req.file.size,
      durationSeconds
    );

    // Delete old video file if it exists
    if (oldVideoFilename) {
      const oldVideoPath = path.join(__dirname, '../uploads/videos', oldVideoFilename);
      try {
        await fs.unlink(oldVideoPath);
        console.log(`Deleted old video file: ${oldVideoFilename}`);
      } catch (fileError) {
        console.warn(`Failed to delete old video file: ${oldVideoPath}`, fileError.message);
      }
    }

    const message = existingVideo ? 'Video replaced successfully' : 'Video uploaded successfully';

    res.status(existingVideo ? 200 : 201).json({
      message,
      video: result.rows[0],
      replaced: !!existingVideo
    });
  } catch (error) {
    console.error('Upload video error:', error);
    // Clean up uploaded file on error
    if (req.file) {
      await fs.unlink(req.file.path).catch(console.error);
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Chunked upload routes (admin only)

// Initiate chunked upload
router.post('/modules/:id/videos/initiate', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Verify module exists
    const module = await getModuleById(id);
    if (!module) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    // Add moduleId to request body
    req.body.moduleId = id;

    await initiateUpload(req, res);
  } catch (error) {
    console.error('Initiate chunked upload error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Upload chunk
router.post('/modules/:id/videos/chunk', requireAdmin, chunkUpload.single('chunk'), async (req, res) => {
  try {
    const { id } = req.params;

    // Verify module exists
    const module = await getModuleById(id);
    if (!module) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    await uploadChunk(req, res);
  } catch (error) {
    console.error('Upload chunk error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Finalize chunked upload
router.post('/modules/:id/videos/finalize', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { uploadId, languageCode, languageName } = req.body;

    // Verify module exists
    const module = await getModuleById(id);
    if (!module) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    // Get upload info
    const { activeUploads } = require('../modules/training/chunkedUpload');
    const uploadInfo = activeUploads.get(uploadId);
    if (!uploadInfo) {
      return res.status(404).json({ error: 'Upload session not found' });
    }

    // Check if all chunks received
    if (uploadInfo.receivedChunks.size !== uploadInfo.totalChunks) {
      return res.status(400).json({
        error: 'Incomplete upload',
        details: `Received ${uploadInfo.receivedChunks.size}/${uploadInfo.totalChunks} chunks`
      });
    }

    // Reassemble file using the chunked upload module
    const { reassembleFile } = require('../modules/training/chunkedUpload');
    const finalFilename = await reassembleFile(uploadInfo);

    // Check if video already exists for this module and language
    const existingVideo = await checkVideoExists(id, languageCode.toLowerCase());
    let oldVideoFilename = null;
    if (existingVideo) {
      oldVideoFilename = existingVideo.video_filename;
    }

    // Extract video duration from the reassembled file
    let durationSeconds = null;
    try {
      const finalPath = path.join(__dirname, '../uploads/videos', finalFilename);
      const duration = await getVideoDurationInSeconds(finalPath);
      durationSeconds = Math.round(duration); // Round to nearest integer for database
      console.log(`Video duration extracted from chunked upload: ${durationSeconds} seconds (${duration.toFixed(2)} exact)`);
    } catch (durationError) {
      console.warn('Failed to extract video duration from chunked upload:', durationError.message);
      // Continue without duration - it's not critical for functionality
    }

    // Save video to database
    const result = await upsertVideo(
      id,
      languageCode.toLowerCase(),
      languageName,
      finalFilename,
      uploadInfo.filename,
      uploadInfo.fileSize,
      durationSeconds
    );

    // Delete old video file if it exists
    if (oldVideoFilename) {
      const oldVideoPath = path.join(__dirname, '../uploads/videos', oldVideoFilename);
      try {
        await fs.unlink(oldVideoPath);
        console.log(`Deleted old video file: ${oldVideoFilename}`);
      } catch (fileError) {
        console.warn(`Failed to delete old video file: ${oldVideoPath}`, fileError.message);
      }
    }

    // Clean up upload session
    activeUploads.delete(uploadId);

    const message = existingVideo ? 'Video replaced successfully' : 'Video uploaded successfully';

    res.status(existingVideo ? 200 : 201).json({
      message,
      video: result.rows[0],
      replaced: !!existingVideo
    });

  } catch (error) {
    console.error('Finalize chunked upload error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get upload status
router.get('/modules/:id/videos/status/:uploadId', requireAdmin, getUploadStatus);

// Cancel active upload
router.post('/modules/:id/videos/cancel/:uploadId', requireAdmin, cancelUpload);

// Cleanup failed upload
router.delete('/modules/:id/videos/cleanup/:uploadId', requireAdmin, cleanupUpload);

// Simple video replacement route
const { replaceVideoSimple } = require('../modules/training/videoReplacement');

// Replace video with already uploaded file
router.post('/modules/:id/videos/:languageCode/replace', requireAdmin, async (req, res) => {
  try {
    const { id, languageCode } = req.params;

    // Verify module exists
    const module = await getModuleById(id);
    if (!module) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    await replaceVideoSimple(req, res);
  } catch (error) {
    console.error('Video replacement error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Manual cleanup of orphaned chunks (admin only)
router.post('/admin/cleanup-orphaned-chunks', requireAdmin, async (req, res) => {
  try {
    const result = await pruneOrphanedChunks();
    res.json({
      message: 'Orphaned chunk cleanup completed',
      ...result
    });
  } catch (error) {
    console.error('Manual orphaned chunk cleanup error:', error);
    res.status(500).json({
      error: 'Failed to cleanup orphaned chunks',
      details: error.message
    });
  }
});

// Update video watch progress
router.post('/modules/:id/progress', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const userEmail = req.user.email;

    console.log('=== UPDATE PROGRESS DEBUG ===');
    console.log('moduleId:', id);
    console.log('userEmail:', userEmail);
    console.log('req.body:', JSON.stringify(req.body, null, 2));

    // Validate input
    const { error, value } = updateProgressSchema.validate(req.body);
    if (error) {
      console.log('Progress validation error:', error.details[0].message);
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    const { watchTime, completed, languageCode, forceOverwrite } = value;
    console.log('Validated values:', { watchTime, completed, languageCode, forceOverwrite });

    // Verify module exists
    const module = await getModuleById(id);
    if (!module) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    // Upsert user progress using the query module
    const result = await upsertUserProgress(
      userEmail,
      id,
      languageCode || 'en',
      watchTime,
      completed || false,
      forceOverwrite || false
    );

    console.log('Database update result:', result.rows[0]);

    res.json({
      message: 'Progress updated successfully',
      progress: result.rows[0]
    });
  } catch (error) {
    console.error('Update progress error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update video language name (admin only)
router.patch('/videos/:videoId/language-name', requireAdmin, async (req, res) => {
  try {
    const { videoId } = req.params;
    const { languageName } = req.body;

    // Validate input
    if (!languageName || typeof languageName !== 'string' || languageName.trim().length === 0) {
      return res.status(400).json({ error: 'Language name is required and must be a non-empty string' });
    }

    if (languageName.length > 50) {
      return res.status(400).json({ error: 'Language name must be 50 characters or less' });
    }

    // Check if video exists
    const videoResult = await query('SELECT * FROM training_videos WHERE id = $1', [videoId]);
    if (videoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Video not found' });
    }

    // Update video language name
    const result = await updateVideoLanguageName(videoId, languageName.trim());

    res.json({
      message: 'Video language name updated successfully',
      video: result.rows[0]
    });
  } catch (error) {
    console.error('Update video language name error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete individual video (admin only)
router.delete('/videos/:videoId', requireAdmin, async (req, res) => {
  try {
    const { videoId } = req.params;

    // Get video details before deletion
    const videoResult = await query(
      'SELECT * FROM training_videos WHERE id = $1',
      [videoId]
    );

    if (videoResult.rows.length === 0) {
      return res.status(404).json({ error: 'Video not found' });
    }

    const video = videoResult.rows[0];
    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Delete associated quiz and all related data if exists
      const quizResult = await client.query(
        'SELECT id FROM quizzes WHERE video_id = $1',
        [videoId]
      );

      if (quizResult.rows.length > 0) {
        const quizId = quizResult.rows[0].id;

        // Delete quiz answers first (foreign key constraint)
        await client.query(
          'DELETE FROM quiz_answers WHERE attempt_id IN (SELECT id FROM quiz_attempts WHERE quiz_id = $1)',
          [quizId]
        );

        // Delete quiz attempts
        await client.query(
          'DELETE FROM quiz_attempts WHERE quiz_id = $1',
          [quizId]
        );

        // Delete quiz question options
        await client.query(
          'DELETE FROM quiz_question_options WHERE question_id IN (SELECT id FROM quiz_questions WHERE quiz_id = $1)',
          [quizId]
        );

        // Delete quiz questions
        await client.query(
          'DELETE FROM quiz_questions WHERE quiz_id = $1',
          [quizId]
        );

        // Delete the quiz itself
        await client.query(
          'DELETE FROM quizzes WHERE id = $1',
          [quizId]
        );
      }

      // Delete user progress for this video
      await client.query(
        'DELETE FROM user_progress WHERE module_id = $1 AND language_code = $2',
        [video.module_id, video.language_code]
      );

      // Delete certificates for this video
      await client.query(
        'DELETE FROM certificates WHERE module_id = $1 AND language_code = $2',
        [video.module_id, video.language_code]
      );

      // Delete the video record
      await client.query(
        'DELETE FROM training_videos WHERE id = $1',
        [videoId]
      );

      // Delete video file from filesystem
      if (video.video_filename) {
        const videoPath = path.join(__dirname, '../uploads/videos', video.video_filename);
        try {
          await fs.unlink(videoPath);
        } catch (fileError) {
          console.warn(`Failed to delete video file: ${videoPath}`, fileError.message);
        }
      }

      await client.query('COMMIT');

      res.json({
        message: 'Video deleted successfully',
        deletedVideo: {
          id: video.id,
          language_name: video.language_name,
          language_code: video.language_code
        }
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Delete video error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete user progress for other languages when switching (user only)
router.delete('/modules/:id/progress/other-languages', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { keepLanguageCode } = req.body;
    const userEmail = req.user.email;

    // Validate input
    if (!keepLanguageCode) {
      return res.status(400).json({ error: 'keepLanguageCode is required' });
    }

    // Verify module exists
    const moduleResult = await query(
      'SELECT id FROM training_modules WHERE id = $1 AND is_active = true',
      [id]
    );

    if (moduleResult.rows.length === 0) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    // Get languages that will be deleted for response
    const languagesToDeleteResult = await query(`
      SELECT DISTINCT language_code
      FROM user_progress
      WHERE user_email = $1 AND module_id = $2 AND language_code != $3
    `, [userEmail, id, keepLanguageCode]);

    const deletedLanguages = languagesToDeleteResult.rows.map(row => row.language_code);

    if (deletedLanguages.length > 0) {
      // Delete quiz attempt answers for other languages
      await query(`
        DELETE FROM quiz_attempt_answers
        WHERE attempt_id IN (
          SELECT qa.id
          FROM quiz_attempts qa
          JOIN quizzes q ON qa.quiz_id = q.id
          JOIN training_videos tv ON q.video_id = tv.id
          WHERE qa.user_email = $1
            AND tv.module_id = $2
            AND tv.language_code != $3
        )
      `, [userEmail, id, keepLanguageCode]);

      // Delete quiz attempts for other languages
      await query(`
        DELETE FROM quiz_attempts
        WHERE user_email = $1
          AND quiz_id IN (
            SELECT q.id
            FROM quizzes q
            JOIN training_videos tv ON q.video_id = tv.id
            WHERE tv.module_id = $2 AND tv.language_code != $3
          )
      `, [userEmail, id, keepLanguageCode]);

      // Delete certificates for other languages
      await query(`
        DELETE FROM certificates
        WHERE user_email = $1 AND module_id = $2 AND language_code != $3
      `, [userEmail, id, keepLanguageCode]);

      // Delete user progress for other languages
      await query(`
        DELETE FROM user_progress
        WHERE user_email = $1 AND module_id = $2 AND language_code != $3
      `, [userEmail, id, keepLanguageCode]);
    }

    res.json({
      message: 'Progress for other languages deleted successfully',
      deletedLanguages: deletedLanguages
    });

  } catch (error) {
    console.error('Delete other languages progress error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Toggle module active status (admin only)
router.patch('/modules/:id/toggle-status', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    // Validate input
    if (typeof isActive !== 'boolean') {
      return res.status(400).json({ error: 'isActive must be a boolean value' });
    }

    // Check if module exists
    const module = await getModuleById(id);
    if (!module) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    // Toggle module status
    const result = await toggleModuleStatus(id, isActive);

    res.json({
      message: `Module ${isActive ? 'activated' : 'deactivated'} successfully`,
      module: result.rows[0]
    });
  } catch (error) {
    console.error('Toggle module status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete training module (admin only)
router.delete('/modules/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if module exists (regardless of active status)
    const moduleResult = await query(
      'SELECT id, title, is_active FROM training_modules WHERE id = $1',
      [id]
    );

    if (moduleResult.rows.length === 0) {
      return res.status(404).json({ error: 'Training module not found' });
    }

    const module = moduleResult.rows[0];
    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Get all video files to delete from filesystem
      const videosResult = await client.query(
        'SELECT video_filename FROM training_videos WHERE module_id = $1',
        [id]
      );

      // Delete video files from filesystem
      for (const video of videosResult.rows) {
        if (video.video_filename) {
          const videoPath = path.join(__dirname, '../uploads/videos', video.video_filename);
          try {
            await fs.unlink(videoPath);
          } catch (fileError) {
            console.warn(`Failed to delete video file: ${videoPath}`, fileError.message);
          }
        }
      }

      if (module.is_active) {
        // If module is active, perform soft delete (set is_active to false)
        await client.query(
          'UPDATE training_modules SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
          [id]
        );
      } else {
        // If module is inactive, perform hard delete (completely remove from database)

        // Delete user progress for this module
        await client.query(
          'DELETE FROM user_progress WHERE module_id = $1',
          [id]
        );

        // Delete certificates for this module
        await client.query(
          'DELETE FROM certificates WHERE module_id = $1',
          [id]
        );

        // Delete quiz attempts for quizzes associated with this module's videos
        // This ensures user quiz attempt data is properly cleaned up
        await client.query(`
          DELETE FROM quiz_attempts
          WHERE quiz_id IN (
            SELECT q.id FROM quizzes q
            JOIN training_videos tv ON q.video_id = tv.id
            WHERE tv.module_id = $1
          )
        `, [id]);

        // Delete training videos (this will cascade to related quiz data)
        // The CASCADE relationships will automatically delete:
        // - quizzes (via video_id FK)
        // - quiz_questions (via quiz_id FK)
        // - quiz_question_options (via question_id FK)
        await client.query(
          'DELETE FROM training_videos WHERE module_id = $1',
          [id]
        );

        // Finally, delete the training module itself
        await client.query(
          'DELETE FROM training_modules WHERE id = $1',
          [id]
        );
      }

      await client.query('COMMIT');

      const action = module.is_active ? 'deactivated' : 'permanently deleted';
      res.json({
        message: `Training module ${action} successfully`,
        moduleId: id,
        action: module.is_active ? 'soft_delete' : 'hard_delete'
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Delete module error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Handle OPTIONS requests for video streaming
router.options('/video/:filename', handleVideoOptions);

// Stream video file with proper CORS headers
router.get('/video/:filename', streamVideo);

module.exports = router;
