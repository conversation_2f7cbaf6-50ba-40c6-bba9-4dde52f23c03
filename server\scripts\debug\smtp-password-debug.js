/**
 * SMTP Password Encryption/Decryption Debug Tool
 * 
 * This script specifically tests the SMTP password storage and retrieval mechanism
 * to identify corruption issues similar to those found in email templates.
 */

const { query } = require('../../database/init');
const PasswordEncryptionService = require('../../services/email/PasswordEncryptionService');

class SMTPPasswordDebugger {
  constructor() {
    this.passwordService = new PasswordEncryptionService();
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': '📋',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️',
      'debug': '🔍'
    }[type] || '📋';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  // Test password encryption/decryption with various character sets
  async testPasswordEncryptionDecryption() {
    this.log('=== PASSWORD ENCRYPTION/DECRYPTION TEST ===', 'info');
    
    const testPasswords = [
      'SimplePassword123',
      'Complex!@#$%^&*()Password',
      'Password<with>HTML&chars"',
      'Password{with}template{{syntax}}',
      'Password\\with/slashes',
      'Password`with`backticks',
      'PasswordWithUnicode测试密码',
      'Very$pecial!Ch@rs#123',
      'Base64==Like==Password',
      'Password:with:colons'
    ];

    let allPassed = true;
    const results = [];

    for (const originalPassword of testPasswords) {
      try {
        this.log(`Testing password: "${originalPassword}"`, 'debug');
        
        // Test encryption
        const encrypted = this.passwordService.encryptPassword(originalPassword);
        this.log(`  Encrypted: "${encrypted}" (${encrypted.length} chars)`, 'debug');
        
        // Test decryption
        const decrypted = this.passwordService.decryptPassword(encrypted);
        this.log(`  Decrypted: "${decrypted}" (${decrypted.length} chars)`, 'debug');
        
        // Check if they match
        const matches = originalPassword === decrypted;
        this.log(`  Match: ${matches ? '✅' : '❌'}`, matches ? 'success' : 'error');
        
        // Check for corruption patterns
        const hasCorruption = this.checkForCorruption(originalPassword, decrypted);
        
        const result = {
          original: originalPassword,
          encrypted: encrypted,
          decrypted: decrypted,
          matches: matches,
          hasCorruption: hasCorruption,
          success: matches && !hasCorruption
        };
        
        results.push(result);
        
        if (!result.success) {
          allPassed = false;
          this.log(`  ❌ FAILURE: ${hasCorruption ? 'Corruption detected' : 'Password mismatch'}`, 'error');
        }
        
      } catch (error) {
        this.log(`  ❌ ERROR: ${error.message}`, 'error');
        allPassed = false;
        results.push({
          original: originalPassword,
          encrypted: null,
          decrypted: null,
          matches: false,
          hasCorruption: true,
          success: false,
          error: error.message
        });
      }
    }

    this.log(`\n=== ENCRYPTION/DECRYPTION SUMMARY ===`, 'info');
    this.log(`Total tests: ${results.length}`, 'info');
    this.log(`Successful: ${results.filter(r => r.success).length}`, 'success');
    this.log(`Failed: ${results.filter(r => !r.success).length}`, 'error');
    
    return { allPassed, results };
  }

  // Check for corruption patterns
  checkForCorruption(original, decrypted) {
    if (!decrypted) return true;
    
    // Check for HTML entity corruption
    if (decrypted.includes('&lt;') || decrypted.includes('&gt;') || decrypted.includes('&amp;')) {
      return true;
    }
    
    // Check for template syntax corruption
    if (original.includes('{{') && !decrypted.includes('{{')) {
      return true;
    }
    
    // Check for character encoding issues
    if (original.length !== decrypted.length) {
      return true;
    }
    
    // Check for base64 corruption
    if (decrypted.includes('=') && !original.includes('=')) {
      return true;
    }
    
    return false;
  }

  // Test current database SMTP configuration
  async testCurrentSMTPConfig() {
    this.log('=== CURRENT SMTP CONFIGURATION TEST ===', 'info');
    
    try {
      const result = await query('SELECT smtp_pass, email_from_address, smtp_user, smtp_host FROM email_config WHERE id = 1');
      
      if (result.rows.length === 0) {
        this.log('No SMTP configuration found in database', 'warning');
        return { success: false, error: 'No configuration found' };
      }

      const config = result.rows[0];
      
      this.log('Current SMTP configuration:', 'info');
      this.log(`  Host: ${config.smtp_host}`, 'info');
      this.log(`  User: ${config.smtp_user}`, 'info');
      this.log(`  Password: ${config.smtp_pass ? `[${config.smtp_pass.length} chars] PROVIDED` : 'NOT_SET'}`, 'info');
      this.log(`  From Address: ${config.email_from_address ? 'PROVIDED' : 'NOT_SET'}`, 'info');

      // Test password decryption
      if (config.smtp_pass) {
        this.log('\n--- Password Analysis ---', 'debug');
        
        const isEncrypted = this.passwordService.isPasswordEncrypted(config.smtp_pass);
        this.log(`  Is encrypted: ${isEncrypted}`, 'debug');
        
        if (isEncrypted) {
          const decrypted = this.passwordService.decryptPassword(config.smtp_pass);
          this.log(`  Decrypted length: ${decrypted ? decrypted.length : 0}`, 'debug');
          this.log(`  Decryption successful: ${decrypted ? '✅' : '❌'}`, decrypted ? 'success' : 'error');
          
          // Check for corruption patterns
          if (decrypted) {
            const hasCorruption = this.checkForCorruption('test', decrypted);
            this.log(`  Has corruption patterns: ${hasCorruption ? '❌' : '✅'}`, hasCorruption ? 'error' : 'success');
            
            // Check for specific corruption patterns
            if (decrypted.includes('<') || decrypted.includes('>')) {
              this.log(`  ⚠️  Contains HTML tags: ${decrypted.includes('<')} / ${decrypted.includes('>')}`, 'warning');
            }
            if (decrypted.includes('&')) {
              this.log(`  ⚠️  Contains HTML entities`, 'warning');
            }
            if (decrypted.includes('{{') || decrypted.includes('}}')) {
              this.log(`  ⚠️  Contains template syntax`, 'warning');
            }
          }
        } else {
          this.log(`  Password appears to be plain text`, 'warning');
        }
      }

      // Test from address decryption
      if (config.email_from_address) {
        this.log('\n--- From Address Analysis ---', 'debug');
        
        const isEncrypted = this.passwordService.isPasswordEncrypted(config.email_from_address);
        this.log(`  Is encrypted: ${isEncrypted}`, 'debug');
        
        if (isEncrypted) {
          const decrypted = this.passwordService.decryptPassword(config.email_from_address);
          this.log(`  Decrypted: ${decrypted}`, 'debug');
          this.log(`  Decryption successful: ${decrypted ? '✅' : '❌'}`, decrypted ? 'success' : 'error');
        } else {
          this.log(`  From address appears to be plain text: ${config.email_from_address}`, 'info');
        }
      }

      return { success: true, config };
      
    } catch (error) {
      this.log(`Database error: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  // Test SMTP authentication with current config
  async testSMTPAuthentication() {
    this.log('=== SMTP AUTHENTICATION TEST ===', 'info');
    
    try {
      const configResult = await this.testCurrentSMTPConfig();
      if (!configResult.success) {
        return configResult;
      }

      const config = configResult.config;
      
      if (!config.smtp_pass) {
        this.log('No SMTP password configured', 'warning');
        return { success: false, error: 'No password configured' };
      }

      // Decrypt password for authentication test
      const decryptedPassword = this.passwordService.decryptPassword(config.smtp_pass);
      
      if (!decryptedPassword) {
        this.log('❌ Password decryption failed - this is likely the cause of SMTP auth failure', 'error');
        return { success: false, error: 'Password decryption failed' };
      }

      this.log(`✅ Password decryption successful`, 'success');
      this.log(`Decrypted password length: ${decryptedPassword.length}`, 'debug');
      
      // Check for problematic characters that might cause SMTP auth issues
      const problematicChars = ['<', '>', '&', '"', "'", '{', '}', '`', '\\', '/'];
      const hasProblematic = problematicChars.some(char => decryptedPassword.includes(char));
      
      if (hasProblematic) {
        this.log('⚠️  Decrypted password contains potentially problematic characters', 'warning');
        problematicChars.forEach(char => {
          if (decryptedPassword.includes(char)) {
            this.log(`    Contains: ${char}`, 'warning');
          }
        });
      } else {
        this.log('✅ Decrypted password appears clean (no problematic characters)', 'success');
      }

      return { 
        success: true, 
        passwordDecrypted: true, 
        hasProblematicChars: hasProblematic,
        passwordLength: decryptedPassword.length
      };
      
    } catch (error) {
      this.log(`SMTP authentication test error: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  // Run all tests
  async runAllTests() {
    this.log('Starting SMTP password debugging session', 'info');
    
    const encryptionTest = await this.testPasswordEncryptionDecryption();
    const configTest = await this.testCurrentSMTPConfig();
    const authTest = await this.testSMTPAuthentication();
    
    this.log('\n=== FINAL DIAGNOSIS ===', 'info');
    
    if (!encryptionTest.allPassed) {
      this.log('❌ Password encryption/decryption system has issues', 'error');
      this.log('   This could cause SMTP authentication failures', 'error');
    } else {
      this.log('✅ Password encryption/decryption system works correctly', 'success');
    }
    
    if (!configTest.success) {
      this.log('❌ Current SMTP configuration has issues', 'error');
    } else {
      this.log('✅ SMTP configuration loaded successfully', 'success');
    }
    
    if (!authTest.success) {
      this.log('❌ SMTP authentication test failed', 'error');
      if (authTest.error === 'Password decryption failed') {
        this.log('   🔍 ROOT CAUSE: Password decryption is failing', 'error');
        this.log('   💡 SOLUTION: Re-enter SMTP password in admin panel', 'info');
      }
    } else {
      this.log('✅ SMTP authentication test passed', 'success');
      if (authTest.hasProblematicChars) {
        this.log('   ⚠️  WARNING: Password contains characters that might cause issues', 'warning');
      }
    }
    
    // Provide recommendations
    this.log('\n=== RECOMMENDATIONS ===', 'info');
    
    if (!authTest.success || authTest.hasProblematicChars) {
      this.log('1. Re-enter SMTP password in admin panel to ensure clean encryption', 'info');
      this.log('2. Avoid special characters like < > & " \' { } in SMTP passwords', 'info');
      this.log('3. Test email configuration after updating password', 'info');
    } else {
      this.log('SMTP password system appears to be working correctly', 'success');
    }
  }
}

// Main execution
async function main() {
  const smtpDebugger = new SMTPPasswordDebugger();

  try {
    await smtpDebugger.runAllTests();
  } catch (error) {
    console.error('❌ Fatal error:', error);
  }

  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = SMTPPasswordDebugger;
