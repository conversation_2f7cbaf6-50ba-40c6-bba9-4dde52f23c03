import React, { createContext, useContext, useReducer, useEffect, useMemo, useCallback } from 'react'
import { useQueryClient } from 'react-query'
import { authAPI, handleAPIError, setToken, getToken } from '../services/api'
import { message } from 'antd'
import { useTranslation } from 'react-i18next'

// Auth state
const initialState = {
  user: null,
  loading: true,
  error: null,
}

// Auth actions
const authActions = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT',
}

// Auth reducer
function authReducer(state, action) {
  switch (action.type) {
    case authActions.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      }
    case authActions.SET_USER:
      return {
        ...state,
        user: action.payload,
        loading: false,
        error: null,
      }
    case authActions.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      }
    case authActions.LOGOUT:
      return {
        ...state,
        user: null,
        loading: false,
        error: null,
      }
    default:
      return state
  }
}

// Create Auth Context
const AuthContext = createContext()

// Auth Provider Component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState)
  const queryClient = useQueryClient()
  const { i18n } = useTranslation()

  // Function to sync user language with i18n - memoized to prevent recreating on every render
  const syncUserLanguage = useCallback(async (userLanguage) => {
    if (userLanguage && userLanguage !== i18n.language) {
      try {
        await i18n.changeLanguage(userLanguage)
        localStorage.setItem('i18nextLng', userLanguage)
        if (process.env.NODE_ENV === 'development') {
          console.log(`Language synced to user preference: ${userLanguage}`)
        }
      } catch (error) {
        console.error('Failed to sync user language:', error)
      }
    }
  }, [i18n])

  // Memoized checkAuthStatus function to prevent unnecessary recreations
  const checkAuthStatus = useCallback(async () => {
    try {
      dispatch({ type: authActions.SET_LOADING, payload: true })

      // Check if we have a token
      const token = getToken();
      if (!token) {
        dispatch({ type: authActions.LOGOUT })
        return;
      }

      const response = await authAPI.checkStatus()

      if (response.data.authenticated) {
        const user = response.data.user
        dispatch({ type: authActions.SET_USER, payload: user })
        // Sync user's language preference
        if (user.language) {
          await syncUserLanguage(user.language)
        }
      } else {
        setToken(null); // Clear invalid token
        dispatch({ type: authActions.LOGOUT })
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      setToken(null); // Clear invalid token
      dispatch({ type: authActions.LOGOUT })
    }
  }, [syncUserLanguage])

  // Define refreshUser function first before using it in useEffect
  const refreshUser = useCallback(async () => {
    try {
      const response = await authAPI.checkStatus()
      if (response.data.authenticated) {
        const user = response.data.user
        dispatch({ type: authActions.SET_USER, payload: user })
        return user
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error)
    }
    return null
  }, [])

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus()
  }, [checkAuthStatus])

  // Listen for language change events and refresh user data
  useEffect(() => {
    const handleLanguageChange = async (event) => {
      console.log('Language change event received:', event.detail.language)
      await refreshUser()
      console.log('User data refreshed after language change')
    }

    window.addEventListener('languageChanged', handleLanguageChange)
    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange)
    }
  }, [refreshUser])

  const login = useCallback(async (credentials) => {
    try {
      dispatch({ type: authActions.SET_LOADING, payload: true })
      const response = await authAPI.login(credentials)

      // Store the JWT token
      if (response.data.token) {
        setToken(response.data.token);
      }

      // Clear any existing cache to ensure fresh data for the new user
      queryClient.clear()

      const user = response.data.user
      dispatch({ type: authActions.SET_USER, payload: user })

      // Sync user's language preference immediately on login
      if (user.language) {
        await syncUserLanguage(user.language)
      }

      message.success('Login successful!')
      return { success: true }
    } catch (error) {
      const errorInfo = handleAPIError(error)
      dispatch({ type: authActions.SET_ERROR, payload: errorInfo.message })
      message.error(errorInfo.message)
      return { success: false, error: errorInfo.message }
    }
  }, [queryClient, syncUserLanguage])

  const logout = useCallback(async () => {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear the JWT token
      setToken(null);

      // Clear all React Query cache to prevent data leakage between users
      queryClient.clear()

      dispatch({ type: authActions.LOGOUT })
      message.success('Logged out successfully')
    }
  }, [queryClient])

  const changePassword = useCallback(async (passwordData) => {
    try {
      await authAPI.changePassword(passwordData)
      message.success('Password changed successfully!')
      return { success: true }
    } catch (error) {
      const errorInfo = handleAPIError(error)
      message.error(errorInfo.message)
      return { success: false, error: errorInfo.message }
    }
  }, [])

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    user: state.user,
    loading: state.loading,
    error: state.error,
    login,
    logout,
    changePassword,
    checkAuthStatus,
    refreshUser,
  }), [state.user, state.loading, state.error, login, logout, changePassword, checkAuthStatus, refreshUser])

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
