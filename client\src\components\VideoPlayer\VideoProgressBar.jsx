/**
 * Video Progress Bar Component
 * Extracted from VideoPlayer.jsx for better modularity
 */

import React, { useState, useRef, useCallback, useMemo } from 'react'
import { Tooltip } from 'antd'
import { useTranslation } from 'react-i18next'
import { formatTime, calculateVideoProgress } from '../../utils/videoHelpers'

const VideoProgressBar = React.memo(({
  watchTime,
  maxWatched,
  duration,
  onSeek,
  videoCompleted,
  hasCompletedBefore,
  adminControlsActive
}) => {
  const { t } = useTranslation()
  const [hoverTime, setHoverTime] = useState(null)
  const [isHovering, setIsHovering] = useState(false)
  const progressBarRef = useRef(null)

  const handleClick = useCallback((e) => {
    if (!progressBarRef.current || duration <= 0) return

    const rect = progressBarRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const clickPercent = clickX / rect.width
    const targetTime = clickPercent * duration

    onSeek(targetTime)
  }, [duration, onSeek])

  const handleMouseMove = useCallback((e) => {
    if (!progressBarRef.current || duration <= 0) return

    const rect = progressBarRef.current.getBoundingClientRect()
    const mouseX = e.clientX - rect.left
    const mousePercent = Math.max(0, Math.min(1, mouseX / rect.width))
    const targetTime = mousePercent * duration

    setHoverTime(targetTime)
  }, [duration])

  const handleMouseEnter = useCallback(() => {
    setIsHovering(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovering(false)
    setHoverTime(null)
  }, [])

  // Calculate percentages - memoized to prevent recalculation on every render
  const { watchedPercent, maxWatchedPercent, hoverPercent, canSeekToEnd, seekablePercent } = useMemo(() => {
    return calculateVideoProgress(duration, watchTime, maxWatched, hoverTime, hasCompletedBefore, adminControlsActive)
  }, [duration, watchTime, maxWatched, hoverTime, hasCompletedBefore, adminControlsActive])

  return (
    <div style={{ position: 'relative', marginBottom: '16px' }}>
      {/* Progress Info */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '8px',
        fontSize: '14px',
        color: '#666'
      }}>
        <span>{formatTime(watchTime)}</span>
        <span>{formatTime(duration)}</span>
      </div>

      {/* Progress Bar */}
      <Tooltip
        title={hoverTime !== null ? formatTime(hoverTime) : ''}
        open={isHovering && hoverTime !== null}
        placement="top"
        getPopupContainer={() => progressBarRef.current || document.body}
      >
        <div
          ref={progressBarRef}
          onClick={handleClick}
          onMouseMove={handleMouseMove}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          style={{
            width: '100%',
            height: '8px',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px',
            cursor: 'pointer',
            position: 'relative',
            border: '1px solid #d9d9d9'
          }}
        >
          {/* Background track */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              height: '100%',
              width: '100%',
              backgroundColor: '#f0f0f0',
              borderRadius: '4px'
            }}
          />

          {/* Seekable portion (what user can click on) */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              height: '100%',
              width: `${seekablePercent}%`,
              backgroundColor: canSeekToEnd ? '#e6f7ff' : '#fff2e8',
              borderRadius: '4px',
              transition: 'all 0.2s ease'
            }}
          />

          {/* Watched portion */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              height: '100%',
              width: `${Math.min(watchedPercent, 100)}%`,
              backgroundColor: videoCompleted ? '#52c41a' : '#1890ff',
              borderRadius: '4px',
              transition: 'width 0.3s ease'
            }}
          />

          {/* Hover indicator */}
          {isHovering && hoverPercent !== null && (
            <div
              style={{
                position: 'absolute',
                top: '-2px',
                left: `${hoverPercent}%`,
                width: '2px',
                height: 'calc(100% + 4px)',
                backgroundColor: '#1890ff',
                borderRadius: '1px',
                transform: 'translateX(-50%)',
                pointerEvents: 'none'
              }}
            />
          )}
        </div>
      </Tooltip>

      {/* Progress Status */}
      <div style={{ 
        marginTop: '8px',
        fontSize: '12px',
        color: '#999',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <span>
          {t('components.videoPlayer.progress.watchedProgress')}: {Math.round(watchedPercent)}%
        </span>
        {!canSeekToEnd && (
          <span>
            {t('components.videoPlayer.progress.restrictedArea')}
          </span>
        )}
        {videoCompleted && (
          <span style={{ color: '#52c41a' }}>
            ✓ {t('trainingModules.completed')}
          </span>
        )}
      </div>
    </div>
  )
})

VideoProgressBar.displayName = 'VideoProgressBar'

export default VideoProgressBar
