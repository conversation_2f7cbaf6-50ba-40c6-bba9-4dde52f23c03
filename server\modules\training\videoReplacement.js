const path = require('path');
const fs = require('fs').promises;
const multer = require('multer');
const { getVideoDurationInSeconds } = require('get-video-duration');
const { query, getClient } = require('../../database/init');
const { getModuleById, getVideoByModuleAndLanguage, upsertVideo } = require('./queries');
const systemConfigService = require('../../services/systemConfigService');

// Configure multer for video replacement uploads
const replacementStorage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/videos');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `video-replacement-${uniqueSuffix}${ext}`);
  }
});

const replacementUpload = multer({
  storage: replacementStorage,
  limits: {
    fileSize: systemConfigService.getMaxFileSizeSync(),
  },
  fileFilter: (req, file, cb) => {
    console.log('=== VIDEO REPLACEMENT FILE FILTER ===');
    console.log('Original filename:', file.originalname);
    console.log('MIME type:', file.mimetype);

    const allowedExtensions = /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i;
    const allowedMimeTypes = /^video\/(mp4|avi|quicktime|x-msvideo|x-ms-wmv|x-flv|webm|x-matroska|mp4v-es|3gpp|3gpp2)$/i;

    const filename = file.originalname.toLowerCase();
    const mimetype = file.mimetype.toLowerCase();

    if (!allowedExtensions.test(filename)) {
      console.log('File rejected: Invalid extension');
      return cb(new Error('Invalid file extension. Only video files are allowed.'), false);
    }

    if (!allowedMimeTypes.test(mimetype)) {
      console.log('File rejected: Invalid MIME type');
      return cb(new Error('Invalid file type. Only video files are allowed.'), false);
    }

    console.log('File accepted for replacement');
    cb(null, true);
  }
});

/**
 * Handle video replacement upload errors
 */
function handleReplacementUploadError(err, req, res, next) {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      const maxSizeMB = Math.round(systemConfigService.getMaxFileSizeSync() / (1024 * 1024));
      return res.status(400).json({
        error: 'File too large',
        details: `Maximum file size is ${maxSizeMB}MB`
      });
    }
    return res.status(400).json({
      error: 'Upload error',
      details: err.message
    });
  }

  if (err) {
    return res.status(400).json({
      error: 'File validation failed',
      details: err.message
    });
  }

  next();
}

/**
 * Get current video information for replacement validation
 */
async function getCurrentVideoInfo(moduleId, languageCode) {
  try {
    const video = await getVideoByModuleAndLanguage(moduleId, languageCode);
    if (!video) {
      return null;
    }

    // Check if video file exists on filesystem
    const videoPath = path.join(__dirname, '../../uploads/videos', video.video_filename);
    let fileExists = false;
    let fileSize = 0;

    try {
      const stats = await fs.stat(videoPath);
      fileExists = true;
      fileSize = stats.size;
    } catch (error) {
      console.warn(`Video file not found on filesystem: ${videoPath}`);
    }

    return {
      ...video,
      file_exists: fileExists,
      file_size: fileSize,
      file_path: videoPath
    };
  } catch (error) {
    console.error('Error getting current video info:', error);
    throw error;
  }
}

/**
 * Perform atomic video file replacement
 */
async function performAtomicReplacement(currentVideo, newFile, languageName) {
  const client = await getClient();
  let tempFilePath = null;
  let backupFilePath = null;

  try {
    await client.query('BEGIN');

    // Extract video duration from new file
    let durationSeconds = null;
    try {
      const duration = await getVideoDurationInSeconds(newFile.path);
      durationSeconds = Math.round(duration);
      console.log(`New video duration: ${durationSeconds} seconds`);
    } catch (durationError) {
      console.warn('Failed to extract video duration:', durationError.message);
    }

    // Create backup of current file if it exists
    if (currentVideo.file_exists) {
      backupFilePath = `${currentVideo.file_path}.backup-${Date.now()}`;
      await fs.rename(currentVideo.file_path, backupFilePath);
      console.log(`Created backup: ${backupFilePath}`);
    }

    // Move new file to final location
    const finalFilename = `video-${Date.now()}-${Math.round(Math.random() * 1E9)}${path.extname(newFile.originalname)}`;
    const finalPath = path.join(__dirname, '../../uploads/videos', finalFilename);
    await fs.rename(newFile.path, finalPath);
    tempFilePath = finalPath;

    // Update database record
    const result = await upsertVideo(
      currentVideo.module_id,
      currentVideo.language_code,
      languageName || currentVideo.language_name,
      finalFilename,
      newFile.originalname,
      newFile.size,
      durationSeconds
    );

    await client.query('COMMIT');

    // Delete backup file after successful commit
    if (backupFilePath) {
      try {
        await fs.unlink(backupFilePath);
        console.log(`Deleted backup file: ${backupFilePath}`);
      } catch (error) {
        console.warn(`Failed to delete backup file: ${backupFilePath}`, error.message);
      }
    }

    return {
      success: true,
      video: result.rows[0],
      oldFilename: currentVideo.video_filename,
      newFilename: finalFilename
    };

  } catch (error) {
    await client.query('ROLLBACK');

    // Restore backup if it exists
    if (backupFilePath && currentVideo.file_exists) {
      try {
        await fs.rename(backupFilePath, currentVideo.file_path);
        console.log(`Restored backup: ${currentVideo.file_path}`);
      } catch (restoreError) {
        console.error(`Failed to restore backup: ${restoreError.message}`);
      }
    }

    // Clean up temporary file
    if (tempFilePath) {
      try {
        await fs.unlink(tempFilePath);
      } catch (cleanupError) {
        console.warn(`Failed to cleanup temp file: ${tempFilePath}`, cleanupError.message);
      }
    }

    throw error;
  } finally {
    client.release();
  }
}

/**
 * Replace video file endpoint (single upload)
 */
async function replaceVideoFile(req, res) {
  const upload = replacementUpload.single('video');
  
  upload(req, res, async (uploadErr) => {
    if (uploadErr) {
      return handleReplacementUploadError(uploadErr, req, res, () => {});
    }

    try {
      const { id: moduleId, languageCode } = req.params;
      const { languageName } = req.body;

      if (!req.file) {
        return res.status(400).json({ error: 'No video file provided for replacement' });
      }

      console.log('=== VIDEO REPLACEMENT REQUEST ===');
      console.log('Module ID:', moduleId);
      console.log('Language Code:', languageCode);
      console.log('New file:', {
        originalname: req.file.originalname,
        filename: req.file.filename,
        size: `${(req.file.size / 1024 / 1024).toFixed(2)}MB`,
        mimetype: req.file.mimetype
      });

      // Verify module exists
      const module = await getModuleById(moduleId);
      if (!module) {
        await fs.unlink(req.file.path).catch(console.error);
        return res.status(404).json({ error: 'Training module not found' });
      }

      // Get current video information
      const currentVideo = await getCurrentVideoInfo(moduleId, languageCode.toLowerCase());
      if (!currentVideo) {
        await fs.unlink(req.file.path).catch(console.error);
        return res.status(404).json({ 
          error: 'Video not found',
          details: `No video exists for module ${moduleId} in language ${languageCode}`
        });
      }

      // Perform atomic replacement
      const result = await performAtomicReplacement(currentVideo, req.file, languageName);

      res.json({
        message: 'Video replaced successfully',
        video: result.video,
        replacement_info: {
          old_filename: result.oldFilename,
          new_filename: result.newFilename,
          module_id: moduleId,
          language_code: languageCode
        }
      });

    } catch (error) {
      console.error('Video replacement error:', error);
      
      // Clean up uploaded file on error
      if (req.file) {
        await fs.unlink(req.file.path).catch(console.error);
      }

      res.status(500).json({ 
        error: 'Video replacement failed',
        details: error.message
      });
    }
  });
}

// Note: Chunked replacement is now handled through individual route handlers in training.js

/**
 * Simple video replacement using existing upload system
 */
async function replaceVideoSimple(req, res) {
  const client = await getClient();
  let newVideoPath = null;
  let oldVideoPath = null;

  try {
    const { id: moduleId, languageCode } = req.params;
    const { newVideoFilename, languageName } = req.body;

    console.log('=== SIMPLE VIDEO REPLACEMENT ===');
    console.log('Module ID:', moduleId);
    console.log('Language Code:', languageCode);
    console.log('New Video Filename:', newVideoFilename);
    console.log('Language Name:', languageName);

    await client.query('BEGIN');

    // Get current video information
    const currentVideo = await getCurrentVideoInfo(moduleId, languageCode.toLowerCase());
    if (!currentVideo) {
      return res.status(404).json({
        error: 'Video not found',
        details: `No video exists for module ${moduleId} in language ${languageCode}`
      });
    }

    // Verify new video file exists
    newVideoPath = path.join(__dirname, '../../uploads/videos', newVideoFilename);
    try {
      const stats = await fs.stat(newVideoPath);
      console.log(`New video file verified: ${newVideoFilename} (${stats.size} bytes)`);
    } catch (error) {
      return res.status(400).json({
        error: 'New video file not found',
        details: `File ${newVideoFilename} does not exist in uploads directory`
      });
    }

    // Get video duration from the new file
    let durationSeconds = null;
    try {
      const duration = await getVideoDurationInSeconds(newVideoPath);
      durationSeconds = Math.round(duration);
      console.log(`New video duration: ${durationSeconds} seconds`);
    } catch (durationError) {
      console.warn('Failed to extract video duration:', durationError.message);
    }

    // Get file size
    const stats = await fs.stat(newVideoPath);
    const fileSize = stats.size;

    // Update database record to point to new file
    const videoUrl = `/uploads/videos/${newVideoFilename}`;
    const result = await client.query(`
      UPDATE training_videos
      SET
        video_filename = $1,
        video_url = $2,
        duration_seconds = $3,
        language_name = $4,
        updated_at = CURRENT_TIMESTAMP
      WHERE module_id = $5 AND language_code = $6
      RETURNING *
    `, [newVideoFilename, videoUrl, durationSeconds, languageName || currentVideo.language_name, moduleId, languageCode.toLowerCase()]);

    if (result.rows.length === 0) {
      throw new Error('Failed to update video record');
    }

    await client.query('COMMIT');

    // Delete old video file if it exists and is different from new file
    if (currentVideo.video_filename && currentVideo.video_filename !== newVideoFilename) {
      oldVideoPath = path.join(__dirname, '../../uploads/videos', currentVideo.video_filename);
      try {
        await fs.unlink(oldVideoPath);
        console.log(`Deleted old video file: ${currentVideo.video_filename}`);
      } catch (fileError) {
        console.warn(`Failed to delete old video file: ${oldVideoPath}`, fileError.message);
      }
    }

    console.log('Video replacement completed successfully:', {
      oldFilename: currentVideo.video_filename,
      newFilename: newVideoFilename,
      moduleId: moduleId,
      languageCode: languageCode
    });

    res.json({
      message: 'Video replaced successfully',
      video: result.rows[0],
      replacement_info: {
        old_filename: currentVideo.video_filename,
        new_filename: newVideoFilename,
        module_id: moduleId,
        language_code: languageCode,
        file_size: fileSize,
        duration_seconds: durationSeconds
      },
      replaced: true
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Simple video replacement failed:', error);

    // Clean up new video file on error (optional - might want to keep it)
    // if (newVideoPath) {
    //   try {
    //     await fs.unlink(newVideoPath);
    //   } catch (cleanupError) {
    //     console.warn(`Failed to cleanup new video file: ${newVideoPath}`, cleanupError.message);
    //   }
    // }

    res.status(500).json({
      error: 'Video replacement failed',
      details: error.message
    });
  } finally {
    client.release();
  }
}

// Note: Complex chunked replacement functions removed in favor of simpler approach

module.exports = {
  replaceVideoFile,
  getCurrentVideoInfo,
  performAtomicReplacement,
  replaceVideoSimple
};
