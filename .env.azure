# =============================================================================
# AZURE CONTAINERIZED DEPLOYMENT - PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================

# =============================================================================
# DEPLOYMENT ENVIRONMENT
# =============================================================================
NODE_ENV=production
DEPLOYMENT_TYPE=azure-container-instances

# =============================================================================
# APPLICATION PORTS - AZURE OPTIMIZED
# =============================================================================
# Use Azure standard ports
FRONTEND_PORT=8080
BACKEND_PORT=8080
FRONTEND_DEV_PORT=3000

# =============================================================================
# API CONFIGURATION - INTERNAL SERVICE DISCOVERY
# =============================================================================
# Use internal service names for container-to-container communication
REACT_APP_API_URL=http://backend:8080/api

# =============================================================================
# DATABASE CONFIGURATION - AZURE DATABASE FOR POSTGRESQL
# =============================================================================
# Use Azure Database for PostgreSQL Flexible Server
DB_HOST=your-postgres-server.postgres.database.azure.com
DB_PORT=5432
DB_NAME=training_system
DB_USER=training_user@your-postgres-server
DB_PASSWORD=your_secure_password_here
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=false

# =============================================================================
# SECURITY CONFIGURATION - AZURE OPTIMIZED
# =============================================================================
SESSION_SECRET=your_session_secret_change_this_in_production
JWT_SECRET=your_jwt_secret_change_this_in_production
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# Rate Limiting - More restrictive for public cloud
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# CORS Configuration - Azure Load Balancer and CDN
CORS_ORIGIN=https://your-domain.com,https://your-cdn.azureedge.net

# SSL/HTTPS Configuration
FORCE_HTTPS=true
TRUST_PROXY=true

# =============================================================================
# EMAIL CONFIGURATION - AZURE COMMUNICATION SERVICES
# =============================================================================
EMAIL_ENABLED=true
EMAIL_METHOD=smtp

# Azure Communication Services SMTP
SMTP_HOST=your-acs-resource.communication.azure.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your-acs-smtp-user
SMTP_PASS=your-acs-smtp-password
EMAIL_FROM_NAME=TradeLink Training System
EMAIL_FROM_ADDRESS=<EMAIL>

# =============================================================================
# FILE UPLOAD CONFIGURATION - AZURE BLOB STORAGE
# =============================================================================
UPLOAD_PROVIDER=azure
MAX_FILE_SIZE=5GB

# Azure Blob Storage
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=your-storage-account;AccountKey=your-storage-key;EndpointSuffix=core.windows.net
AZURE_STORAGE_ACCOUNT=your-storage-account
AZURE_STORAGE_KEY=your-storage-key
AZURE_STORAGE_CONTAINER=training-uploads
AZURE_BLOB_CONTAINER=training-uploads

# =============================================================================
# AZURE CONFIGURATION
# =============================================================================
# Azure Subscription and Resource Group
AZURE_SUBSCRIPTION_ID=your-subscription-id
AZURE_RESOURCE_GROUP=training-system-rg
AZURE_LOCATION=East US

# Container Instance Configuration
AZURE_DNS_LABEL=training-system
AZURE_NETWORK_PROFILE_ID=/subscriptions/your-subscription-id/resourceGroups/your-rg/providers/Microsoft.Network/networkProfiles/your-network-profile

# App Service Names (if using App Service instead)
FRONTEND_APP_NAME=training-system-frontend
BACKEND_APP_NAME=training-system-backend

# Azure Key Vault
AZURE_KEY_VAULT_URL=https://your-keyvault.vault.azure.net/

# Azure File Shares for persistent storage
AZURE_POSTGRES_SHARE_NAME=training-postgres
AZURE_UPLOADS_SHARE_NAME=training-uploads

# =============================================================================
# MONITORING AND LOGGING - AZURE MONITOR
# =============================================================================
# Application Insights
APPINSIGHTS_INSTRUMENTATIONKEY=your-instrumentation-key
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=your-key;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/

# Log Analytics
AZURE_LOG_ANALYTICS_WORKSPACE_ID=your-workspace-id
AZURE_LOG_ANALYTICS_WORKSPACE_KEY=your-workspace-key

# Logging Configuration
LOG_LEVEL=info
LOG_TO_AZURE=true

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/api/health

# =============================================================================
# CACHE CONFIGURATION - AZURE REDIS CACHE
# =============================================================================
# Redis Cache
REDIS_ENABLED=true
REDIS_HOST=your-redis-cache.redis.cache.windows.net
REDIS_PORT=6380
REDIS_PASSWORD=your-redis-password
REDIS_TLS=true

# Session Store
SESSION_STORE=redis
AZURE_TABLES_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=your-storage-account;AccountKey=your-storage-key;TableEndpoint=https://your-storage-account.table.core.windows.net/

# =============================================================================
# CONTAINER REGISTRY
# =============================================================================
AZURE_CONTAINER_REGISTRY=your-registry.azurecr.io
ACR_USERNAME=your-registry-username
ACR_PASSWORD=your-registry-password

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
# Virtual Network Configuration
AZURE_VNET_NAME=training-system-vnet
AZURE_SUBNET_NAME=training-system-subnet
AZURE_NSG_NAME=training-system-nsg

# Load Balancer Configuration
AZURE_LB_NAME=training-system-lb
AZURE_LB_PUBLIC_IP=training-system-ip

# =============================================================================
# FEATURE FLAGS - AZURE OPTIMIZED
# =============================================================================
FEATURE_EMAIL_NOTIFICATIONS=true
FEATURE_VIDEO_CONTROLS=true
FEATURE_QUIZ_GENERATION=true
FEATURE_CERTIFICATE_GENERATION=true
FEATURE_ADMIN_PANEL=true

# Azure-specific features
FEATURE_AZURE_AD_AUTH=false
FEATURE_AZURE_CDN=true
FEATURE_AZURE_BACKUP=true

# =============================================================================
# INTERNATIONALIZATION
# =============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,zh

# =============================================================================
# BACKUP AND MAINTENANCE - AZURE BACKUP
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Azure Backup Configuration
AZURE_BACKUP_VAULT=training-system-backup-vault
AZURE_BACKUP_POLICY=training-system-backup-policy

# Maintenance Configuration
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.

# =============================================================================
# SCALING CONFIGURATION
# =============================================================================
# Auto-scaling settings
MIN_REPLICAS=2
MAX_REPLICAS=10
TARGET_CPU_UTILIZATION=70
TARGET_MEMORY_UTILIZATION=80

# Resource limits
CPU_LIMIT=2
MEMORY_LIMIT=4Gi
CPU_REQUEST=1
MEMORY_REQUEST=2Gi
