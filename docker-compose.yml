# =============================================================================
# OPTIMIZED AZURE CONTAINERIZED DEPLOYMENT CONFIGURATION
# =============================================================================

# Azure Container Instances with optimized networking
version: '3.8'

services:
  # =============================================================================
  # DATABASE SERVICE - INTERNAL ONLY
  # =============================================================================
  postgres:
    image: postgres:15
    container_name: training_postgres
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    # NO external port mapping - internal only
    expose:
      - "5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - backend_network  # Separate backend network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # =============================================================================
  # BACKEND SERVICE - INTERNAL + LOAD BALANCER ACCESS
  # =============================================================================
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile.azure
    environment:
      NODE_ENV: production
      DB_HOST: postgres  # Service discovery via service name
      DB_PORT: 5432
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      PORT: 8080  # Azure standard port
      SESSION_SECRET: ${SESSION_SECRET}
      JWT_SECRET: ${JWT_SECRET}
      TRUST_PROXY: "true"  # Enable for Azure Load Balancer
      FORCE_HTTPS: "true"  # Force HTTPS in production
      # Azure-specific configurations
      AZURE_STORAGE_CONNECTION_STRING: ${AZURE_STORAGE_CONNECTION_STRING}
      APPINSIGHTS_INSTRUMENTATIONKEY: ${APPINSIGHTS_INSTRUMENTATIONKEY}
    expose:
      - "8080"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - uploads_data:/app/uploads
    networks:
      - backend_network   # Database access
      - frontend_network  # Frontend communication
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      replicas: 2  # Multiple instances for HA
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3

  # =============================================================================
  # FRONTEND SERVICE - PUBLIC ACCESS VIA LOAD BALANCER
  # =============================================================================
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile.azure
      args:
        # Use internal service name for API calls
        REACT_APP_API_URL: http://backend:8080/api
        NODE_ENV: production
    environment:
      NODE_ENV: production
      PORT: 8080  # Azure standard port
    expose:
      - "8080"
    depends_on:
      - backend
    networks:
      - frontend_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      replicas: 2  # Multiple instances for HA
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3

  # =============================================================================
  # REVERSE PROXY / LOAD BALANCER
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: training_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - frontend_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

# =============================================================================
# VOLUMES - AZURE FILE SHARES
# =============================================================================
volumes:
  postgres_data:
    driver: azure_file
    driver_opts:
      share_name: ${AZURE_POSTGRES_SHARE_NAME:-training-postgres}
      storage_account_name: ${AZURE_STORAGE_ACCOUNT}
      storage_account_key: ${AZURE_STORAGE_KEY}
  
  uploads_data:
    driver: azure_file
    driver_opts:
      share_name: ${AZURE_UPLOADS_SHARE_NAME:-training-uploads}
      storage_account_name: ${AZURE_STORAGE_ACCOUNT}
      storage_account_key: ${AZURE_STORAGE_KEY}

# =============================================================================
# NETWORKS - SEGMENTED FOR SECURITY
# =============================================================================
networks:
  # Backend network - Database and API server only
  backend_network:
    driver: bridge
    internal: true  # No external access
    ipam:
      config:
        - subnet: **********/24
  
  # Frontend network - API server and web server
  frontend_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24

# =============================================================================
# AZURE-SPECIFIC CONFIGURATIONS
# =============================================================================
x-azure-config:
  # Container Instance Group settings
  resource_group: ${AZURE_RESOURCE_GROUP}
  location: ${AZURE_LOCATION}
  
  # DNS and Load Balancer
  dns_name_label: ${AZURE_DNS_LABEL:-training-system}
  
  # Resource allocation
  cpu_cores: 4
  memory_gb: 8
  
  # Network profile
  network_profile:
    id: ${AZURE_NETWORK_PROFILE_ID}
  
  # Log analytics
  log_analytics:
    workspace_id: ${AZURE_LOG_ANALYTICS_WORKSPACE_ID}
    workspace_key: ${AZURE_LOG_ANALYTICS_WORKSPACE_KEY}
