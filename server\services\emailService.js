const EmailConfigService = require('./email/EmailConfigService');
const EmailTemplateService = require('./email/EmailTemplateService');
const SMTPService = require('./email/SMTPService');
const EmailMigrationService = require('./email/EmailMigrationService');
const PasswordEncryptionService = require('./email/PasswordEncryptionService');

class EmailService {
  constructor() {
    this.config = null;
    this.configService = new EmailConfigService();
    this.templateService = new EmailTemplateService();
    this.smtpService = new SMTPService();
    this.migrationService = new EmailMigrationService();
    this.passwordService = new PasswordEncryptionService();
  }

  // Initialize email service with current configuration
  async initialize() {
    try {
      // Run database migrations first
      await this.migrationService.runMigrations();

      // Migrate any existing plain text sensitive data
      await this.configService.migrateSensitiveData();

      this.config = await this.configService.getEmailConfig();

      // Initialize SMTP service if configured
      const smtpInitialized = await this.smtpService.initializeTransporter(this.config);

      if (this.config.email_enabled && this.config.email_method === 'smtp') {
        if (smtpInitialized) {
          console.log('Email service initialized successfully with SMTP');
        } else {
          console.log('Email service initialized with SMTP fallback to clipboard (SMTP connection failed)');
        }
      } else {
        console.log('Email service initialized successfully (clipboard mode)');
      }
    } catch (error) {
      console.error('Failed to initialize email service:', error);
    }
  }

  // Get email configuration from database or environment
  async getEmailConfig() {
    return await this.configService.getEmailConfig();
  }

  // Update email configuration
  async updateEmailConfig(config) {
    const result = await this.configService.updateEmailConfig(config);
    // Reinitialize with new config
    await this.initialize();
    return result;
  }

  // Test email configuration
  async testEmailConfig(config) {
    return await this.smtpService.testEmailConfig(config);
  }

  // Test email configuration and send test email (server-side)
  async testEmailConfigAndSend(config) {
    return await this.smtpService.testEmailConfigAndSend(config);
  }

  // Generate test email HTML template
  generateTestEmailHTML(config) {
    return this.smtpService.generateTestEmailHTML(config);
  }

  // Send email
  async sendEmail(to, subject, htmlContent, textContent = null, ccAddresses = null) {
    try {
      if (!this.config) {
        await this.initialize();
      }

      return await this.smtpService.sendEmail(this.config, to, subject, htmlContent, textContent, ccAddresses);
    } catch (error) {
      console.error('Error sending email:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // Get TradeLink logo as base64 for email embedding
  getTradeLinkLogoBase64() {
    return this.templateService.getTradeLinkLogoBase64();
  }

  // Get default email template
  getDefaultEmailTemplate(language = 'en') {
    return this.templateService.getDefaultEmailTemplate(language);
  }

  // Process template variables
  processTemplate(template, variables) {
    return this.templateService.processTemplate(template, variables);
  }

  // Extract name from email address as fallback
  extractNameFromEmail(email) {
    return this.templateService.extractNameFromEmail(email);
  }

  // Capitalize first letter of a string
  capitalizeFirst(str) {
    return this.templateService.capitalizeFirst(str);
  }

  // Generate HTML email template for login credentials using configurable template
  async generateLoginEmailHTML(userEmail, password, firstName = '', lastName = '', language = 'en') {
    return await this.templateService.generateLoginEmailHTML(this.config, userEmail, password, firstName, lastName, language);
  }

  // Fallback simple email template
  generateSimpleLoginEmail(userEmail, password, firstName = '', lastName = '') {
    return this.templateService.generateSimpleLoginEmail(userEmail, password, firstName, lastName);
  }

  // Password encryption/decryption methods (exposed for backward compatibility)
  async hashPassword(password) {
    return await this.passwordService.hashPassword(password);
  }

  encryptPassword(password) {
    return this.passwordService.encryptPassword(password);
  }

  decryptPassword(encryptedPassword) {
    return this.passwordService.decryptPassword(encryptedPassword);
  }

  isPasswordEncrypted(password) {
    return this.passwordService.isPasswordEncrypted(password);
  }

  // Check if email service is available
  isEmailAvailable() {
    return this.config && this.config.email_enabled && this.config.email_method === 'smtp' && this.smtpService.isAvailable();
  }
}

// Create singleton instance
const emailService = new EmailService();

module.exports = emailService;
