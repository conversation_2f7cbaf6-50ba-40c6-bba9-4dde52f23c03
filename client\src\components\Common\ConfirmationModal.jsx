/**
 * Reusable Confirmation Modal Component
 * Standardized confirmation dialogs with consistent styling
 */

import React from 'react'
import { Modal, Typography, Space, List } from 'antd'
import { ExclamationCircleOutlined, DeleteOutlined, WarningOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const { Text, Paragraph } = Typography

function ConfirmationModal({
  visible,
  title,
  content,
  onConfirm,
  onCancel,
  loading = false,
  type = 'warning', // 'warning', 'danger', 'info'
  confirmText,
  cancelText,
  warningItems = [],
  width = 500,
  ...modalProps
}) {
  const { t } = useTranslation()

  const getIcon = () => {
    switch (type) {
      case 'danger':
        return <DeleteOutlined style={{ color: '#ff4d4f' }} />
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />
      default:
        return <ExclamationCircleOutlined style={{ color: '#1890ff' }} />
    }
  }

  const getOkButtonProps = () => {
    const baseProps = {
      loading,
      onClick: onConfirm
    }

    switch (type) {
      case 'danger':
        return { ...baseProps, danger: true }
      case 'warning':
        return { ...baseProps, type: 'primary' }
      default:
        return { ...baseProps, type: 'primary' }
    }
  }

  return (
    <Modal
      title={
        <Space>
          {getIcon()}
          {title || t('components.modals.confirm')}
        </Space>
      }
      open={visible}
      onOk={onConfirm}
      onCancel={onCancel}
      okText={confirmText || t('common.confirm')}
      cancelText={cancelText || t('common.cancel')}
      okButtonProps={getOkButtonProps()}
      cancelButtonProps={{ disabled: loading }}
      width={width}
      closable={!loading}
      maskClosable={!loading}
      {...modalProps}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {content && (
          <div>
            {typeof content === 'string' ? (
              <Paragraph>{content}</Paragraph>
            ) : (
              content
            )}
          </div>
        )}

        {warningItems.length > 0 && (
          <div>
            <Text strong style={{ color: '#ff4d4f' }}>
              {t('components.modals.warning')}:
            </Text>
            <List
              size="small"
              dataSource={warningItems}
              renderItem={(item) => (
                <List.Item style={{ padding: '4px 0', border: 'none' }}>
                  <Text>• {item}</Text>
                </List.Item>
              )}
              style={{ 
                marginTop: 8,
                backgroundColor: '#fff2f0',
                padding: '8px 12px',
                borderRadius: 4,
                border: '1px solid #ffccc7'
              }}
            />
            <Text type="danger" style={{ fontSize: 12, marginTop: 8, display: 'block' }}>
              {t('videoPlayer.actionCannotBeUndone')}
            </Text>
          </div>
        )}
      </Space>
    </Modal>
  )
}

export default ConfirmationModal
