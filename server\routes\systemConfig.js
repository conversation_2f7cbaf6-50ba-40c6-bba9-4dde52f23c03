const express = require('express');
const router = express.Router();
const systemConfigService = require('../services/systemConfigService');
const { requireAuth, requireAdmin } = require('../middleware/auth');

// Get system configuration (admin only)
router.get('/', requireAuth, requireAdmin, async (req, res) => {
  try {
    const config = await systemConfigService.getSystemConfig();
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Get system config error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve system configuration'
    });
  }
});

// Update system configuration (admin only)
router.put('/', requireAuth, requireAdmin, async (req, res) => {
  try {
    const { password_expiration_days, timezone, max_file_size } = req.body;

    // Validate input
    if (!password_expiration_days) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Password expiration days is required'
      });
    }

    if (typeof password_expiration_days !== 'number' || password_expiration_days < 1 || password_expiration_days > 365) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Password expiration days must be a number between 1 and 365'
      });
    }

    // Validate timezone if provided
    if (timezone && typeof timezone !== 'string') {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Timezone must be a valid string'
      });
    }

    // Validate max file size if provided
    if (max_file_size && typeof max_file_size !== 'string') {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Max file size must be a valid string'
      });
    }

    const updatedConfig = await systemConfigService.updateSystemConfig({
      password_expiration_days,
      timezone: timezone || 'Asia/Shanghai',
      max_file_size: max_file_size || '500MB'
    });

    console.log(`System configuration updated by admin: ${req.user.email}`);
    console.log(`Password expiration days set to: ${password_expiration_days}`);
    console.log(`Timezone set to: ${timezone || 'Asia/Shanghai'}`);
    console.log(`Max file size set to: ${max_file_size || '500MB'}`);

    res.json({
      success: true,
      message: 'System configuration updated successfully',
      data: updatedConfig
    });
  } catch (error) {
    console.error('Update system config error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message || 'Failed to update system configuration'
    });
  }
});

// Get password expiration days (convenience endpoint for internal use)
router.get('/password-expiration-days', async (req, res) => {
  try {
    const days = await systemConfigService.getPasswordExpirationDays();
    res.json({
      success: true,
      data: { password_expiration_days: days }
    });
  } catch (error) {
    console.error('Get password expiration days error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve password expiration days'
    });
  }
});

// Get system timezone (convenience endpoint for internal use)
router.get('/timezone', async (req, res) => {
  try {
    const timezone = await systemConfigService.getSystemTimezone();
    res.json({
      success: true,
      data: { timezone: timezone }
    });
  } catch (error) {
    console.error('Get system timezone error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve system timezone'
    });
  }
});

// Get file upload configuration (public endpoint for client validation)
router.get('/upload-config', async (req, res) => {
  try {
    const config = await systemConfigService.getFileUploadConfig();
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Get upload config error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve upload configuration'
    });
  }
});

module.exports = router;
