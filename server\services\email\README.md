# Email Service Modularization

This document describes the refactoring of the monolithic `emailService.js` file into smaller, focused, modular components.

## Overview

The original `emailService.js` file was 1114 lines long and contained multiple responsibilities. It has been split into 6 focused modules:

## Modular Structure

### 1. **PasswordEncryptionService.js**
**Responsibility**: Password and sensitive data encryption/decryption
- `hashPassword()` - Hash passwords using bcrypt
- `encryptPassword()` - Encrypt passwords for SMTP storage
- `decryptPassword()` - Decrypt passwords for SMTP use
- `isPasswordEncrypted()` - Check if data is encrypted
- `migrateSensitiveData()` - Migrate plain text data to encrypted format

### 2. **EmailConfigService.js**
**Responsibility**: Email configuration management
- `getEmailConfig()` - Retrieve email configuration from database/environment
- `updateEmailConfig()` - Update email configuration in database
- `getDefaultEmailTemplate()` - Get default email templates
- `migrateSensitiveData()` - Wrapper for password service migration

### 3. **EmailMigrationService.js**
**Responsibility**: Database schema migrations for email features
- `runMigrations()` - Execute database migrations for email tables
- Adds columns for reply-to, CC, and template configurations
- Handles both English and Chinese template columns

### 4. **EmailTemplateService.js**
**Responsibility**: Email template processing and generation
- `processTemplate()` - Replace template variables with actual values
- `extractNameFromEmail()` - Extract names from email addresses
- `capitalizeFirst()` - String capitalization utility
- `getTradeLinkLogoBase64()` - Load and encode company logo
- `generateLoginEmailHTML()` - Generate complete login email HTML
- `generateSimpleLoginEmail()` - Fallback simple email template
- `wrapInHTML()` - Wrap content in HTML structure

### 5. **SMTPService.js**
**Responsibility**: SMTP operations and email sending
- `initializeTransporter()` - Set up nodemailer SMTP transporter
- `testEmailConfig()` - Test SMTP configuration
- `testEmailConfigAndSend()` - Test configuration and send test email
- `generateTestEmailHTML()` - Generate test email content
- `sendEmail()` - Send emails via SMTP
- `isAvailable()` - Check if SMTP service is ready

### 6. **EmailService.js** (Main Orchestrator)
**Responsibility**: Coordinate all email services and provide unified API
- Maintains the same public interface as before
- Delegates operations to appropriate specialized services
- Reduced from 1114 lines to 127 lines (89% reduction)

## Benefits of Modularization

### 1. **Separation of Concerns**
Each service has a single, well-defined responsibility:
- Password encryption is isolated from email sending
- Template processing is separate from SMTP operations
- Database migrations are handled independently

### 2. **Improved Maintainability**
- Smaller, focused files are easier to understand and modify
- Changes to one aspect (e.g., encryption) don't affect others
- Easier to locate and fix bugs

### 3. **Better Testability**
- Each service can be unit tested independently
- Mock dependencies more easily for isolated testing
- Reduced complexity in test setup

### 4. **Enhanced Reusability**
- Services can be reused in other parts of the application
- Template service could be used for other notification types
- Encryption service could be used for other sensitive data

### 5. **Cleaner Dependencies**
- Clear dependency relationships between services
- Easier to understand data flow
- Reduced coupling between components

## File Size Comparison

| File | Original Lines | New Lines | Reduction |
|------|----------------|-----------|-----------|
| emailService.js | 1114 | 127 | 89% |
| PasswordEncryptionService.js | - | 150 | New |
| EmailConfigService.js | - | 170 | New |
| EmailMigrationService.js | - | 140 | New |
| EmailTemplateService.js | - | 300 | New |
| SMTPService.js | - | 300 | New |
| **Total** | **1114** | **1187** | **+6.5%** |

While the total lines increased slightly due to module structure and documentation, the main service file was reduced by 89%, making it much more manageable.

## Usage

The public API remains unchanged. Existing code using `emailService` will continue to work without modifications:

```javascript
const emailService = require('./services/emailService');

// All existing methods work the same way
await emailService.initialize();
await emailService.sendEmail(to, subject, htmlContent);
await emailService.generateLoginEmailHTML(email, password, firstName, lastName, language);
```

## Future Enhancements

This modular structure makes it easier to:
- Add new email providers (SendGrid, AWS SES, etc.)
- Implement different encryption methods
- Add new template engines
- Create email queuing and retry mechanisms
- Add email analytics and tracking
