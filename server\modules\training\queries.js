const { query } = require('../../database/init');

/**
 * Get all training modules
 */
async function getAllModules() {
  return await query(`
    SELECT
      tm.*,
      COUNT(DISTINCT tv.language_code) as language_count,
      COUNT(DISTINCT tv.language_code) as video_count,
      ARRAY_AGG(DISTINCT tv.language_code ORDER BY tv.language_code) as available_languages
    FROM training_modules tm
    LEFT JOIN training_videos tv ON tm.id = tv.module_id
    WHERE tm.is_active = true
    GROUP BY tm.id, tm.title, tm.description, tm.created_at, tm.updated_at, tm.is_active
    ORDER BY tm.created_at DESC
  `);
}

/**
 * Get all training modules for admin (including inactive)
 */
async function getAllModulesForAdmin() {
  return await query(`
    SELECT
      tm.*,
      COUNT(DISTINCT tv.language_code) as language_count,
      COUNT(DISTINCT tv.language_code) as video_count,
      ARRAY_AGG(DISTINCT tv.language_code ORDER BY tv.language_code) as available_languages
    FROM training_modules tm
    LEFT JOIN training_videos tv ON tm.id = tv.module_id
    GROUP BY tm.id, tm.title, tm.description, tm.created_at, tm.updated_at, tm.is_active
    ORDER BY tm.is_active DESC, tm.created_at DESC
  `);
}

/**
 * Get module by ID
 */
async function getModuleById(moduleId) {
  const result = await query('SELECT * FROM training_modules WHERE id = $1', [moduleId]);
  return result.rows[0] || null;
}

/**
 * Create new training module
 */
async function createModule(title, description) {
  return await query(`
    INSERT INTO training_modules (title, description)
    VALUES ($1, $2)
    RETURNING *
  `, [title, description]);
}

/**
 * Get video by module and language
 */
async function getVideoByModuleAndLanguage(moduleId, languageCode) {
  const result = await query(`
    SELECT tv.*, tm.title as module_title
    FROM training_videos tv
    JOIN training_modules tm ON tv.module_id = tm.id
    WHERE tv.module_id = $1 AND tv.language_code = $2
  `, [moduleId, languageCode]);
  
  return result.rows[0] || null;
}

/**
 * Create or update video record
 */
async function upsertVideo(moduleId, languageCode, languageName, filename, originalName, fileSize, durationSeconds = null) {
  // Construct the video URL path
  const videoUrl = `/uploads/videos/${filename}`;

  return await query(`
    INSERT INTO training_videos (module_id, language_code, language_name, video_filename, video_url, duration_seconds)
    VALUES ($1, $2, $3, $4, $5, $6)
    ON CONFLICT (module_id, language_code)
    DO UPDATE SET
      language_name = EXCLUDED.language_name,
      video_filename = EXCLUDED.video_filename,
      video_url = EXCLUDED.video_url,
      duration_seconds = EXCLUDED.duration_seconds,
      updated_at = CURRENT_TIMESTAMP
    RETURNING *
  `, [moduleId, languageCode, languageName, filename, videoUrl, durationSeconds]);
}

/**
 * Get user progress for a module
 */
async function getUserProgress(userEmail, moduleId, languageCode) {
  const result = await query(`
    SELECT * FROM user_progress
    WHERE user_email = $1 AND module_id = $2 AND language_code = $3
  `, [userEmail, moduleId, languageCode]);
  
  return result.rows[0] || null;
}

/**
 * Create or update user progress
 */
async function upsertUserProgress(userEmail, moduleId, languageCode, watchTime, completed = false, forceOverwrite = false) {
  if (forceOverwrite) {
    return await query(`
      INSERT INTO user_progress (user_email, module_id, language_code, video_watch_time_seconds, video_watched, updated_at)
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      ON CONFLICT (user_email, module_id, language_code)
      DO UPDATE SET
        video_watch_time_seconds = EXCLUDED.video_watch_time_seconds,
        video_watched = EXCLUDED.video_watched,
        updated_at = CURRENT_TIMESTAMP
      RETURNING *
    `, [userEmail, moduleId, languageCode, watchTime, completed]);
  } else {
    return await query(`
      INSERT INTO user_progress (user_email, module_id, language_code, video_watch_time_seconds, video_watched, updated_at)
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      ON CONFLICT (user_email, module_id, language_code)
      DO UPDATE SET
        video_watch_time_seconds = GREATEST(user_progress.video_watch_time_seconds, EXCLUDED.video_watch_time_seconds),
        video_watched = CASE
          WHEN EXCLUDED.video_watched = true THEN true
          ELSE user_progress.video_watched
        END,
        updated_at = CURRENT_TIMESTAMP
      RETURNING *
    `, [userEmail, moduleId, languageCode, watchTime, completed]);
  }
}

/**
 * Get modules with user progress
 */
async function getModulesWithProgress(userEmail) {
  return await query(`
    SELECT 
      tm.*,
      COUNT(DISTINCT tv.language_code) as language_count,
      ARRAY_AGG(DISTINCT tv.language_code ORDER BY tv.language_code) as available_languages,
      COALESCE(
        json_agg(
          DISTINCT jsonb_build_object(
            'language_code', up.language_code,
            'video_watched', up.video_watched,
            'quiz_completed', up.quiz_completed,
            'quiz_passed', up.quiz_passed,
            'quiz_score', up.quiz_score,
            'certificate_generated', up.certificate_generated,
            'watch_time_seconds', up.video_watch_time_seconds
          )
        ) FILTER (WHERE up.language_code IS NOT NULL),
        '[]'
      ) as user_progress
    FROM training_modules tm
    LEFT JOIN training_videos tv ON tm.id = tv.module_id
    LEFT JOIN user_progress up ON tm.id = up.module_id AND up.user_email = $1
    WHERE tm.is_active = true
    GROUP BY tm.id, tm.title, tm.description, tm.created_at, tm.updated_at, tm.is_active
    ORDER BY tm.created_at DESC
  `, [userEmail]);
}

/**
 * Check if video file exists
 */
async function checkVideoExists(moduleId, languageCode) {
  const result = await query(`
    SELECT video_filename FROM training_videos
    WHERE module_id = $1 AND language_code = $2
  `, [moduleId, languageCode]);

  return result.rows[0] || null;
}

/**
 * Delete video record
 */
async function deleteVideo(moduleId, languageCode) {
  return await query(`
    DELETE FROM training_videos
    WHERE module_id = $1 AND language_code = $2
    RETURNING *
  `, [moduleId, languageCode]);
}

/**
 * Get video by filename
 */
async function getVideoByFilename(filename) {
  const result = await query(`
    SELECT tv.*, tm.title as module_title
    FROM training_videos tv
    JOIN training_modules tm ON tv.module_id = tm.id
    WHERE tv.video_filename = $1
  `, [filename]);

  return result.rows[0] || null;
}

/**
 * Toggle module active status
 */
async function toggleModuleStatus(moduleId, isActive) {
  return await query(`
    UPDATE training_modules
    SET is_active = $2, updated_at = CURRENT_TIMESTAMP
    WHERE id = $1
    RETURNING *
  `, [moduleId, isActive]);
}

/**
 * Update video language name
 */
async function updateVideoLanguageName(videoId, languageName) {
  return await query(`
    UPDATE training_videos
    SET language_name = $2, updated_at = CURRENT_TIMESTAMP
    WHERE id = $1
    RETURNING *
  `, [videoId, languageName]);
}

module.exports = {
  getAllModules,
  getAllModulesForAdmin,
  getModuleById,
  createModule,
  getVideoByModuleAndLanguage,
  upsertVideo,
  getUserProgress,
  upsertUserProgress,
  getModulesWithProgress,
  checkVideoExists,
  deleteVideo,
  getVideoByFilename,
  toggleModuleStatus,
  updateVideoLanguageName
};
