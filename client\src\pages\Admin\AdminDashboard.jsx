import React, { useState } from 'react'
import { Card, Row, Col, Typography, Statistic, Alert, Button, Modal, Space, message } from 'antd'
import { UserOutlined, BookOutlined, TrophyOutlined, FileTextOutlined, DeleteOutlined, ExclamationCircleOutlined, ReloadOutlined, ClockCircleOutlined } from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import { reportsAPI, usersAPI, trainingAPI, adminAPI, handleAPIError } from '../../services/api'
import LoadingImage from '../../components/LoadingImage'

const { Title } = Typography

function AdminDashboard() {
  const { t } = useTranslation()
  const [resetModalVisible, setResetModalVisible] = useState(false)
  const [clearUploadsModalVisible, setClearUploadsModalVisible] = useState(false)
  const queryClient = useQueryClient()

  // Fetch completion statistics
  const { data: statsData, isLoading: statsLoading, error: statsError } = useQuery(
    'completion-stats',
    () => reportsAPI.getCompletionStats(),
    {
      select: (response) => response.data,
    }
  )

  // Fetch all users
  const { data: usersData, isLoading: usersLoading, error: usersError } = useQuery(
    'all-users',
    usersAPI.getAll,
    {
      select: (response) => response.data.users,
    }
  )

  // Fetch training modules
  const { data: modulesData, isLoading: modulesLoading, error: modulesError } = useQuery(
    'training-modules',
    trainingAPI.getModules,
    {
      select: (response) => response.data.modules,
    }
  )

  // Database reset mutation
  const resetDatabaseMutation = useMutation(
    adminAPI.resetDatabase,
    {
      onSuccess: (response) => {
        message.success(t('admin.databaseResetCompleted'))
        queryClient.clear() // Clear all cached data
        setResetModalVisible(false)

        // Show details about what was reset
        Modal.info({
          title: t('admin.databaseResetComplete'),
          content: (
            <div>
              <p>{t('admin.databaseResetDetails')}</p>
              <ul>
                <li><strong>{t('admin.defaultAdmin')}:</strong> {response.data.details.defaultAdmin.email}</li>
                <li><strong>{t('admin.password')}:</strong> {response.data.details.defaultAdmin.password}</li>
                <li><strong>{t('admin.sampleModule')}:</strong> {response.data.details.sampleModule.title}</li>
                <li><strong>{t('admin.videos')}:</strong> {response.data.details.sampleModule.videoCount}</li>
                <li><strong>{t('admin.quizQuestions')}:</strong> {response.data.details.sampleModule.quizQuestions}</li>
              </ul>
            </div>
          ),
        })
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('admin.failedToResetDatabase')}: ${errorInfo.message}`)
        setResetModalVisible(false)
      }
    }
  )

  // Clear uploads mutation
  const clearUploadsMutation = useMutation(
    adminAPI.clearUploads,
    {
      onSuccess: (response) => {
        message.success(t('admin.successfullyClearedFiles', { count: response.data.filesDeleted }))
        setClearUploadsModalVisible(false)
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('admin.failedToClearUploads')}: ${errorInfo.message}`)
        setClearUploadsModalVisible(false)
      }
    }
  )

  const isLoading = statsLoading || usersLoading || modulesLoading
  const error = statsError || usersError || modulesError

  if (isLoading) {
    return <LoadingImage text={t('admin.loadingDashboard')} />
  }

  if (error) {
    const errorInfo = handleAPIError(error)
    return (
      <Alert
        message={t('admin.errorLoadingDashboard')}
        description={errorInfo.message}
        type="error"
        showIcon
      />
    )
  }

  const stats = statsData?.stats || []
  const users = usersData || []
  const modules = modulesData || []

  // Calculate overall statistics
  const totalUsers = users.filter(user => !user.is_admin).length // Exclude admin users from count
  const totalModules = modules.length
  // Use the total certificate count from the API response (actual count from certificates table)
  const totalCertificates = statsData?.totalCertificates || 0
  const expiredPasswordUsers = users.filter(user => user.password_expired && !user.is_admin).length

  // Calculate average completion rate properly
  const totalEnrolledUsers = stats.reduce((sum, stat) => sum + (stat.total_enrolled_users || 0), 0)
  const totalCompletedUsers = stats.reduce((sum, stat) => sum + (stat.users_fully_completed || 0), 0)
  const averageCompletionRate = totalEnrolledUsers > 0
    ? Math.round((totalCompletedUsers / totalEnrolledUsers) * 100)
    : 0



  return (
    <div>
      <Title level={2}>{t('admin.adminDashboard')}</Title>

      {/* Overview Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('admin.totalUsers')}
              value={totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('admin.trainingModules')}
              value={totalModules}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('admin.certificatesIssued')}
              value={totalCertificates}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('admin.avgCompletionRate')}
              value={averageCompletionRate}
              suffix="%"
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Password Expiration Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('admin.expiredPasswords')}
              value={expiredPasswordUsers}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: expiredPasswordUsers > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={18} lg={18}>
          <Card title={t('admin.passwordStatusOverview')}>
            {users.length === 0 ? (
              <div style={{ textAlign: 'center', padding: 20 }}>
                <UserOutlined style={{ fontSize: 32, color: '#d9d9d9', marginBottom: 8 }} />
                <div style={{ color: '#8c8c8c' }}>{t('admin.noUsersFound')}</div>
              </div>
            ) : (
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ borderBottom: '2px solid #f0f0f0' }}>
                      <th style={{ padding: '8px', textAlign: 'left' }}>{t('admin.user')}</th>
                      <th style={{ padding: '8px', textAlign: 'center' }}>{t('admin.type')}</th>
                      <th style={{ padding: '8px', textAlign: 'center' }}>{t('admin.passwordAge')}</th>
                      <th style={{ padding: '8px', textAlign: 'center' }}>{t('admin.status')}</th>
                      <th style={{ padding: '8px', textAlign: 'center' }}>{t('admin.lastLogin')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.slice(0, 10).map((user) => (
                      <tr key={user.email} style={{ borderBottom: '1px solid #f0f0f0' }}>
                        <td style={{ padding: '8px' }}>
                          <div>
                            <strong>{user.email}</strong>
                            {user.first_name && (
                              <div style={{ fontSize: 12, color: '#8c8c8c' }}>
                                {user.first_name} {user.last_name}
                              </div>
                            )}
                          </div>
                        </td>
                        <td style={{ padding: '8px', textAlign: 'center' }}>
                          <span style={{
                            padding: '2px 8px',
                            borderRadius: 4,
                            fontSize: 12,
                            background: user.is_admin ? '#fff7e6' : '#f6ffed',
                            color: user.is_admin ? '#d46b08' : '#389e0d',
                            border: `1px solid ${user.is_admin ? '#ffd591' : '#b7eb8f'}`
                          }}>
                            {user.is_admin ? t('admin.admin') : t('admin.user')}
                          </span>
                        </td>
                        <td style={{ padding: '8px', textAlign: 'center' }}>
                          {user.is_admin ? (
                            <span style={{ color: '#8c8c8c' }}>N/A</span>
                          ) : (
                            <span>{user.password_age_days || 0} {t('admin.days')}</span>
                          )}
                        </td>
                        <td style={{ padding: '8px', textAlign: 'center' }}>
                          {user.is_admin ? (
                            <span style={{ color: '#8c8c8c' }}>{t('admin.noExpiry')}</span>
                          ) : user.password_expired ? (
                            <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>{t('admin.expired')}</span>
                          ) : (
                            <span style={{ color: '#52c41a' }}>{t('admin.valid')}</span>
                          )}
                        </td>
                        <td style={{ padding: '8px', textAlign: 'center', fontSize: 12, color: '#8c8c8c' }}>
                          {user.last_login ? new Date(user.last_login).toLocaleDateString() : t('admin.never')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {users.length > 10 && (
                  <div style={{ textAlign: 'center', padding: 8, color: '#8c8c8c', fontSize: 12 }}>
                    {t('admin.showingFirstTenUsers')}
                  </div>
                )}
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* Module Statistics */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title={t('admin.trainingModuleStatistics')}>
            {stats.length === 0 ? (
              <div style={{ textAlign: 'center', padding: 40 }}>
                <BookOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <Title level={4} type="secondary">{t('admin.noTrainingData')}</Title>
              </div>
            ) : (
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ borderBottom: '2px solid #f0f0f0' }}>
                      <th style={{ padding: '12px 8px', textAlign: 'left' }}>{t('admin.module')}</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>{t('admin.languages')}</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>{t('admin.enrolled')}</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>{t('admin.completed')}</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>{t('admin.certificates')}</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>{t('admin.avgScore')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {stats.map((stat) => {
                      const enrolledUsers = stat.total_enrolled_users || 0
                      const completedUsers = stat.users_fully_completed || 0
                      const completionRate = enrolledUsers > 0 ? Math.round((completedUsers / enrolledUsers) * 100) : 0

                      return (
                        <tr key={stat.module_id} style={{ borderBottom: '1px solid #f0f0f0' }}>
                          <td style={{ padding: '12px 8px' }}>
                            <strong>{stat.module_title}</strong>
                          </td>
                          <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                            {stat.available_languages || 0}
                          </td>
                          <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                            {enrolledUsers}
                          </td>
                          <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                            {completedUsers}
                            {enrolledUsers > 0 && (
                              <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                                ({completionRate}%)
                              </div>
                            )}
                          </td>
                          <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                            {stat.certificates_issued || 0}
                          </td>
                          <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                            {stat.average_score ? `${stat.average_score}%` : '-'}
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title={t('admin.recentActivity')} style={{ marginBottom: 16 }}>
            <div style={{ textAlign: 'center', padding: 20 }}>
              <FileTextOutlined style={{ fontSize: 32, color: '#d9d9d9', marginBottom: 8 }} />
              <div style={{ color: '#8c8c8c' }}>
                {t('admin.activityTrackingComingSoon')}
              </div>
            </div>
          </Card>

          <Card title={t('admin.systemHealth')}>
            <div style={{ textAlign: 'center', padding: 20 }}>
              <div style={{
                width: 60,
                height: 60,
                borderRadius: '50%',
                background: '#52c41a',
                margin: '0 auto 12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: 24,
                fontWeight: 'bold'
              }}>
                ✓
              </div>
              <div style={{ color: '#52c41a', fontWeight: 'bold' }}>
                {t('admin.allSystemsOperational')}
              </div>
            </div>
          </Card>

          <Card title={t('admin.databaseManagement')} style={{ marginTop: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                onClick={() => setResetModalVisible(true)}
                loading={resetDatabaseMutation.isLoading}
                block
              >
                {t('admin.resetDatabase')}
              </Button>

              <Button
                icon={<ReloadOutlined />}
                onClick={() => setClearUploadsModalVisible(true)}
                loading={clearUploadsMutation.isLoading}
                block
              >
                {t('admin.clearUploadedFiles')}
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Database Reset Confirmation Modal */}
      <Modal
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            {t('admin.resetDatabase')}
          </Space>
        }
        open={resetModalVisible}
        onCancel={() => setResetModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setResetModalVisible(false)}>
            Cancel
          </Button>,
          <Button
            key="reset"
            type="primary"
            danger
            loading={resetDatabaseMutation.isLoading}
            onClick={() => resetDatabaseMutation.mutate()}
          >
            {t('admin.resetDatabase')}
          </Button>,
        ]}
      >
        <Alert
          message={t('admin.resetDatabaseWarning')}
          description={
            <div>
              <p>{t('admin.resetDatabaseDescription')}</p>
              <ul>
                <li>{t('admin.allUserProgress')}</li>
                <li>{t('admin.allTrainingModules')}</li>
                <li>{t('admin.allCertificates')}</li>
                <li>{t('admin.allUsers')}</li>
              </ul>
              <p><strong>{t('admin.sampleModuleWillBeCreated')}</strong></p>
            </div>
          }
          type="warning"
          showIcon
        />
      </Modal>

      {/* Clear Uploads Confirmation Modal */}
      <Modal
        title={t('admin.clearUploadedFilesTitle')}
        open={clearUploadsModalVisible}
        onCancel={() => setClearUploadsModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setClearUploadsModalVisible(false)}>
            Cancel
          </Button>,
          <Button
            key="clear"
            type="primary"
            loading={clearUploadsMutation.isLoading}
            onClick={() => clearUploadsMutation.mutate()}
          >
            {t('admin.clearFiles')}
          </Button>,
        ]}
      >
        <Alert
          message={t('admin.clearUploadedFilesDescription')}
          description={t('admin.clearUploadedFilesWarning')}
          type="warning"
          showIcon
        />
      </Modal>
    </div>
  )
}

export default AdminDashboard
