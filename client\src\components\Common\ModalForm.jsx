/**
 * Reusable Modal Form Component
 * Common modal form wrapper with consistent styling and behavior
 */

import React, { useEffect } from 'react'
import { Modal, Form, Button, Space } from 'antd'
import { useTranslation } from 'react-i18next'

function ModalForm({
  title,
  visible,
  onCancel,
  onSubmit,
  loading = false,
  width = 600,
  children,
  initialValues = {},
  form: externalForm,
  submitText,
  cancelText,
  destroyOnClose = true,
  maskClosable = false,
  footer = null,
  layout = 'vertical',
  size = 'large',
  ...modalProps
}) {
  const { t } = useTranslation()
  const [internalForm] = Form.useForm()
  const form = externalForm || internalForm

  // Set initial values when modal opens
  useEffect(() => {
    if (visible && initialValues && Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues)
    }
  }, [visible, initialValues, form])

  // Reset form when modal closes
  useEffect(() => {
    if (!visible && destroyOnClose) {
      form.resetFields()
    }
  }, [visible, destroyOnClose, form])

  const handleSubmit = async (values) => {
    try {
      await onSubmit?.(values)
    } catch (error) {
      console.error('Form submission error:', error)
      // Error handling should be done in parent component
    }
  }

  const handleCancel = () => {
    if (destroyOnClose) {
      form.resetFields()
    }
    onCancel?.()
  }

  const defaultFooter = footer === null ? (
    <Space>
      <Button onClick={handleCancel} disabled={loading}>
        {cancelText || t('common.cancel')}
      </Button>
      <Button 
        type="primary" 
        onClick={() => form.submit()} 
        loading={loading}
      >
        {submitText || t('common.submit')}
      </Button>
    </Space>
  ) : footer

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      footer={defaultFooter}
      width={width}
      destroyOnClose={destroyOnClose}
      maskClosable={maskClosable}
      {...modalProps}
    >
      <Form
        form={form}
        layout={layout}
        size={size}
        onFinish={handleSubmit}
        preserve={false}
      >
        {children}
      </Form>
    </Modal>
  )
}

export default ModalForm
