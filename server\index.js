const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const session = require('express-session');
const rateLimit = require('express-rate-limit');
const path = require('path');

// Load unified environment configuration
const config = require('../config/environment');

const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const trainingRoutes = require('./routes/training');
const quizRoutes = require('./routes/quiz');
const reportRoutes = require('./routes/reports');
const adminRoutes = require('./routes/admin');
const emailConfigRoutes = require('./routes/emailConfig');
const systemConfigRoutes = require('./routes/systemConfig');
const { initializeDatabase } = require('./database/init');
const emailService = require('./services/emailService');
const systemConfigService = require('./services/systemConfigService');

const app = express();
const PORT = config.get('server').port;

// Security middleware - Disable CSP temporarily for debugging
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP to debug video loading issues
}));

// Rate limiting - Disabled for development
// const limiter = rateLimit({
//   windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
//   max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
//   message: 'Too many requests from this IP, please try again later.',
// });
// app.use(limiter);

// CORS configuration from unified config
const corsConfig = config.get('cors');
app.use(cors(corsConfig));

// Logging
app.use(morgan('combined'));

// Body parsing
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Note: Email logos are now served as CID attachments for better email client compatibility

// Security middleware - Production hardened
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      imgSrc: ["'self'", "data:", "blob:"],
      mediaSrc: ["'self'", "blob:"],
      connectSrc: ["'self'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Additional security headers for production
app.use((req, res, next) => {
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  next();
});

// Debug middleware - log all requests (disable in production)
if (config.get('environment').isDevelopment) {
  app.use((req, res, next) => {
    console.log('=== INCOMING REQUEST ===');
    console.log('Method:', req.method);
    console.log('URL:', req.url);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Body:', JSON.stringify(req.body, null, 2));
    console.log('Query:', JSON.stringify(req.query, null, 2));
    console.log('========================');
    next();
  });
}

// Session configuration from unified config
const securityConfig = config.get('security');
app.use(session({
  secret: securityConfig.sessionSecret,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: config.get('environment').isProduction,
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
  },
}));

// Static file serving for uploads
const uploadsPath = path.join(__dirname, 'uploads');
console.log('Static files served from:', uploadsPath);

// Disable CORS for uploads entirely - let the specific routes handle it
app.use('/uploads', (req, res, next) => {
  console.log('=== UPLOADS REQUEST ===');
  console.log('Static file request:', req.url);
  console.log('Full path would be:', path.join(uploadsPath, req.url));
  console.log('Request method:', req.method);
  console.log('Request origin:', req.headers.origin);
  console.log('Request headers:', req.headers);
  console.log('========================');

  // Remove all CORS restrictions for uploads
  res.removeHeader('X-Powered-By');

  next();
});

// Handle OPTIONS requests for video files - more permissive
app.options('/uploads/videos/:filename', (req, res) => {
  console.log('OPTIONS request for video:', req.params.filename);
  console.log('Request origin:', req.headers.origin);

  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Range, Content-Range, Content-Length, Content-Type, Cache-Control, If-Range, Origin, X-Requested-With');
  res.header('Access-Control-Expose-Headers', 'Content-Range, Content-Length, Accept-Ranges');
  res.header('Access-Control-Max-Age', '86400'); // 24 hours

  res.status(200).end();
});

// Handle OPTIONS requests for API video routes
app.options('/api/training/video/:filename', (req, res) => {
  console.log('OPTIONS request for API video:', req.params.filename);
  console.log('Request origin:', req.headers.origin);

  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Range, Content-Range, Content-Length, Content-Type, Cache-Control, If-Range, Origin, X-Requested-With');
  res.header('Access-Control-Expose-Headers', 'Content-Range, Content-Length, Accept-Ranges');
  res.header('Access-Control-Max-Age', '86400'); // 24 hours

  res.status(200).end();
});

// Custom video serving with proper CORS and range support
app.get('/uploads/videos/:filename', (req, res) => {
  const filename = req.params.filename;
  const videoPath = path.join(uploadsPath, 'videos', filename);

  console.log('Direct video request for:', filename);
  console.log('Video path:', videoPath);
  console.log('Request origin:', req.headers.origin);

  // Set CORS headers - very permissive
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Expose-Headers', 'Content-Range, Content-Length, Accept-Ranges');
  res.header('Access-Control-Allow-Credentials', 'false');

  // Set video headers
  res.header('Accept-Ranges', 'bytes');
  res.header('Content-Type', 'video/mp4');
  res.header('Cache-Control', 'public, max-age=3600');

  // Serve the file with range support
  res.sendFile(videoPath, (err) => {
    if (err) {
      console.error('Error serving video file:', err);
      if (!res.headersSent) {
        res.status(404).json({ error: 'Video file not found' });
      }
    } else {
      console.log('Video file served successfully:', filename);
    }
  });
});

// Fallback static file serving for other uploads
app.use('/uploads', express.static(uploadsPath, {
  // Enable range requests for video streaming
  acceptRanges: true,
  // Set proper cache headers
  maxAge: '1d',
  // Set proper content type headers
  setHeaders: (res, path) => {
    if (path.endsWith('.mp4')) {
      res.setHeader('Content-Type', 'video/mp4');
    } else if (path.endsWith('.webm')) {
      res.setHeader('Content-Type', 'video/webm');
    } else if (path.endsWith('.avi')) {
      res.setHeader('Content-Type', 'video/x-msvideo');
    } else if (path.endsWith('.mov')) {
      res.setHeader('Content-Type', 'video/quicktime');
    }
    // Enable range requests
    res.setHeader('Accept-Ranges', 'bytes');
  }
}));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/training', trainingRoutes);
app.use('/api/quiz', quizRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/email-config', emailConfigRoutes);
app.use('/api/system-config', systemConfigRoutes);

// Video replacement endpoints (admin only)
const { requireAdmin } = require('./middleware/auth');
const { replaceVideoFile } = require('./modules/training/videoReplacement');

// In-place video file replacement (single upload)
app.put('/api/training/modules/:id/videos/:languageCode/replace', requireAdmin, replaceVideoFile);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Test endpoint to check video file access
app.get('/api/test-video/:filename', async (req, res) => {
  const filename = req.params.filename;
  const videoPath = path.join(uploadsPath, 'videos', filename);

  try {
    const fs = require('fs').promises;
    const stats = await fs.stat(videoPath);

    res.json({
      message: 'Video file found',
      filename: filename,
      path: videoPath,
      size: stats.size,
      exists: true,
      url: `http://127.0.0.1:3002/uploads/videos/${filename}`
    });
  } catch (error) {
    res.status(404).json({
      message: 'Video file not found',
      filename: filename,
      path: videoPath,
      exists: false,
      error: error.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Initialize database and start server
async function startServer() {
  try {
    await initializeDatabase();
    console.log('Database initialized successfully');

    // Initialize system config service (cache database values)
    await systemConfigService.initialize();
    console.log('System config service initialized');

    // Initialize email service
    await emailService.initialize();
    console.log('Email service initialized');

    app.listen(PORT, '0.0.0.0', () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
