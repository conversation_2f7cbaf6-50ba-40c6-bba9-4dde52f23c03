import React from 'react'
import { Select, Space } from 'antd'
import { TranslationOutlined } from '@ant-design/icons'
import { useLanguage } from '../contexts/LanguageContext'

const { Option } = Select

const LanguageSwitch = ({ size = 'small', showIcon = true, style = {} }) => {
  const { currentLanguage, changeLanguage, languageOptions } = useLanguage()

  const handleLanguageChange = (languageCode) => {
    if (languageCode === currentLanguage) {
      return // No change needed
    }

    // Simply change the UI language without deleting any progress
    changeLanguage(languageCode)
  }

  return (
    <Space
      size="small"
      style={{
        ...style,
        border: '1px solid #40a9ff',
        borderRadius: '8px',
        padding: '2px 6px',
        backgroundColor: '#f0f8ff',
        height: '28px',
        alignItems: 'center',
        flexShrink: 0
      }}
      className="language-switch-container"
    >
      {showIcon && <TranslationOutlined style={{ color: '#1890ff', fontSize: '14px' }} />}
      <Select
        value={currentLanguage}
        onChange={handleLanguageChange}
        size="small"
        style={{
          minWidth: 90,
          color: '#000000',
          fontSize: '12px'
        }}
        variant="borderless"
        suffixIcon={null}

      >
        {languageOptions.map(lang => (
          <Option key={lang.code} value={lang.code}>
            {lang.nativeName}
          </Option>
        ))}
      </Select>
    </Space>
  )
}

export default LanguageSwitch
