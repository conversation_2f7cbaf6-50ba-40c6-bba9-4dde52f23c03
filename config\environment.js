// Unified Environment Configuration Loader
// This module provides a centralized way to load and validate environment variables
const path = require('path');
const fs = require('fs');

// Load environment variables from root .env file
require('dotenv').config({ path: path.join(__dirname, '../.env') });

/**
 * Environment configuration object with validation and defaults
 */
class EnvironmentConfig {
  constructor() {
    this.validateRequiredVariables();
    this.config = this.loadConfiguration();
  }

  /**
   * Validate required environment variables
   */
  validateRequiredVariables() {
    const required = [
      'DB_HOST',
      'DB_NAME',
      'DB_USER',
      'DB_PASSWORD',
      'SESSION_SECRET'
    ];

    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      console.error('❌ Missing required environment variables:', missing.join(', '));
      console.error('Please check your .env file in the root directory');
      process.exit(1);
    }
  }

  /**
   * Load and organize all configuration
   */
  loadConfiguration() {
    return {
      // Environment
      environment: {
        nodeEnv: process.env.NODE_ENV || 'development',
        deploymentType: process.env.DEPLOYMENT_TYPE || 'local',
        isDevelopment: process.env.NODE_ENV === 'development',
        isProduction: process.env.NODE_ENV === 'production',
        isDocker: process.env.DEPLOYMENT_TYPE === 'docker',
        isAzure: process.env.DEPLOYMENT_TYPE?.includes('azure')
      },

      // Server Configuration
      server: {
        port: parseInt(process.env.BACKEND_PORT) || parseInt(process.env.PORT) || 8080,
        host: '0.0.0.0',
        trustProxy: process.env.TRUST_PROXY === 'true',
        forceHttps: process.env.FORCE_HTTPS === 'true'
      },

      // Client Configuration
      client: {
        port: parseInt(process.env.FRONTEND_PORT) || 8080,
        devPort: parseInt(process.env.FRONTEND_DEV_PORT) || 3000,
        apiUrl: process.env.REACT_APP_API_URL || `http://backend:${parseInt(process.env.BACKEND_PORT) || 8080}/api`
      },

      // Database Configuration
      database: {
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT) || 5432,
        database: process.env.DB_NAME,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        ssl: process.env.DB_SSL === 'true' ? {
          rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false'
        } : false,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000
      },

      // Security Configuration
      security: {
        sessionSecret: process.env.SESSION_SECRET,
        jwtSecret: process.env.JWT_SECRET || 'fallback_jwt_secret_change_in_production',
        jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
        bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
        rateLimiting: {
          windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
          max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
        }
      },

      // CORS Configuration
      cors: {
        origin: process.env.CORS_ORIGIN ?
          process.env.CORS_ORIGIN.split(',').map(origin => origin.trim()) :
          ['https://your-domain.com', 'https://your-cdn.azureedge.net'],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
        allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization', 'Range', 'Cache-Control', 'If-Range'],
        exposedHeaders: ['Content-Range', 'Content-Length', 'Accept-Ranges']
      },

      // Email Configuration
      email: {
        enabled: process.env.EMAIL_ENABLED === 'true',
        method: process.env.EMAIL_METHOD || 'clipboard',
        smtp: {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT) || 587,
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
          }
        },
        from: {
          name: process.env.EMAIL_FROM_NAME || 'TradeLink Training System',
          address: process.env.EMAIL_FROM_ADDRESS
        },
        templates: {
          subjectEn: process.env.EMAIL_TEMPLATE_SUBJECT_EN || 'Your Training System Login Credentials',
          subjectZh: process.env.EMAIL_TEMPLATE_SUBJECT_ZH || '您的培训系统登录凭据'
        }
      },

      // File Upload Configuration
      upload: {
        provider: process.env.UPLOAD_PROVIDER || 'local',
        path: process.env.UPLOAD_PATH || './uploads',
        maxFileSize: process.env.MAX_FILE_SIZE || '5GB',
        azure: {
          connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING,
          accountName: process.env.AZURE_STORAGE_ACCOUNT,
          accountKey: process.env.AZURE_STORAGE_KEY,
          containerName: process.env.AZURE_STORAGE_CONTAINER || 'training-uploads'
        }
      },

      // Azure Configuration
      azure: {
        subscriptionId: process.env.AZURE_SUBSCRIPTION_ID,
        resourceGroup: process.env.AZURE_RESOURCE_GROUP,
        location: process.env.AZURE_LOCATION || 'East US',
        keyVaultUrl: process.env.AZURE_KEY_VAULT_URL,
        appInsights: {
          instrumentationKey: process.env.APPINSIGHTS_INSTRUMENTATIONKEY,
          connectionString: process.env.APPLICATIONINSIGHTS_CONNECTION_STRING
        }
      },

      // Logging Configuration
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        toAzure: process.env.LOG_TO_AZURE === 'true'
      },

      // Health Check Configuration
      healthCheck: {
        enabled: process.env.HEALTH_CHECK_ENABLED !== 'false',
        path: process.env.HEALTH_CHECK_PATH || '/api/health'
      },

      // Cache Configuration
      cache: {
        redis: {
          enabled: process.env.REDIS_ENABLED === 'true',
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT) || 6379,
          password: process.env.REDIS_PASSWORD,
          tls: process.env.REDIS_TLS === 'true'
        },
        session: {
          store: process.env.SESSION_STORE || 'memory',
          azureTablesConnectionString: process.env.AZURE_TABLES_CONNECTION_STRING
        }
      },

      // Feature Flags
      features: {
        emailNotifications: process.env.FEATURE_EMAIL_NOTIFICATIONS !== 'false',
        videoControls: process.env.FEATURE_VIDEO_CONTROLS !== 'false',
        quizGeneration: process.env.FEATURE_QUIZ_GENERATION !== 'false',
        certificateGeneration: process.env.FEATURE_CERTIFICATE_GENERATION !== 'false',
        adminPanel: process.env.FEATURE_ADMIN_PANEL !== 'false'
      },

      // Internationalization
      i18n: {
        defaultLanguage: process.env.DEFAULT_LANGUAGE || 'en',
        supportedLanguages: process.env.SUPPORTED_LANGUAGES ? 
          process.env.SUPPORTED_LANGUAGES.split(',').map(lang => lang.trim()) : 
          ['en', 'zh']
      },

      // Maintenance
      maintenance: {
        mode: process.env.MAINTENANCE_MODE === 'true',
        message: process.env.MAINTENANCE_MESSAGE || 'System is under maintenance. Please try again later.',
        backup: {
          enabled: process.env.BACKUP_ENABLED === 'true',
          schedule: process.env.BACKUP_SCHEDULE || '0 2 * * *',
          retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS) || 30
        }
      }
    };
  }

  /**
   * Get configuration for a specific section
   */
  get(section) {
    return this.config[section];
  }

  /**
   * Get all configuration
   */
  getAll() {
    return this.config;
  }

  /**
   * Print configuration summary (without sensitive data)
   */
  printSummary() {
    const summary = {
      Environment: this.config.environment.nodeEnv,
      'Deployment Type': this.config.environment.deploymentType,
      'Server Port': this.config.server.port,
      'Client Port': this.config.client.port,
      'Database Host': this.config.database.host,
      'Email Enabled': this.config.email.enabled,
      'Upload Provider': this.config.upload.provider,
      'Features Enabled': Object.entries(this.config.features)
        .filter(([, enabled]) => enabled)
        .map(([feature]) => feature)
        .join(', ')
    };

    console.log('📋 Configuration Summary:');
    Object.entries(summary).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
  }
}

// Create and export singleton instance
const environmentConfig = new EnvironmentConfig();

module.exports = environmentConfig;
