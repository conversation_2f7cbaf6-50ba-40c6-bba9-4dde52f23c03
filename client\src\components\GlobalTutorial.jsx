import React from 'react'
import { Modal, Button, Space, Typography } from 'antd'
import { useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useTutorial } from '../contexts/TutorialContext'
import { useAuth } from '../contexts/AuthContext'
import { BookOutlined, PlayCircleOutlined, UserOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import {
  InteractiveTrainingModulesButton,
  InteractiveQuizButton,
  InteractiveLanguageSwitch,
  InteractiveVideoControls,
  InteractiveVolumeControls
} from './InteractiveTutorialElements'

const { Text } = Typography

function GlobalTutorial() {
  const { t } = useTranslation()
  const location = useLocation()
  const { user } = useAuth()
  const {
    showTutorial,
    tutorialStep,
    closeTutorial,
    nextStep,
    prevStep,
    setTutorialStep
  } = useTutorial()

  // Get comprehensive tutorial steps that work on all pages
  const getTutorialSteps = () => {
    return [
      {
        title: t('loginTutorial.welcomeTitle'),
        content: t('loginTutorial.welcomeDescription'),
        element: null
      },
      {
        title: t('loginTutorial.trainingModulesTitle'),
        content: t('loginTutorial.trainingModulesDescription'),
        element: <InteractiveTrainingModulesButton />
      },
      {
        title: t('loginTutorial.videoWatchingTitle'),
        content: t('loginTutorial.videoWatchingDescription'),
        element: <InteractiveVideoControls />
      },
      {
        title: t('tutorial.volumeTitle'),
        content: t('tutorial.volumeDescription'),
        element: <InteractiveVolumeControls />
      },
      {
        title: t('loginTutorial.postVideoWorkflowTitle'),
        content: t('loginTutorial.postVideoWorkflowDescription'),
        element: null
      },
      {
        title: t('loginTutorial.quizSystemTitle'),
        content: t('loginTutorial.quizSystemDescription'),
        element: <InteractiveQuizButton />
      },
      {
        title: t('loginTutorial.certificatesTitle'),
        content: t('loginTutorial.certificatesDescription'),
        element: (
          <div style={{ textAlign: 'center', margin: '20px 0', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
            <div style={{
              border: '2px dashed #1890ff',
              padding: '16px',
              borderRadius: '8px',
              backgroundColor: 'white',
              display: 'inline-block',
              minWidth: '200px'
            }}>
              <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff', marginBottom: '8px' }}>
                🏆 Training Certificate
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                Earned after quiz completion
              </div>
            </div>
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              (Example certificate - downloadable and printable)
            </div>
          </div>
        )
      },
      {
        title: t('loginTutorial.languageSwitchTitle'),
        content: t('loginTutorial.languageSwitchDescription'),
        element: <InteractiveLanguageSwitch />
      },
      {
        title: t('loginTutorial.tutorialAccessTitle'),
        content: t('loginTutorial.tutorialAccessDescription'),
        element: null
      }
    ]
  }

  const tutorialSteps = getTutorialSteps()

  const handleTutorialNext = () => {
    if (tutorialStep < tutorialSteps.length - 1) {
      nextStep()
    } else {
      closeTutorial()
      // Mark tutorial as seen when completing it
      if (user?.email) {
        localStorage.setItem(`tutorial_seen_${user.email}`, 'true')
      }
    }
  }

  const handleTutorialPrev = () => {
    if (tutorialStep > 0) {
      prevStep()
    }
  }

  const handleTutorialSkip = () => {
    closeTutorial()
    // Mark tutorial as seen when skipping it
    if (user?.email) {
      localStorage.setItem(`tutorial_seen_${user.email}`, 'true')
    }
  }

  if (!showTutorial || tutorialSteps.length === 0) {
    return null
  }

  return (
    <Modal
      title={tutorialSteps[tutorialStep]?.title}
      open={showTutorial}
      onCancel={handleTutorialSkip}
      footer={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ fontSize: 12, color: '#666' }}>
            {t('loginTutorial.stepCounter', { current: tutorialStep + 1, total: tutorialSteps.length })}
          </div>
          <Space>
            <Button onClick={handleTutorialSkip}>
              {t('loginTutorial.skipTutorial')}
            </Button>
            {tutorialStep > 0 && (
              <Button onClick={handleTutorialPrev}>
                {t('loginTutorial.previous')}
              </Button>
            )}
            <Button
              type="primary"
              onClick={handleTutorialNext}
            >
              {tutorialStep < tutorialSteps.length - 1 ? t('loginTutorial.next') : t('loginTutorial.getStarted')}
            </Button>
          </Space>
        </div>
      }
      width={600}
      closable={false}
      maskClosable={false}
    >
      <div style={{ minHeight: 200 }}>
        <div style={{ fontSize: 16, lineHeight: 1.6, marginBottom: 16 }}>
          {tutorialSteps[tutorialStep]?.content}
        </div>
        {tutorialSteps[tutorialStep]?.element}
      </div>
    </Modal>
  )
}

export default GlobalTutorial
