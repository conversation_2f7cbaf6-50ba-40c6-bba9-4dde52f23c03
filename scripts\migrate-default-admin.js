#!/usr/bin/env node

/**
 * Migration script to add is_default_admin column and update existing default admin
 * This script can be run safely multiple times
 */

const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'training_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'training_system',
  password: process.env.DB_PASSWORD || 'training_password',
  port: process.env.DB_PORT || 5432,
});

async function runMigration() {
  console.log('🚀 Starting default admin migration...');
  
  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'server', 'database', 'migrations', 'add_default_admin_flag.sql');
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');
    
    console.log('📄 Migration SQL loaded');
    
    // Execute the migration
    const client = await pool.connect();
    
    try {
      console.log('🔄 Executing migration...');
      await client.query(migrationSQL);
      console.log('✅ Migration completed successfully!');
      
      // Verify the results
      const result = await client.query(`
        SELECT email, is_admin, is_default_admin 
        FROM users 
        WHERE email = '<EMAIL>'
      `);
      
      if (result.rows.length > 0) {
        const admin = result.rows[0];
        console.log('📊 Default admin status:');
        console.log(`   Email: ${admin.email}`);
        console.log(`   Is Admin: ${admin.is_admin}`);
        console.log(`   Is Default Admin: ${admin.is_default_admin}`);
      } else {
        console.log('⚠️  Warning: Default admin user not found');
      }
      
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
  
  console.log('🎉 Migration process completed!');
}

// Run the migration
runMigration().catch(console.error);
