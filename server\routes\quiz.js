const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { query, getClient } = require('../database/init');
const { requireAuth, requireAdmin } = require('../middleware/auth');
const { createQuizSchema, updateQuizSchema, submitQuizSchema } = require('../modules/quiz/schemas');
const {
  calculateQuizScore,
  generateCertificate,
  createQuizAttempt,
  updateUserProgress
} = require('../modules/quiz/services');
const {
  getVideoByModuleAndLanguage,
  checkVideoCompletion,
  getQuizByVideoId,
  getQuizQuestionsWithOptions,
  getQuizQuestionsForGrading,
  checkQuizPassed,
  getQuizForEditing,
  checkQuizExists,
  getVideoById
} = require('../modules/quiz/queries');

const router = express.Router();

// Get quiz by module and language (for taking quiz)
router.get('/modules/:moduleId/quiz/:languageCode', requireAuth, async (req, res) => {
  try {
    const { moduleId, languageCode } = req.params;
    const userEmail = req.user.email;

    console.log('=== GET QUIZ BY MODULE DEBUG ===');
    console.log('moduleId:', moduleId);
    console.log('languageCode:', languageCode);
    console.log('userEmail:', userEmail);

    // Get video for this module and language
    const video = await getVideoByModuleAndLanguage(moduleId, languageCode);
    if (!video) {
      return res.status(404).json({ error: 'Video not found for this module and language' });
    }

    console.log('Found video:', video.id);

    // Check if user has completed the video first
    const videoCompleted = await checkVideoCompletion(userEmail, video.module_id, video.language_code);
    if (!videoCompleted) {
      return res.status(403).json({
        error: 'Video must be completed before taking the quiz'
      });
    }

    // Get quiz details
    const quiz = await getQuizByVideoId(video.id);
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    // Get quiz questions with options
    const questions = await getQuizQuestionsWithOptions(quiz.id);



    // Check if user has already passed this quiz
    const alreadyPassed = await checkQuizPassed(userEmail, quiz.id);

    res.json({
      quiz: {
        ...quiz,
        questions: questions,
        videoId: video.id // Include videoId for submission
      },
      video: video, // Include video data for the watch video button
      alreadyPassed
    });
  } catch (error) {
    console.error('Get quiz by module error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Submit quiz by module and language
router.post('/modules/:moduleId/quiz/:languageCode/submit', requireAuth, async (req, res) => {
  try {
    const { moduleId, languageCode } = req.params;
    const userEmail = req.user.email;

    console.log('=== SUBMIT QUIZ BY MODULE DEBUG ===');
    console.log('moduleId:', moduleId);
    console.log('languageCode:', languageCode);
    console.log('userEmail:', userEmail);
    console.log('req.body:', JSON.stringify(req.body, null, 2));

    // Get video for this module and language
    const video = await getVideoByModuleAndLanguage(moduleId, languageCode);
    if (!video) {
      return res.status(404).json({ error: 'Video not found for this module and language' });
    }

    console.log('Found video:', video.id);

    // Validate input
    const { error, value } = submitQuizSchema.validate(req.body);
    if (error) {
      console.log('Validation error:', error.details[0].message);
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    const { answers } = value;

    // Get quiz details
    const quiz = await getQuizByVideoId(video.id);
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    // Check if user has already passed this quiz
    const alreadyPassed = await checkQuizPassed(userEmail, quiz.id);
    if (alreadyPassed) {
      return res.status(400).json({ error: 'Quiz already passed' });
    }

    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Get all questions with correct answers
      const questions = await getQuizQuestionsForGrading(client, quiz.id);
      const questionMap = new Map(questions.map(q => [q.id, q]));

      // Validate answer format matches question type
      for (const answer of answers) {
        const question = questionMap.get(answer.questionId);
        if (!question) {
          await client.query('ROLLBACK');
          return res.status(400).json({ error: `Question ${answer.questionId} not found` });
        }
      }

      // Calculate score using service
      const { score, passed, detailedAnswers, earnedPoints } = await calculateQuizScore(client, quiz, answers, questions);

      // Create quiz attempt record
      const { attemptNumber } = await createQuizAttempt(client, userEmail, quiz, score, questions, passed, answers, detailedAnswers);

      // Update user progress
      await updateUserProgress(client, userEmail, video, passed, score);

      // Generate certificate if passed
      if (passed) {
        await generateCertificate(client, userEmail, req.user, video, score);
      }

      await client.query('COMMIT');

      res.json({
        message: passed ? 'Quiz passed! Certificate generated.' : 'Quiz completed. Please try again.',
        result: {
          score,
          passed,
          passingScore: quiz.passing_score,
          attemptNumber,
          totalQuestions: questions.length,
          correctAnswers: earnedPoints / (questions.length > 0 ? questions[0].points : 1)
        }
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Submit quiz by module error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get quiz details for editing (admin only)
router.get('/videos/:videoId/quiz/edit', requireAdmin, async (req, res) => {
  try {
    const { videoId } = req.params;

    const result = await getQuizForEditing(videoId);
    if (!result) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    res.json(result);
  } catch (error) {
    console.error('Get quiz for editing error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get quiz for a specific video
router.get('/videos/:videoId/quiz', requireAuth, async (req, res) => {
  try {
    const { videoId } = req.params;
    const userEmail = req.user.email;

    // Get video details and check if user has completed it
    const video = await getVideoById(videoId);
    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    // Check if user has completed the video first
    const videoCompleted = await checkVideoCompletion(userEmail, video.module_id, video.language_code);
    if (!videoCompleted) {
      return res.status(403).json({
        error: 'Video must be completed before taking the quiz'
      });
    }

    // Get quiz details
    const quiz = await getQuizByVideoId(videoId);
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    // Get quiz questions with options
    const questions = await getQuizQuestionsWithOptions(quiz.id);



    // Check if user has already passed this quiz
    const alreadyPassed = await checkQuizPassed(userEmail, quiz.id);

    res.json({
      quiz: {
        ...quiz,
        questions: questions
      },
      alreadyPassed
    });
  } catch (error) {
    console.error('Get quiz error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Submit quiz answers
router.post('/videos/:videoId/quiz/submit', requireAuth, async (req, res) => {
  try {
    const { videoId } = req.params;
    const userEmail = req.user.email;

    console.log('=== SUBMIT QUIZ DEBUG ===');
    console.log('videoId:', videoId);
    console.log('userEmail:', userEmail);
    console.log('req.body:', JSON.stringify(req.body, null, 2));

    // Validate input
    const { error, value } = submitQuizSchema.validate(req.body);
    if (error) {
      console.log('Validation error:', error.details[0].message);
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    const { answers } = value;

    // Get video and quiz details
    const video = await getVideoById(videoId);
    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }

    // Get quiz details
    const quiz = await getQuizByVideoId(videoId);
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    // Check if user has already passed this quiz
    const alreadyPassed = await checkQuizPassed(userEmail, quiz.id);
    if (alreadyPassed) {
      return res.status(400).json({ error: 'Quiz already passed' });
    }

    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Get all questions with correct answers
      const questions = await getQuizQuestionsForGrading(client, quiz.id);
      const questionMap = new Map(questions.map(q => [q.id, q]));

      // Validate answer format matches question type
      for (const answer of answers) {
        const question = questionMap.get(answer.questionId);
        if (!question) {
          await client.query('ROLLBACK');
          return res.status(400).json({ error: `Question ${answer.questionId} not found` });
        }
      }

      // Calculate score using service
      const { score, passed, detailedAnswers, earnedPoints } = await calculateQuizScore(client, quiz, answers, questions);

      // Create quiz attempt record
      const { attemptNumber } = await createQuizAttempt(client, userEmail, quiz, score, questions, passed, answers, detailedAnswers);

      // Update user progress
      await updateUserProgress(client, userEmail, video, passed, score);

      // Generate certificate if passed
      if (passed) {
        await generateCertificate(client, userEmail, req.user, video, score);
      }

      await client.query('COMMIT');

      res.json({
        message: passed ? 'Quiz passed! Certificate generated.' : 'Quiz completed. Please try again.',
        result: {
          score,
          passed,
          passingScore: quiz.passing_score,
          attemptNumber,
          totalQuestions: questions.length,
          correctAnswers: earnedPoints / (questions.length > 0 ? questions[0].points : 1)
        }
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Submit quiz error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new quiz (admin only)
router.post('/videos/:videoId/quiz', requireAdmin, async (req, res) => {
  try {
    const { videoId } = req.params;

    // Validate input
    const { error, value } = createQuizSchema.validate({ ...req.body, videoId: parseInt(videoId) });
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    const {
      title,
      description,
      passingScore,
      timeLimitMinutes,
      questions
    } = value;

    // Verify video exists
    const video = await getVideoById(videoId);
    if (!video) {
      return res.status(404).json({ error: 'Training video not found' });
    }

    // Check if quiz already exists for this video
    const quizExists = await checkQuizExists(videoId);
    if (quizExists) {
      return res.status(400).json({ error: 'Quiz already exists for this video' });
    }

    const client = await getClient();
    
    try {
      await client.query('BEGIN');

      // Create quiz
      const quizResult = await client.query(`
        INSERT INTO quizzes (
          video_id,
          title,
          description,
          passing_score,
          time_limit_minutes
        ) VALUES ($1, $2, $3, $4, $5)
        RETURNING id
      `, [videoId, title, description, passingScore, timeLimitMinutes]);

      const quizId = quizResult.rows[0].id;

      // Create questions and options
      for (let i = 0; i < questions.length; i++) {
        const question = questions[i];
        
        const questionResult = await client.query(`
          INSERT INTO quiz_questions (
            quiz_id,
            question_text,
            question_type,
            question_order,
            points,
            question_image
          ) VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING id
        `, [quizId, question.questionText, question.questionType, i + 1, question.points, question.questionImage || null]);

        const questionId = questionResult.rows[0].id;

        // Add options for multiple choice and true/false questions
        if (question.options && question.options.length > 0) {
          for (let j = 0; j < question.options.length; j++) {
            const option = question.options[j];
            
            await client.query(`
              INSERT INTO quiz_question_options (
                question_id,
                option_text,
                is_correct,
                option_order,
                option_image
              ) VALUES ($1, $2, $3, $4, $5)
            `, [questionId, option.optionText, option.isCorrect, j + 1, option.optionImage || null]);
          }
        }
      }

      await client.query('COMMIT');

      res.status(201).json({ 
        message: 'Quiz created successfully',
        quizId
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Create quiz error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update existing quiz (admin only)
router.put('/videos/:videoId/quiz', requireAdmin, async (req, res) => {
  try {
    const { videoId } = req.params;

    console.log('=== UPDATE QUIZ DEBUG ===');
    console.log('videoId:', videoId);
    console.log('req.body:', JSON.stringify(req.body, null, 2));

    // Validate input
    const { error, value } = updateQuizSchema.validate(req.body);
    if (error) {
      console.log('Validation error:', error.details[0].message);
      console.log('Failed validation on:', error.details[0].path);
      console.log('Received value:', error.details[0].context?.value);
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    console.log('Validation passed. Validated value:', JSON.stringify(value, null, 2));

    const { title, description, passingScore, timeLimitMinutes, questions } = value;

    console.log('Extracted values:');
    console.log('- title:', title);
    console.log('- description:', description);
    console.log('- passingScore:', passingScore);
    console.log('- timeLimitMinutes:', timeLimitMinutes);
    console.log('- questions:', questions ? questions.length : 'undefined', 'questions');

    // Check if quiz exists
    const existingQuizResult = await query(
      'SELECT id FROM quizzes WHERE video_id = $1 AND is_active = true',
      [videoId]
    );

    if (existingQuizResult.rows.length === 0) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    const quizId = existingQuizResult.rows[0].id;
    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Update quiz details
      const updateFields = [];
      const updateValues = [];
      let paramCount = 1;

      if (title !== undefined) {
        updateFields.push(`title = $${paramCount++}`);
        updateValues.push(title);
      }
      if (description !== undefined) {
        updateFields.push(`description = $${paramCount++}`);
        updateValues.push(description);
      }
      if (passingScore !== undefined) {
        updateFields.push(`passing_score = $${paramCount++}`);
        updateValues.push(passingScore);
      }
      if (timeLimitMinutes !== undefined) {
        updateFields.push(`time_limit_minutes = $${paramCount++}`);
        updateValues.push(timeLimitMinutes);
      }

      if (updateFields.length > 0) {
        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        updateValues.push(quizId);

        await client.query(`
          UPDATE quizzes
          SET ${updateFields.join(', ')}
          WHERE id = $${paramCount}
        `, updateValues);
      }

      // Update questions if provided
      if (questions && questions.length > 0) {
        // Delete existing questions and options
        await client.query('DELETE FROM quiz_questions WHERE quiz_id = $1', [quizId]);

        // Create new questions and options
        for (let i = 0; i < questions.length; i++) {
          const question = questions[i];

          const questionResult = await client.query(`
            INSERT INTO quiz_questions (
              quiz_id,
              question_text,
              question_type,
              question_order,
              points,
              question_image
            ) VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id
          `, [quizId, question.questionText, question.questionType, i + 1, question.points, question.questionImage || null]);

          const questionId = questionResult.rows[0].id;

          // Add options for multiple choice and true/false questions
          if (question.options && question.options.length > 0) {
            for (let j = 0; j < question.options.length; j++) {
              const option = question.options[j];

              await client.query(`
                INSERT INTO quiz_question_options (
                  question_id,
                  option_text,
                  is_correct,
                  option_order,
                  option_image
                ) VALUES ($1, $2, $3, $4, $5)
              `, [questionId, option.optionText, option.isCorrect, j + 1, option.optionImage || null]);
            }
          }
        }
      }

      await client.query('COMMIT');

      res.json({
        message: 'Quiz updated successfully',
        quizId
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Update quiz error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete quiz (admin only)
router.delete('/videos/:videoId/quiz', requireAdmin, async (req, res) => {
  try {
    const { videoId } = req.params;

    // Check if quiz exists
    const existingQuizResult = await query(
      'SELECT id FROM quizzes WHERE video_id = $1',
      [videoId]
    );

    if (existingQuizResult.rows.length === 0) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    const quizId = existingQuizResult.rows[0].id;
    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Delete quiz answers first (foreign key constraint)
      await client.query(
        'DELETE FROM quiz_attempt_answers WHERE attempt_id IN (SELECT id FROM quiz_attempts WHERE quiz_id = $1)',
        [quizId]
      );

      // Delete quiz attempts
      await client.query(
        'DELETE FROM quiz_attempts WHERE quiz_id = $1',
        [quizId]
      );

      // Delete quiz question options
      await client.query(
        'DELETE FROM quiz_question_options WHERE question_id IN (SELECT id FROM quiz_questions WHERE quiz_id = $1)',
        [quizId]
      );

      // Delete quiz questions
      await client.query(
        'DELETE FROM quiz_questions WHERE quiz_id = $1',
        [quizId]
      );

      // Delete the quiz itself
      await client.query(
        'DELETE FROM quizzes WHERE id = $1',
        [quizId]
      );

      await client.query('COMMIT');

      res.json({ message: 'Quiz deleted successfully' });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Delete quiz error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
