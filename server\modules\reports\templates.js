/**
 * HTML report template generation for compliance reports
 */
function generateComplianceHTMLReport(summary, data, filters) {
  const currentDate = new Date().toLocaleDateString();
  const currentTime = new Date().toLocaleTimeString();

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Training Compliance Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .summary {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .summary h2 {
            margin-top: 0;
            color: #495057;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .status-passed {
            color: #28a745;
            font-weight: bold;
        }
        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }
        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        @media print {
            body { margin: 0; }
            .header { page-break-after: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Training Compliance Report</h1>
        <p>Generated on ${currentDate} at ${currentTime}</p>
        ${filters.moduleId ? `<p>Module ID: ${filters.moduleId}</p>` : ''}
        ${filters.startDate ? `<p>Date Range: ${filters.startDate} to ${filters.endDate || 'Present'}</p>` : ''}
    </div>

    <div class="summary">
        <h2>Executive Summary</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">${summary.total_users || 0}</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${summary.total_modules || 0}</div>
                <div class="stat-label">Training Modules</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${summary.users_completed_videos || 0}</div>
                <div class="stat-label">Videos Completed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${summary.users_passed_quizzes || 0}</div>
                <div class="stat-label">Quizzes Passed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${summary.certificates_issued || 0}</div>
                <div class="stat-label">Certificates Issued</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${summary.average_quiz_score || 0}%</div>
                <div class="stat-label">Average Quiz Score</div>
            </div>
        </div>
    </div>

    <h2>Detailed Training Records</h2>
    <table>
        <thead>
            <tr>
                <th>User Email</th>
                <th>Name</th>
                <th>Module</th>
                <th>Language</th>
                <th>Video Status</th>
                <th>Quiz Status</th>
                <th>Score</th>
                <th>Certificate</th>
                <th>Completion Date</th>
            </tr>
        </thead>
        <tbody>
            ${data.map(row => `
                <tr>
                    <td>${row.email || 'N/A'}</td>
                    <td>${(row.first_name || '') + ' ' + (row.last_name || '')}</td>
                    <td>${row.module_title || 'N/A'}</td>
                    <td>${row.language_code || 'N/A'}</td>
                    <td class="${row.video_watched ? 'status-passed' : 'status-pending'}">
                        ${row.video_watched ? 'Completed' : 'Pending'}
                    </td>
                    <td class="${row.quiz_passed ? 'status-passed' : (row.quiz_completed ? 'status-failed' : 'status-pending')}">
                        ${row.quiz_passed ? 'Passed' : (row.quiz_completed ? 'Failed' : 'Pending')}
                    </td>
                    <td>${row.quiz_score || 'N/A'}${row.quiz_score ? '%' : ''}</td>
                    <td class="${row.certificate_number ? 'status-passed' : 'status-pending'}">
                        ${row.certificate_number ? row.certificate_number : 'Not Issued'}
                    </td>
                    <td>${row.quiz_completed_at ? new Date(row.quiz_completed_at).toLocaleDateString() : 'N/A'}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="footer">
        <p>This report was generated automatically by the Internal Training System.</p>
        <p>For questions or concerns, please contact the system administrator.</p>
    </div>
</body>
</html>`;
}

module.exports = {
  generateComplianceHTMLReport
};
