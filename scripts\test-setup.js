#!/usr/bin/env node

// Simple test script to verify the setup

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Internal Training System Setup...\n');

const tests = [
  {
    name: 'Root package.json exists',
    test: () => fs.existsSync('package.json'),
  },
  {
    name: 'Server package.json exists',
    test: () => fs.existsSync('server/package.json'),
  },
  {
    name: 'Client package.json exists',
    test: () => fs.existsSync('client/package.json'),
  },
  {
    name: 'Server node_modules exists',
    test: () => fs.existsSync('server/node_modules'),
  },
  {
    name: 'Client node_modules exists',
    test: () => fs.existsSync('client/node_modules'),
  },
  {
    name: 'Server .env file exists',
    test: () => fs.existsSync('server/.env'),
  },
  {
    name: 'Database init script exists',
    test: () => fs.existsSync('server/database/init.sql'),
  },
  {
    name: 'Docker compose file exists',
    test: () => fs.existsSync('docker-compose.yml'),
  },
  {
    name: 'Client src directory exists',
    test: () => fs.existsSync('client/src'),
  },
  {
    name: 'Server routes directory exists',
    test: () => fs.existsSync('server/routes'),
  },
  {
    name: 'Uploads directory exists',
    test: () => fs.existsSync('server/uploads'),
  },
  {
    name: 'Main React components exist',
    test: () => {
      const components = [
        'client/src/App.jsx',
        'client/src/main.jsx',
        'client/src/pages/Login.jsx',
        'client/src/pages/Dashboard.jsx',
      ];
      return components.every(comp => fs.existsSync(comp));
    },
  },
  {
    name: 'Server routes exist',
    test: () => {
      const routes = [
        'server/routes/auth.js',
        'server/routes/users.js',
        'server/routes/training.js',
        'server/routes/quiz.js',
        'server/routes/reports.js',
      ];
      return routes.every(route => fs.existsSync(route));
    },
  },
  {
    name: 'Admin components exist',
    test: () => {
      const adminComponents = [
        'client/src/pages/Admin/AdminDashboard.jsx',
        'client/src/pages/Admin/UserManagement.jsx',
        'client/src/pages/Admin/ModuleManagement.jsx',
        'client/src/pages/Admin/QuizManagement.jsx',
        'client/src/pages/Admin/Reports.jsx',
      ];
      return adminComponents.every(comp => fs.existsSync(comp));
    },
  },
];

let passed = 0;
let failed = 0;

tests.forEach((test, index) => {
  try {
    const result = test.test();
    if (result) {
      console.log(`✅ ${test.name}`);
      passed++;
    } else {
      console.log(`❌ ${test.name}`);
      failed++;
    }
  } catch (error) {
    console.log(`❌ ${test.name} (Error: ${error.message})`);
    failed++;
  }
});

console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed\n`);

if (failed === 0) {
  console.log('🎉 All tests passed! Your setup looks good.');
  console.log('\n📋 Next steps:');
  console.log('1. Setup PostgreSQL database');
  console.log('2. Update server/.env with your database credentials');
  console.log('3. Run: npm run dev');
  console.log('4. Open: http://localhost:3000');
} else {
  console.log('⚠️  Some tests failed. Please check the setup.');
  console.log('\n🔧 Common fixes:');
  console.log('- Run: npm install');
  console.log('- Run: cd server && npm install');
  console.log('- Run: cd client && npm install');
  console.log('- Create: server/uploads directory');
}

console.log('\n📚 For help, check README.md or run the setup script.');

process.exit(failed > 0 ? 1 : 0);
