# =============================================================================
# AZURE-OPTIMIZED FRONTEND DOCKERFILE
# =============================================================================

# Build stage
FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files first for better Docker layer caching
COPY client/package*.json ./

# Install dependencies including dev dependencies for build
RUN npm ci --prefer-offline --no-audit --no-fund

# Copy source files
COPY client/vite.config.js ./
COPY client/index.html ./
COPY .env ./.env
COPY client/src/ ./src/

# Accept build arguments for React app
ARG REACT_APP_API_URL
ARG NODE_ENV=production

# Set environment variables for React + Vite build
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV NODE_ENV=$NODE_ENV

# Build the application with Vite
RUN npm run build

# Production stage - Azure optimized
FROM node:22-alpine AS production

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache \
    wget \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

WORKDIR /app

# Install runtime dependencies (including express for serving)
COPY client/package*.json ./
RUN npm ci --prefer-offline --no-audit --no-fund && \
    npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Copy server file
COPY client/server.js ./

# Set proper ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose Azure standard port
EXPOSE 8080

# Health check optimized for Azure
HEALTHCHECK --interval=30s --timeout=15s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

# Use dumb-init to handle signals properly in containers
ENTRYPOINT ["dumb-init", "--"]

# Start the server on Azure standard port
CMD ["sh", "-c", "PORT=8080 node server.js"]
