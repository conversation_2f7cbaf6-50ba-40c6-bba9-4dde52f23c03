import React, { useState } from 'react'
import { Card, Table, Button, Space, Typography, Modal, Form, Input, Upload, message, Select, Progress, Tag, Switch } from 'antd'
import { PlusOutlined, UploadOutlined, PlayCircleOutlined, DeleteOutlined, QuestionCircleOutlined, EditOutlined, SwapOutlined } from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import api, { trainingAPI, quizAPI, handleAPIError } from '../../services/api'
import systemConfigAPI from '../../services/systemConfigAPI'
import ChunkedUploadService from '../../services/chunkedUpload'
import VideoReplacementService from '../../services/videoReplacementService'
import { useLanguage } from '../../contexts/LanguageContext'
import QuizManagement from '../../components/QuizManagement'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select


// Component to show video details and quiz management for a module
function ModuleVideoDetails({ moduleId }) {
  const { t } = useTranslation()
  const { data: moduleDetails, isLoading } = useQuery(
    ['module-details', moduleId],
    () => trainingAPI.getModule(moduleId),
    {
      select: (response) => response.data,
    }
  )

  const queryClient = useQueryClient()

  // Helper function to validate and convert video ID
  const validateVideoId = (videoId) => {
    const numericVideoId = Number(videoId);
    if (isNaN(numericVideoId)) {
      throw new Error('Invalid video ID');
    }
    return numericVideoId;
  };

  // Helper function to invalidate queries after mutations
  const invalidateQueries = () => {
    queryClient.invalidateQueries(['module-details', moduleId])
    queryClient.invalidateQueries('training-modules')
  };

  // Quiz mutations for the video details component - optimized with shared logic
  const createQuizMutation = useMutation(
    ({ videoId, quizData }) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Creating quiz for video:', videoId, 'with data:', quizData);
      }
      return quizAPI.createQuiz(validateVideoId(videoId), quizData);
    },
    {
      onSuccess: () => {
        invalidateQueries();
        message.success(t('moduleManagement.quizCreatedSuccessfully'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToCreateQuiz')}: ${errorInfo.message}`)
      }
    }
  )

  const updateQuizMutation = useMutation(
    ({ videoId, quizData }) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Updating quiz for video:', videoId, 'with data:', quizData);
      }
      return quizAPI.updateQuiz(validateVideoId(videoId), quizData);
    },
    {
      onSuccess: () => {
        invalidateQueries();
        message.success(t('moduleManagement.quizUpdatedSuccessfully'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToUpdateQuiz')}: ${errorInfo.message}`)
      }
    }
  )

  const deleteQuizMutation = useMutation(
    (videoId) => quizAPI.deleteQuiz(validateVideoId(videoId)),
    {
      onSuccess: () => {
        invalidateQueries();
        message.success(t('moduleManagement.quizDeletedSuccessfully'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToDeleteQuiz')}: ${errorInfo.message}`)
      }
    }
  )

  const deleteVideoMutation = useMutation(
    (videoId) => trainingAPI.deleteVideo(validateVideoId(videoId)),
    {
      onSuccess: () => {
        invalidateQueries();
        message.success(t('moduleManagement.videoDeletedSuccessfully'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToDeleteVideo')}: ${errorInfo.message}`)
      }
    }
  )

  const updateVideoLanguageNameMutation = useMutation(
    ({ videoId, languageName }) => trainingAPI.updateVideoLanguageName(validateVideoId(videoId), languageName),
    {
      onSuccess: () => {
        invalidateQueries();
        setEditLanguageModalVisible(false);
        editLanguageForm.resetFields();
        message.success(t('moduleManagement.languageNameUpdatedSuccessfully'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToUpdateLanguageName')}: ${errorInfo.message}`)
      }
    }
  )

  // Video replacement mutation (using chunked upload which handles replacement automatically)
  const replaceVideoMutation = useMutation(
    async ({ moduleId, languageCode, file, languageName }) => {
      console.log('=== VIDEO REPLACEMENT VIA CHUNKED UPLOAD ===');
      console.log('Module ID:', moduleId);
      console.log('Language Code:', languageCode);
      console.log('File:', file.name, `(${(file.size / 1024 / 1024).toFixed(2)}MB)`);
      console.log('Language Name:', languageName);

      // Use chunked upload system which automatically handles video replacement
      // when a video already exists for the same module and language
      const uploadService = new ChunkedUploadService();
      setVideoReplacementService(uploadService);

      const uploadResult = await uploadService.upload(moduleId, file, {
        languageCode: languageCode,
        languageName: languageName
      }, {
        onProgress: (progress) => {
          // Show replacement progress (0-95% of total progress)
          const adjustedProgress = Math.round(progress.percentage * 0.95);
          setReplaceProgress(adjustedProgress);
          setReplaceDetails({
            uploadedChunks: progress.uploadedChunks,
            totalChunks: progress.totalChunks,
            uploadedBytes: progress.uploadedBytes,
            totalBytes: progress.totalBytes,
            percentage: adjustedProgress,
            message: `Replacing video... ${progress.percentage}% (${progress.uploadedChunks}/${progress.totalChunks} chunks)`
          });
        },
        onError: (message, error) => {
          console.error('Video replacement error:', message, error);
          throw error;
        },
        onComplete: (result) => {
          console.log('Video replacement completed:', result);
          setReplaceProgress(100);
          setReplaceDetails(prev => ({
            ...prev,
            percentage: 100,
            message: 'Video replacement completed successfully!'
          }));
        },
        concurrency: 3 // Use existing upload concurrency
      });

      console.log('Video replacement completed via chunked upload:', uploadResult);
      setReplaceProgress(100);
      setReplaceDetails(prev => ({
        ...prev,
        percentage: 100,
        message: 'Video replacement completed successfully!'
      }));

      return uploadResult;
    },
    {
      onSuccess: (result) => {
        console.log('Video replacement successful:', result);
        invalidateQueries();
        setReplaceVideoVisible(false);
        replaceVideoForm.resetFields();
        setReplaceProgress(0);
        setReplaceDetails(null);
        setSelectedVideoForReplacement(null);
        setVideoReplacementService(null);

        // Show success message based on whether it was a replacement or new upload
        if (result.replaced) {
          message.success(t('moduleManagement.videoReplacedSuccessfully'));
        } else {
          message.success(t('moduleManagement.videoUploadedSuccessfully'));
        }
      },
      onError: (error) => {
        console.error('Video replacement failed:', error);
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToReplaceVideo')}: ${errorInfo.message}`)
        setReplaceProgress(0);
        setReplaceDetails(null);
        setVideoReplacementService(null);
      }
    }
  )



  const [quizModalVisible, setQuizModalVisible] = useState(false)
  const [selectedVideo, setSelectedVideo] = useState(null)
  const [quizMode, setQuizMode] = useState('create')
  const [editLanguageModalVisible, setEditLanguageModalVisible] = useState(false)
  const [editLanguageForm] = Form.useForm()
  const [replaceVideoVisible, setReplaceVideoVisible] = useState(false)
  const [replaceVideoForm] = Form.useForm()
  const [replaceProgress, setReplaceProgress] = useState(0)
  const [replaceDetails, setReplaceDetails] = useState(null)
  const [selectedVideoForReplacement, setSelectedVideoForReplacement] = useState(null)
  const [videoReplacementService, setVideoReplacementService] = useState(null)

  const showCreateQuizModal = (video) => {
    setSelectedVideo(video)
    setQuizMode('create')
    setQuizModalVisible(true)
  }

  const showEditQuizModal = async (video) => {
    setSelectedVideo(video)
    setQuizMode('edit')

    // Fetch quiz details with questions for editing
    if (video.quiz_id) {
      try {
        const response = await quizAPI.getQuizForEdit(video.id)
        const quizData = response.data.quiz

        // Update selected video with full quiz data
        setSelectedVideo({
          ...video,
          quizData: quizData
        })
      } catch (error) {
        console.error('Failed to fetch quiz details:', error)
        message.error(t('moduleManagement.failedToLoadQuizDetails'))
      }
    }

    setQuizModalVisible(true)
  }

  const handleDeleteQuiz = (video) => {
    Modal.confirm({
      title: t('moduleManagement.deleteQuiz'),
      content: (
        <div>
          <p>{t('moduleManagement.deleteQuizConfirm', { language: video.language_name })}</p>
          <p style={{ color: '#ff4d4f', fontSize: '14px' }}>
            {t('moduleManagement.deleteQuizWarning')}
            <br />• {t('moduleManagement.removeQuizAndQuestions')}
            <br />• {t('moduleManagement.deleteUserAttempts')}
            <br />• {t('moduleManagement.actionCannotBeUndone')}
          </p>
        </div>
      ),
      okText: t('common.delete'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk: () => {
        deleteQuizMutation.mutate(video.id)
      },
    })
  }

  const handleDeleteVideo = (video) => {
    Modal.confirm({
      title: t('moduleManagement.deleteVideo'),
      content: (
        <div>
          <p>{t('moduleManagement.deleteVideoConfirm', { language: video.language_name })}</p>
          <p style={{ color: '#ff4d4f', fontSize: '14px' }}>
            {t('moduleManagement.deleteVideoWarning')}
            <br />• {t('moduleManagement.removeVideoFile')}
            <br />• {t('moduleManagement.deleteAssociatedQuiz')}
            <br />• {t('moduleManagement.deleteUserProgress')}
            <br />• {t('moduleManagement.deleteCertificates')}
            <br />• {t('moduleManagement.actionCannotBeUndone')}
          </p>
        </div>
      ),
      okText: t('moduleManagement.deleteVideo'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk: () => {
        deleteVideoMutation.mutate(video.id)
      },
    })
  }

  const handleQuizSubmit = (quizData) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Quiz submit - mode:', quizMode, 'video:', selectedVideo?.id, 'data:', quizData);
    }

    if (!selectedVideo?.id) {
      message.error('No video selected');
      return;
    }

    const videoId = selectedVideo.id;

    if (quizMode === 'create') {
      createQuizMutation.mutate({ videoId, quizData })
    } else if (quizMode === 'edit') {
      updateQuizMutation.mutate({ videoId, quizData })
    }
    setQuizModalVisible(false)
  }

  const showEditLanguageModal = (video) => {
    setSelectedVideo(video)
    editLanguageForm.setFieldsValue({
      languageName: video.language_name
    })
    setEditLanguageModalVisible(true)
  }

  const handleEditLanguageSubmit = (values) => {
    if (!selectedVideo?.id) {
      message.error('No video selected');
      return;
    }

    updateVideoLanguageNameMutation.mutate({
      videoId: selectedVideo.id,
      languageName: values.languageName
    })
  }

  const showReplaceVideoModal = (video) => {
    console.log('=== REPLACE VIDEO MODAL DEBUG ===');
    console.log('Video record:', video);
    console.log('Module ID:', video.module_id);
    console.log('Language Code:', video.language_code);

    setSelectedVideoForReplacement(video)
    replaceVideoForm.setFieldsValue({
      languageName: video.language_name
    })
    setReplaceVideoVisible(true)
  }

  const handleReplaceVideo = (values) => {
    if (!selectedVideoForReplacement) {
      message.error('No video selected for replacement');
      return;
    }

    const file = values.video.file

    // Calculate file size and show initial info
    const fileSizeMB = (file.size / 1024 / 1024).toFixed(2)
    const totalChunks = Math.ceil(file.size / (10 * 1024 * 1024)) // 10MB chunks

    console.log('=== REPLACE VIDEO SUBMIT DEBUG ===');
    console.log('Selected video for replacement:', selectedVideoForReplacement);
    console.log('Module ID:', selectedVideoForReplacement.module_id);
    console.log('Language Code:', selectedVideoForReplacement.language_code);
    console.log(`Starting chunked video replacement: ${file.name} (${fileSizeMB}MB, ${totalChunks} chunks)`);

    replaceVideoMutation.mutate({
      moduleId: selectedVideoForReplacement.module_id,
      languageCode: selectedVideoForReplacement.language_code,
      file: file,
      languageName: values.languageName
    })
  }

  const handleCancelReplacement = async () => {
    // Simple cancellation - just reset the UI state
    setReplaceProgress(0)
    setReplaceDetails(null)
    setVideoReplacementService(null)
    message.info('Video replacement cancelled')
  }

  if (isLoading) {
    return <div style={{ padding: 16, textAlign: 'center' }}>{t('moduleManagement.loadingVideos')}</div>
  }

  if (!moduleDetails?.videos || moduleDetails.videos.length === 0) {
    return <div style={{ padding: 16, textAlign: 'center', color: '#999' }}>{t('moduleManagement.noVideosUploaded')}</div>
  }

  const videoColumns = [
    {
      title: t('moduleManagement.languageName'),
      dataIndex: 'language_name',
      key: 'language_name',
      render: (name, record) => (
        <Space>
          <Text strong>{name}</Text>
          <Text type="secondary">({record.language_code})</Text>
        </Space>
      )
    },
    {
      title: t('moduleManagement.videoFilename'),
      dataIndex: 'video_filename',
      key: 'video_filename',
      render: (filename) => <Text code>{filename}</Text>
    },
    {
      title: t('moduleManagement.duration'),
      dataIndex: 'duration_seconds',
      key: 'duration_seconds',
      render: (seconds) => seconds ? `${Math.round(seconds / 60)}m` : '-'
    },
    {
      title: t('moduleManagement.quizStatus'),
      key: 'quiz_status',
      render: (_, record) => {
        if (record.quiz_id) {
          return <Tag color="green">{t('moduleManagement.quizAvailable')}</Tag>
        }
        return <Tag color="orange">{t('moduleManagement.noQuiz')}</Tag>
      }
    },
    {
      title: t('moduleManagement.quizActions'),
      key: 'quiz_actions',
      render: (_, record) => {
        if (record.quiz_id) {
          return (
            <Space>
              <Button
                size="small"
                type="default"
                icon={<QuestionCircleOutlined />}
                onClick={() => showEditQuizModal(record)}
              >
                {t('moduleManagement.editQuiz')}
              </Button>
              <Button
                size="small"
                danger
                onClick={() => handleDeleteQuiz(record)}
              >
                {t('moduleManagement.deleteQuiz')}
              </Button>
            </Space>
          )
        }
        return (
          <Button
            size="small"
            type="primary"
            icon={<QuestionCircleOutlined />}
            onClick={() => showCreateQuizModal(record)}
          >
            {t('moduleManagement.createQuiz')}
          </Button>
        )
      }
    },
    {
      title: t('moduleManagement.videoActions'),
      key: 'video_actions',
      render: (_, record) => (
        <Space>
          <Button
            size="small"
            icon={<SwapOutlined />}
            onClick={() => showReplaceVideoModal(record)}
            type="default"
          >
            {t('moduleManagement.replaceVideo')}
          </Button>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditLanguageModal(record)}
          >
            {t('moduleManagement.editLanguage')}
          </Button>
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteVideo(record)}
            loading={deleteVideoMutation.isLoading}
          >
            {t('moduleManagement.deleteVideo')}
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: 16, background: '#fafafa' }}>
      <Title level={5} style={{ marginBottom: 16 }}>{t('moduleManagement.videosAndQuizManagement')}</Title>
      <Table
        columns={videoColumns}
        dataSource={moduleDetails.videos}
        rowKey="id"
        pagination={false}
        size="small"
      />

      <QuizManagement
        visible={quizModalVisible}
        onCancel={() => setQuizModalVisible(false)}
        onSubmit={handleQuizSubmit}
        mode={quizMode}
        video={selectedVideo}
        initialData={selectedVideo?.quiz_id ? (selectedVideo.quizData || {
          title: selectedVideo.quiz_title,
          description: selectedVideo.quiz_description,
          passing_score: selectedVideo.passing_score,
          time_limit_minutes: selectedVideo.time_limit_minutes,
          questions: []
        }) : null}
        loading={createQuizMutation.isLoading || updateQuizMutation.isLoading || deleteQuizMutation.isLoading}
      />

      {/* Edit Language Name Modal */}
      <Modal
        title={t('moduleManagement.editLanguageName')}
        open={editLanguageModalVisible}
        onCancel={() => setEditLanguageModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form
          form={editLanguageForm}
          layout="vertical"
          onFinish={handleEditLanguageSubmit}
        >
          <Form.Item
            name="languageName"
            label={t('moduleManagement.languageDisplayName')}
            rules={[
              { required: true, message: t('moduleManagement.pleaseEnterLanguageDisplayName') },
              { max: 50, message: t('moduleManagement.languageNameMaxLength') }
            ]}
          >
            <Input placeholder="e.g., English, 中文, Español" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={updateVideoLanguageNameMutation.isLoading}
              >
                {t('common.update')}
              </Button>
              <Button onClick={() => setEditLanguageModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Replace Video Modal */}
      <Modal
        title={t('moduleManagement.replaceVideoTitle', {
          language: selectedVideoForReplacement?.language_name,
          filename: selectedVideoForReplacement?.video_filename
        })}
        open={replaceVideoVisible}
        onCancel={() => setReplaceVideoVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            {t('moduleManagement.replaceVideoDescription')}
          </Text>
        </div>

        <Form
          form={replaceVideoForm}
          layout="vertical"
          onFinish={handleReplaceVideo}
        >
          <Form.Item
            name="video"
            label={t('moduleManagement.newVideoFile')}
            rules={[{ required: true, message: t('moduleManagement.pleaseSelectNewVideoFile') }]}
          >
            <Upload
              beforeUpload={(file) => {
                console.log('=== VIDEO REPLACEMENT FILE VALIDATION ===');
                console.log('File name:', file.name);
                console.log('File type:', file.type);
                console.log('File size:', `${(file.size / 1024 / 1024).toFixed(2)}MB`);

                const isVideo = file.type.startsWith('video/')
                if (!isVideo) {
                  message.error(`${t('moduleManagement.videoUploadError')} (${file.type})`)
                  return false
                }

                const fileSizeMB = file.size / 1024 / 1024
                message.info(`File selected for replacement: ${file.name} (${fileSizeMB.toFixed(2)}MB)`, 3)

                return false // Prevent auto upload
              }}
              maxCount={1}
              accept="video/*"
            >
              <Button icon={<UploadOutlined />}>
                {t('moduleManagement.selectVideoFile')}
              </Button>
            </Upload>
          </Form.Item>

          <Form.Item
            name="languageName"
            label={t('moduleManagement.languageDisplayName')}
            rules={[
              { required: true, message: t('moduleManagement.pleaseEnterLanguageDisplayName') },
              { max: 50, message: t('moduleManagement.languageNameMaxLength') }
            ]}
          >
            <Input placeholder="e.g., English, 中文, Español" />
          </Form.Item>

          {replaceVideoMutation.isLoading && (
            <Form.Item>
              <div style={{ marginBottom: 16 }}>
                <Progress
                  percent={replaceProgress}
                  status="active"
                  format={(percent) => `${t('moduleManagement.replacingVideo')} ${percent}%`}
                />
                {replaceDetails && (
                  <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                    <div>
                      {t('moduleManagement.uploadProgress')}: {replaceDetails.uploadedChunks}/{replaceDetails.totalChunks} chunks
                    </div>
                    <div>
                      {t('moduleManagement.uploadedSize')}: {(replaceDetails.uploadedBytes / 1024 / 1024).toFixed(2)}MB / {(replaceDetails.totalBytes / 1024 / 1024).toFixed(2)}MB
                    </div>
                  </div>
                )}
              </div>
              <Button
                onClick={handleCancelReplacement}
                size="small"
                danger
              >
                {t('moduleManagement.cancelReplacement')}
              </Button>
            </Form.Item>
          )}

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={replaceVideoMutation.isLoading}
                danger
              >
                {t('moduleManagement.replaceVideo')}
              </Button>
              <Button
                onClick={() => setReplaceVideoVisible(false)}
                disabled={replaceVideoMutation.isLoading}
              >
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

function ModuleManagement() {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { contentLanguageOptions } = useLanguage()
  const [createModuleVisible, setCreateModuleVisible] = useState(false)
  const [uploadVideoVisible, setUploadVideoVisible] = useState(false)
  const [selectedModule, setSelectedModule] = useState(null)
  const [createModuleForm] = Form.useForm()
  const [uploadVideoForm] = Form.useForm()
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadDetails, setUploadDetails] = useState(null)
  const [chunkedUploadService, setChunkedUploadService] = useState(null)
  const queryClient = useQueryClient()

  // Fetch upload configuration
  const { data: uploadConfig, error: uploadConfigError, isLoading: uploadConfigLoading } = useQuery(
    'upload-config',
    systemConfigAPI.getUploadConfig,
    {
      select: (response) => {
        console.log('Upload config response:', response.data);
        return response.data?.data;
      },
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
      retry: 3,
      onSuccess: (data) => {
        console.log('Upload config loaded successfully:', data);
      },
      onError: (error) => {
        console.error('Failed to load upload config:', error);
        console.error('Error details:', error.response?.data || error.message);
      }
    }
  )

  // Debug logging
  React.useEffect(() => {
    console.log('Upload config state:', {
      uploadConfig,
      uploadConfigError,
      uploadConfigLoading
    });
  }, [uploadConfig, uploadConfigError, uploadConfigLoading]);

  // Fetch training modules
  const { data: modulesData, isLoading } = useQuery(
    'training-modules',
    trainingAPI.getModules,
    {
      select: (response) => response.data.modules,
    }
  )

  // Create module mutation
  const createModuleMutation = useMutation(
    (moduleData) => trainingAPI.createModule(moduleData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('training-modules')
        setCreateModuleVisible(false)
        createModuleForm.resetFields()
        message.success(t('moduleManagement.moduleCreatedSuccessfully'))
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToCreateModule')}: ${errorInfo.message}`)
      }
    }
  )

  // Chunked upload video mutation
  const uploadVideoMutation = useMutation(
    async ({ moduleId, file, languageCode, languageName }) => {
      const uploadService = new ChunkedUploadService()
      setChunkedUploadService(uploadService)

      return uploadService.upload(
        moduleId,
        file,
        { languageCode, languageName },
        {
          onProgress: (progress) => {
            setUploadProgress(progress.percentage)
            setUploadDetails({
              uploadedChunks: progress.uploadedChunks,
              totalChunks: progress.totalChunks,
              uploadedBytes: progress.uploadedBytes,
              totalBytes: progress.totalBytes,
              percentage: progress.percentage
            })
          },
          onError: (message, error) => {
            console.error('Chunked upload error:', message, error)
          },
          concurrency: 3 // Upload 3 chunks simultaneously
        }
      )
    },
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries('training-modules')
        setUploadVideoVisible(false)
        uploadVideoForm.resetFields()
        setUploadProgress(0)
        setUploadDetails(null)
        setChunkedUploadService(null)

        const wasReplaced = response.replaced;
        if (wasReplaced) {
          message.success(t('moduleManagement.videoReplacedSuccessfully'))
        } else {
          message.success(t('moduleManagement.videoUploadedSuccessfully'))
        }
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToUploadVideo')}: ${errorInfo.message}`)
        setUploadProgress(0)
        setUploadDetails(null)
        setChunkedUploadService(null)
      }
    }
  )

  // Toggle module status mutation
  const toggleModuleStatusMutation = useMutation(
    ({ moduleId, isActive }) => trainingAPI.toggleModuleStatus(moduleId, isActive),
    {
      onSuccess: (response, variables) => {
        queryClient.invalidateQueries('training-modules')
        const statusText = variables.isActive ? t('moduleManagement.activated') : t('moduleManagement.deactivated')
        message.success(`${t('moduleManagement.module')} ${statusText} ${t('moduleManagement.successfully')}`)
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToToggleModuleStatus')}: ${errorInfo.message}`)
      }
    }
  )

  // Delete module mutation
  const deleteModuleMutation = useMutation(
    (moduleId) => trainingAPI.deleteModule(moduleId),
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries('training-modules')

        // Show different success messages based on the action performed
        if (response.data.action === 'soft_delete') {
          message.success(t('moduleManagement.moduleDeactivatedSuccessfully'))
        } else if (response.data.action === 'hard_delete') {
          message.success(t('moduleManagement.modulePermanentlyDeletedSuccessfully'))
        } else {
          // Fallback message
          message.success(t('moduleManagement.moduleDeletedSuccessfully'))
        }
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('moduleManagement.failedToDeleteModule')}: ${errorInfo.message}`)
      }
    }
  )



  const handleCreateModule = (values) => {
    createModuleMutation.mutate(values)
  }

  const handleToggleModuleStatus = (moduleId, currentStatus) => {
    const newStatus = !currentStatus
    toggleModuleStatusMutation.mutate({ moduleId, isActive: newStatus })
  }

  const handleUploadVideo = (values) => {
    const file = values.video.file

    // Calculate file size and show initial info
    const fileSizeMB = (file.size / 1024 / 1024).toFixed(2)
    const totalChunks = Math.ceil(file.size / (10 * 1024 * 1024)) // 10MB chunks

    console.log(`Starting chunked upload: ${file.name} (${fileSizeMB}MB, ${totalChunks} chunks)`)

    uploadVideoMutation.mutate({
      moduleId: selectedModule.id,
      file: file,
      languageCode: values.languageCode,
      languageName: values.languageName
    })
  }

  const handleCancelUpload = async () => {
    if (chunkedUploadService && selectedModule) {
      try {
        await chunkedUploadService.abort(selectedModule.id)
        message.info('Upload cancelled and cleaned up')
      } catch (error) {
        console.error('Error cancelling upload:', error)
        message.warning('Upload cancelled, but cleanup may have failed')
      }
      setChunkedUploadService(null)
    }
    setUploadProgress(0)
    setUploadDetails(null)
  }



  const showUploadModal = (module) => {
    setSelectedModule(module)
    setUploadVideoVisible(true)
  }

  const handleViewModule = async (moduleId) => {
    try {
      // Fetch module details to get available languages
      const response = await trainingAPI.getModule(moduleId)
      const { videos } = response.data

      if (!videos || videos.length === 0) {
        message.warning(t('trainingModules.noVideosWarning'))
        return
      }

      // Navigate to video player with the first available language
      navigate(`/training/${moduleId}/video/${videos[0].language_code}`)
    } catch (error) {
      console.error('Failed to fetch module details:', error)
      message.error(t('moduleManagement.failedToLoadModule'))
    }
  }

  const handleDeleteModule = (module) => {
    if (module.is_active) {
      // Active module - show deactivation warning (soft delete)
      Modal.confirm({
        title: t('moduleManagement.deactivateTrainingModule'),
        content: (
          <div>
            <p>{t('moduleManagement.deactivateModuleConfirm', { title: module.title })}</p>
            <p style={{ color: '#faad14', fontSize: '14px' }}>
              {t('moduleManagement.deactivateModuleWarning')}
              <br />• {t('moduleManagement.deactivateModuleOnly')}
              <br />• {t('moduleManagement.moduleCanBeReactivated')}
            </p>
          </div>
        ),
        okText: t('moduleManagement.deactivateTrainingModule'),
        okType: 'primary',
        cancelText: t('common.cancel'),
        onOk: () => {
          deleteModuleMutation.mutate(module.id)
        },
      })
    } else {
      // Inactive module - show permanent deletion warning (hard delete)
      Modal.confirm({
        title: t('moduleManagement.permanentlyDeleteTrainingModule'),
        content: (
          <div>
            <p>{t('moduleManagement.permanentDeleteModuleConfirm', { title: module.title })}</p>
            <p style={{ color: '#ff4d4f', fontSize: '14px', fontWeight: 'bold' }}>
              {t('moduleManagement.permanentDeleteModuleWarning')}
            </p>
            <div style={{ color: '#ff4d4f', fontSize: '14px', marginTop: '8px' }}>
              • {t('moduleManagement.permanentlyRemoveModuleAndVideos')}
              <br />• {t('moduleManagement.permanentlyDeleteQuizzesAndProgress')}
              <br />• {t('moduleManagement.permanentlyDeleteVideoFiles')}
              <br />• {t('moduleManagement.moduleCannotBeRecovered')}
            </div>
            <p style={{
              color: '#ff4d4f',
              fontSize: '12px',
              marginTop: '12px',
              fontWeight: 'bold',
              backgroundColor: '#fff2f0',
              padding: '8px',
              borderRadius: '4px',
              border: '1px solid #ffccc7'
            }}>
              {t('moduleManagement.actionCannotBeUndone')}
            </p>
          </div>
        ),
        okText: t('moduleManagement.permanentlyDeleteTrainingModule'),
        okType: 'danger',
        cancelText: t('common.cancel'),
        onOk: () => {
          deleteModuleMutation.mutate(module.id)
        },
      })
    }
  }

  const uploadProps = {
    beforeUpload: (file) => {
      console.log('=== FRONTEND FILE VALIDATION ===');
      console.log('File name:', file.name);
      console.log('File type:', file.type);
      console.log('File size:', `${(file.size / 1024 / 1024).toFixed(2)}MB`);

      const isVideo = file.type.startsWith('video/')
      if (!isVideo) {
        message.error(`${t('moduleManagement.videoUploadError')} (${file.type})`)
        return false
      }

      // Use dynamic file size limit from server config
      const maxFileSize = uploadConfig?.maxFileSize || (500 * 1024 * 1024) // Default to 500MB
      const maxFileSizeFormatted = uploadConfig?.maxFileSizeFormatted || '500 MB'

      console.log('Upload config in beforeUpload:', uploadConfig);
      console.log('Max file size:', maxFileSize, 'Formatted:', maxFileSizeFormatted);

      const fileSizeMB = file.size / 1024 / 1024
      const isUnderLimit = file.size <= maxFileSize
      if (!isUnderLimit) {
        message.error(`${t('moduleManagement.videoSizeError')} (${fileSizeMB.toFixed(2)}MB). Maximum allowed: ${maxFileSizeFormatted}`)
        return false
      }

      // Calculate chunks for display
      const totalChunks = Math.ceil(file.size / (10 * 1024 * 1024))
      console.log(`File will be split into ${totalChunks} chunks of ~10MB each`);

      // Show file info
      message.info(`File selected: ${file.name} (${fileSizeMB.toFixed(2)}MB, ${totalChunks} chunks)`, 3)

      console.log('Frontend validation passed');
      return false // Prevent auto upload
    },
    maxCount: 1,
  }

  const columns = [
    {
      title: t('moduleManagement.moduleTitle'),
      dataIndex: 'title',
      key: 'title',
      render: (title) => <Text strong>{title}</Text>
    },
    {
      title: t('moduleManagement.description'),
      dataIndex: 'description',
      key: 'description',
      render: (description) => description || '-'
    },
    {
      title: t('moduleManagement.videos'),
      dataIndex: 'video_count',
      key: 'video_count',
      render: (count) => count || 0
    },
    {
      title: t('moduleManagement.created'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: t('moduleManagement.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive, record) => (
        <Space>
          <Switch
            checked={isActive}
            onChange={() => handleToggleModuleStatus(record.id, isActive)}
            loading={toggleModuleStatusMutation.isLoading}
            checkedChildren={t('moduleManagement.active')}
            unCheckedChildren={t('moduleManagement.inactive')}
          />
          <Tag color={isActive ? 'green' : 'red'}>
            {isActive ? t('moduleManagement.active') : t('moduleManagement.inactive')}
          </Tag>
        </Space>
      )
    },
    {
      title: t('moduleManagement.actions'),
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            size="small"
            icon={<UploadOutlined />}
            onClick={() => showUploadModal(record)}
          >
            {t('moduleManagement.uploadVideo')}
          </Button>
          <Button
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => handleViewModule(record.id)}
          >
            {t('moduleManagement.view')}
          </Button>
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteModule(record)}
            loading={deleteModuleMutation.isLoading}
          >
            {t('moduleManagement.delete')}
          </Button>
        </Space>
      )
    }
  ]



  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>{t('moduleManagement.title')}</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModuleVisible(true)}
        >
          {t('moduleManagement.createModule')}
        </Button>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={modulesData || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => t('moduleManagement.totalModules', { total })
          }}
          expandable={{
            expandedRowRender: (record) => <ModuleVideoDetails moduleId={record.id} />,
            rowExpandable: (record) => record.video_count > 0,
          }}
        />
      </Card>

      {/* Create Module Modal */}
      <Modal
        title={t('moduleManagement.createTrainingModule')}
        open={createModuleVisible}
        onCancel={() => setCreateModuleVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={createModuleForm}
          layout="vertical"
          onFinish={handleCreateModule}
        >
          <Form.Item
            name="title"
            label={t('moduleManagement.moduleTitle')}
            rules={[
              { required: true, message: t('moduleManagement.pleaseEnterModuleTitle') },
              { max: 255, message: t('moduleManagement.titleMaxLength') }
            ]}
          >
            <Input placeholder={t('moduleManagement.enterModuleTitle')} />
          </Form.Item>

          <Form.Item
            name="description"
            label={t('moduleManagement.description')}
            rules={[
              { max: 1000, message: t('moduleManagement.descriptionMaxLength') }
            ]}
          >
            <TextArea
              rows={4}
              placeholder={t('moduleManagement.enterModuleDescription')}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={createModuleMutation.isLoading}
              >
                {t('moduleManagement.createModule')}
              </Button>
              <Button onClick={() => setCreateModuleVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Upload Video Modal */}
      <Modal
        title={t('moduleManagement.uploadVideoTitle', { title: selectedModule?.title })}
        open={uploadVideoVisible}
        onCancel={() => setUploadVideoVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={uploadVideoForm}
          layout="vertical"
          onFinish={handleUploadVideo}
        >
          <Form.Item
            name="languageCode"
            label={t('moduleManagement.language')}
            rules={[{ required: true, message: t('moduleManagement.pleaseSelectLanguage') }]}
          >
            <Select placeholder={t('moduleManagement.pleaseSelectLanguage')}>
              {contentLanguageOptions.map(lang => (
                <Option key={lang.code} value={lang.code}>
                  {lang.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="languageName"
            label={t('moduleManagement.languageDisplayName')}
            rules={[{ required: true, message: t('moduleManagement.pleaseEnterLanguageDisplayName') }]}
          >
            <Input placeholder="e.g., English, 中文, Español" />
          </Form.Item>

          <Form.Item
            name="video"
            label={t('moduleManagement.videoFile')}
            rules={[{ required: true, message: t('moduleManagement.pleaseSelectVideoFile') }]}
          >
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>{t('moduleManagement.selectVideoFile')}</Button>
            </Upload>
          </Form.Item>

          <div style={{ color: '#8c8c8c', fontSize: 12, marginBottom: 16 }}>
            • {t('moduleManagement.supportedFormats')}
            <br />
            • Maximum file size: {uploadConfigLoading ? 'Loading...' : (uploadConfig?.maxFileSizeFormatted || '500 MB')}
            {uploadConfigError && <span style={{ color: '#ff4d4f' }}> (Failed to load limit - using default)</span>}
            <br />
            • {t('moduleManagement.oneVideoPerLanguage')}
            <br />
            • Large files are automatically split into 10MB chunks for reliable upload
            <br />
            • Upload progress shows real-time chunk completion status
          </div>

          {uploadProgress > 0 && (
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <Text strong>Upload Progress</Text>
                {uploadDetails && (
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    Chunk {uploadDetails.uploadedChunks} of {uploadDetails.totalChunks} • {' '}
                    {(uploadDetails.uploadedBytes / 1024 / 1024).toFixed(1)}MB of {' '}
                    {(uploadDetails.totalBytes / 1024 / 1024).toFixed(1)}MB
                  </div>
                )}
              </div>
              <Progress
                percent={uploadProgress}
                status={uploadVideoMutation.isLoading ? "active" : "success"}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              {uploadVideoMutation.isLoading && (
                <div style={{ marginTop: 8, textAlign: 'center' }}>
                  <Button
                    size="small"
                    danger
                    onClick={handleCancelUpload}
                  >
                    Cancel Upload
                  </Button>
                </div>
              )}
            </div>
          )}

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={uploadVideoMutation.isLoading}
              >
                {t('moduleManagement.uploadVideo')}
              </Button>
              <Button onClick={() => setUploadVideoVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ModuleManagement
