/**
 * Language Selection Modal Component
 * Extracted from VideoPlayer.jsx for better modularity
 */

import React from 'react'
import { Modal, Select, Alert, Space, Typography, Divider } from 'antd'
import { TranslationOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const { Title, Text } = Typography
const { Option } = Select

function LanguageSelectionModal({
  visible,
  availableLanguages = [],
  selectedLanguage,
  onLanguageChange,
  onConfirm,
  onCancel,
  hasExistingProgress = false
}) {
  const { t } = useTranslation()

  const handleLanguageChange = (value) => {
    onLanguageChange?.(value)
  }

  const handleConfirm = () => {
    if (selectedLanguage) {
      onConfirm?.(selectedLanguage)
    }
  }

  const handleCancel = () => {
    onCancel?.()
  }

  // Check if only one language is available
  const isOnlyOneLanguage = availableLanguages.length === 1

  return (
    <Modal
      title={
        <Space>
          <TranslationOutlined />
          {isOnlyOneLanguage
            ? t('videoPlayer.onlyOneLanguageAvailable')
            : t('videoPlayer.selectVideoLanguage')
          }
        </Space>
      }
      open={visible}
      onOk={handleConfirm}
      onCancel={handleCancel}
      okText={t('videoPlayer.confirmLanguage')}
      cancelText={t('common.cancel')}
      okButtonProps={{ 
        disabled: !selectedLanguage,
        type: 'primary'
      }}
      width={500}
      closable={false}
      maskClosable={false}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {isOnlyOneLanguage ? (
          <div>
            <Text>
              {t('videoPlayer.videoAvailableIn')} <strong>{availableLanguages[0]?.language_name}</strong>.
            </Text>
            <br />
            <Text type="secondary">
              {t('videoPlayer.clickConfirmToContinue')}
            </Text>
          </div>
        ) : (
          <div>
            <Text>
              {t('videoPlayer.selectLanguageDescription')}
            </Text>
          </div>
        )}

        {!isOnlyOneLanguage && (
          <div>
            <Title level={5}>{t('videoPlayer.availableLanguages')}</Title>
            <Select
              style={{ width: '100%' }}
              placeholder={t('videoPlayer.selectLanguagePlaceholder')}
              value={selectedLanguage}
              onChange={handleLanguageChange}
              size="large"
            >
              {availableLanguages.map((lang) => (
                <Option key={lang.language_code} value={lang.language_code}>
                  <Space>
                    <span>{lang.language_name}</span>
                    {lang.language_code === 'en' && <span>🇺🇸</span>}
                    {lang.language_code === 'zh' && <span>🇨🇳</span>}
                  </Space>
                </Option>
              ))}
            </Select>
          </div>
        )}

        {hasExistingProgress && !isOnlyOneLanguage && (
          <>
            <Divider />
            <Alert
              message={t('videoPlayer.warning')}
              description={
                <div>
                  <Text>{t('videoPlayer.languageSelectionWarning')}</Text>
                  <br />
                  <br />
                  <Text strong>{t('videoPlayer.thisIncludes')}</Text>
                  <ul style={{ marginTop: 8, marginBottom: 0 }}>
                    <li>{t('videoPlayer.videoWatchProgress')}</li>
                    <li>{t('videoPlayer.quizCompletionStatus')}</li>
                    <li>{t('videoPlayer.quizScores')}</li>
                    <li>{t('videoPlayer.certificates')}</li>
                  </ul>
                  <br />
                  <Text type="danger">{t('videoPlayer.actionCannotBeUndone')}</Text>
                </div>
              }
              type="warning"
              icon={<ExclamationCircleOutlined />}
              showIcon
            />
          </>
        )}

        <Divider />
        
        <Alert
          message={t('videoPlayer.progressTrackingInfo')}
          description={t('videoPlayer.progressTrackingDescription')}
          type="info"
          showIcon
        />
      </Space>
    </Modal>
  )
}

export default LanguageSelectionModal
