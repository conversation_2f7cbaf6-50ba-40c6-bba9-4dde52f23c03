/**
 * Comprehensive Email System Debug and Test Suite
 * 
 * This script consolidates all email-related tests including:
 * - Password generation testing
 * - Email template generation
 * - SMTP configuration testing
 * - Database connectivity
 * - Character encoding validation
 * - Multi-language support
 * 
 * Usage: node scripts/debug/comprehensive-email-tests.js [test-name]
 * Available tests: password, email, smtp, database, templates, all
 */

const bcrypt = require('bcryptjs');
const { query } = require('../../database/init');
const emailService = require('../../services/emailService');

// Generate random password with HTML-safe characters only
function generateRandomPassword(length = 12) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^*()_+-=[]|;:,.?';
  let password = '';
  
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^*()_+-=[]|;:,.?';
  
  password += lowercase.charAt(Math.floor(Math.random() * lowercase.length));
  password += uppercase.charAt(Math.floor(Math.random() * uppercase.length));
  password += numbers.charAt(Math.floor(Math.random() * numbers.length));
  password += symbols.charAt(Math.floor(Math.random() * symbols.length));
  
  for (let i = 4; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  
  password = password.split('').sort(() => Math.random() - 0.5).join('');
  
  const problematicChars = ['<', '>', '&', '"', "'", '{', '}', '`', '\\', '/'];
  if (problematicChars.some(char => password.includes(char))) {
    console.warn('Generated password contains problematic characters, regenerating...');
    return generateRandomPassword(length);
  }

  return password;
}

class EmailTestSuite {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': '📋',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️'
    }[type] || '📋';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async runTest(testName, testFunction) {
    this.results.total++;
    try {
      this.log(`Starting test: ${testName}`);
      const result = await testFunction();
      if (result.success) {
        this.results.passed++;
        this.log(`Test passed: ${testName}`, 'success');
      } else {
        this.results.failed++;
        this.log(`Test failed: ${testName} - ${result.error}`, 'error');
      }
      return result;
    } catch (error) {
      this.results.failed++;
      this.log(`Test error: ${testName} - ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  async testPasswordGeneration() {
    return this.runTest('Password Generation', async () => {
      const passwords = [];
      for (let i = 0; i < 50; i++) {
        passwords.push(generateRandomPassword());
      }

      const uniquePasswords = new Set(passwords);
      const allUnique = uniquePasswords.size === passwords.length;
      const allCorrectLength = passwords.every(p => p.length === 12);
      
      const problematicChars = ['<', '>', '&', '"', "'", '{', '}', '`', '\\', '/'];
      const hasProblematic = passwords.some(password => 
        problematicChars.some(char => password.includes(char))
      );

      if (!allUnique) return { success: false, error: 'Duplicate passwords found' };
      if (!allCorrectLength) return { success: false, error: 'Incorrect password length' };
      if (hasProblematic) return { success: false, error: 'Problematic characters found' };

      return { success: true };
    });
  }

  async testEmailGeneration() {
    return this.runTest('Email Generation', async () => {
      await emailService.initialize();
      
      const password = generateRandomPassword();
      const email = '<EMAIL>';
      
      // Test English
      const englishHTML = await emailService.generateLoginEmailHTML(email, password, 'John', 'Doe', 'en');
      const enContainsPassword = englishHTML.includes(password);
      const enContainsEmail = englishHTML.includes(email);
      
      // Test Chinese
      const chineseHTML = await emailService.generateLoginEmailHTML(email, password, '张', '三', 'zh');
      const zhContainsPassword = chineseHTML.includes(password);
      const zhContainsChinese = chineseHTML.includes('密码');

      if (!enContainsPassword || !enContainsEmail) {
        return { success: false, error: 'English email generation failed' };
      }
      if (!zhContainsPassword || !zhContainsChinese) {
        return { success: false, error: 'Chinese email generation failed' };
      }

      return { success: true };
    });
  }

  async testDatabaseConnectivity() {
    return this.runTest('Database Connectivity', async () => {
      const result = await query('SELECT COUNT(*) as count FROM users');
      if (result.rows.length === 0) {
        return { success: false, error: 'No database response' };
      }
      return { success: true };
    });
  }

  async testEmailTemplates() {
    return this.runTest('Email Templates', async () => {
      const result = await query('SELECT email_template_body_en, email_template_body_zh FROM email_config WHERE id = 1');
      if (result.rows.length === 0) {
        return { success: false, error: 'No email config found' };
      }

      const templates = result.rows[0];
      const enHasPassword = templates.email_template_body_en?.includes('{{password}}');
      const zhHasPassword = templates.email_template_body_zh?.includes('{{password}}');

      if (!enHasPassword || !zhHasPassword) {
        return { success: false, error: 'Templates missing password placeholder' };
      }

      return { success: true };
    });
  }

  async testSMTPConfiguration() {
    return this.runTest('SMTP Configuration', async () => {
      try {
        await emailService.initialize();
        // If initialization succeeds without throwing, SMTP config is valid
        return { success: true };
      } catch (error) {
        if (error.message.includes('Authentication unsuccessful')) {
          return { success: false, error: 'SMTP authentication failed - password corruption suspected' };
        }
        return { success: false, error: `SMTP error: ${error.message}` };
      }
    });
  }

  async testStressGeneration() {
    return this.runTest('Stress Test (100 emails)', async () => {
      await emailService.initialize();
      
      const startTime = Date.now();
      let successCount = 0;
      
      for (let i = 0; i < 100; i++) {
        const password = generateRandomPassword();
        const email = `test${i}@example.com`;
        
        try {
          const emailHTML = await emailService.generateLoginEmailHTML(email, password, 'User', `${i}`, 'en');
          if (emailHTML.includes(password) && emailHTML.includes(email)) {
            successCount++;
          }
        } catch (error) {
          // Count as failure
        }
      }
      
      const duration = Date.now() - startTime;
      this.log(`Generated 100 emails in ${duration}ms, success rate: ${successCount}/100`);
      
      if (successCount < 100) {
        return { success: false, error: `Only ${successCount}/100 emails generated successfully` };
      }
      
      return { success: true };
    });
  }

  async runAllTests() {
    this.log('Starting comprehensive email system tests', 'info');
    
    await this.testPasswordGeneration();
    await this.testDatabaseConnectivity();
    await this.testEmailTemplates();
    await this.testEmailGeneration();
    await this.testSMTPConfiguration();
    await this.testStressGeneration();
    
    this.log(`\n=== TEST SUMMARY ===`);
    this.log(`Total tests: ${this.results.total}`);
    this.log(`Passed: ${this.results.passed}`, this.results.passed === this.results.total ? 'success' : 'info');
    this.log(`Failed: ${this.results.failed}`, this.results.failed === 0 ? 'info' : 'error');
    this.log(`Success rate: ${(this.results.passed / this.results.total * 100).toFixed(1)}%`);
    
    if (this.results.failed === 0) {
      this.log('🎉 ALL TESTS PASSED!', 'success');
    } else {
      this.log('⚠️ Some tests failed. Check logs above for details.', 'warning');
    }
  }

  async runSpecificTest(testName) {
    const tests = {
      'password': () => this.testPasswordGeneration(),
      'email': () => this.testEmailGeneration(),
      'smtp': () => this.testSMTPConfiguration(),
      'database': () => this.testDatabaseConnectivity(),
      'templates': () => this.testEmailTemplates(),
      'stress': () => this.testStressGeneration(),
      'all': () => this.runAllTests()
    };

    const test = tests[testName.toLowerCase()];
    if (!test) {
      this.log(`Unknown test: ${testName}. Available tests: ${Object.keys(tests).join(', ')}`, 'error');
      return;
    }

    if (testName.toLowerCase() !== 'all') {
      await test();
      this.log(`\n=== SINGLE TEST SUMMARY ===`);
      this.log(`Test: ${testName}`);
      this.log(`Result: ${this.results.passed > 0 ? 'PASSED' : 'FAILED'}`, this.results.passed > 0 ? 'success' : 'error');
    } else {
      await test();
    }
  }
}

// Main execution
async function main() {
  const testSuite = new EmailTestSuite();
  const testName = process.argv[2] || 'all';
  
  try {
    await testSuite.runSpecificTest(testName);
  } catch (error) {
    console.error('❌ Fatal error:', error);
  }
  
  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = { EmailTestSuite, generateRandomPassword };
