/**
 * Quiz Timer Component
 * Extracted from Quiz.jsx for better modularity
 */

import React, { useState, useEffect, useCallback } from 'react'
import { Card, Progress, Typography, Modal, Button, Space } from 'antd'
import { ClockCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const { Text, Title } = Typography

function QuizTimer({
  timeLimit, // in minutes
  onTimeUp,
  isActive = true,
  showWarningAt = 5 // minutes remaining to show warning
}) {
  const { t } = useTranslation()
  const [timeRemaining, setTimeRemaining] = useState(timeLimit * 60) // convert to seconds
  const [showTimeUpModal, setShowTimeUpModal] = useState(false)

  // Calculate time display values
  const minutes = Math.floor(timeRemaining / 60)
  const seconds = timeRemaining % 60
  const totalSeconds = timeLimit * 60
  const progressPercent = ((totalSeconds - timeRemaining) / totalSeconds) * 100

  // Determine progress color based on time remaining
  const getProgressColor = useCallback(() => {
    const minutesLeft = timeRemaining / 60
    if (minutesLeft <= 2) return '#ff4d4f' // Red
    if (minutesLeft <= showWarningAt) return '#faad14' // Orange
    return '#52c41a' // Green
  }, [timeRemaining, showWarningAt])

  // Format time display
  const formatTime = useCallback((totalSeconds) => {
    const mins = Math.floor(totalSeconds / 60)
    const secs = totalSeconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }, [])

  // Timer effect
  useEffect(() => {
    if (!isActive || timeRemaining <= 0) return

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          setShowTimeUpModal(true)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isActive, timeRemaining])

  // Handle time up
  const handleTimeUp = () => {
    setShowTimeUpModal(false)
    onTimeUp?.()
  }

  // Don't render if no time limit
  if (!timeLimit) return null

  return (
    <>
      {/* Timer Display */}
      <Card 
        size="small" 
        style={{ 
          position: 'sticky',
          top: 0,
          zIndex: 100,
          marginBottom: 16,
          border: timeRemaining <= 120 ? '2px solid #ff4d4f' : undefined // Red border when < 2 minutes
        }}
      >
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between' 
        }}>
          <Space>
            <ClockCircleOutlined style={{ color: getProgressColor() }} />
            <Text strong>{t('quiz.timeLeft')}</Text>
          </Space>
          
          <div style={{ flex: 1, margin: '0 16px' }}>
            <Progress
              percent={progressPercent}
              strokeColor={getProgressColor()}
              showInfo={false}
              size="small"
            />
          </div>
          
          <Text 
            strong 
            style={{ 
              fontSize: 18,
              color: getProgressColor(),
              minWidth: 60,
              textAlign: 'right'
            }}
          >
            {formatTime(timeRemaining)}
          </Text>
        </div>

        {/* Warning message when time is running low */}
        {timeRemaining <= showWarningAt * 60 && timeRemaining > 0 && (
          <div style={{ 
            marginTop: 8, 
            padding: 8, 
            backgroundColor: '#fff2e8',
            borderRadius: 4,
            textAlign: 'center'
          }}>
            <Text type="warning" style={{ fontSize: 12 }}>
              ⚠️ {Math.ceil(timeRemaining / 60)} {t('quiz.minutes')} remaining
            </Text>
          </div>
        )}
      </Card>

      {/* Time Up Modal */}
      <Modal
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            {t('quiz.timeUp')}
          </Space>
        }
        open={showTimeUpModal}
        onOk={handleTimeUp}
        onCancel={handleTimeUp}
        okText={t('quiz.submitQuiz')}
        cancelText={null}
        closable={false}
        maskClosable={false}
        width={500}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={4} style={{ color: '#ff4d4f', marginBottom: 8 }}>
              {t('quiz.timeUpDescription')}
            </Title>
            <Text>
              {t('quiz.timeUpAutoSubmit')}
            </Text>
          </div>
        </Space>
      </Modal>
    </>
  )
}

export default QuizTimer
