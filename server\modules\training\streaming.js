const path = require('path');
const fs = require('fs').promises;

/**
 * Handle OPTIONS requests for video streaming
 */
function handleVideoOptions(req, res) {
  console.log('OPTIONS request for API video route:', req.params.filename);
  console.log('Request origin:', req.headers.origin);

  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Range, Content-Range, Content-Length, Content-Type, Cache-Control, If-Range, Origin, X-Requested-With');
  res.header('Access-Control-Expose-Headers', 'Content-Range, Content-Length, Accept-Ranges');
  res.header('Access-Control-Max-Age', '86400');

  res.status(200).end();
}

/**
 * Get content type based on file extension
 */
function getContentType(filename) {
  const ext = path.extname(filename).toLowerCase();
  let contentType = 'video/mp4'; // default
  
  if (ext === '.webm') contentType = 'video/webm';
  else if (ext === '.avi') contentType = 'video/x-msvideo';
  else if (ext === '.mov') contentType = 'video/quicktime';
  else if (ext === '.wmv') contentType = 'video/x-ms-wmv';
  else if (ext === '.flv') contentType = 'video/x-flv';
  else if (ext === '.mkv') contentType = 'video/x-matroska';

  return contentType;
}

/**
 * Set CORS headers for video streaming
 */
function setCorsHeaders(res) {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Range, Content-Range, Content-Length, Content-Type, Cache-Control, If-Range, Origin, X-Requested-With');
  res.header('Access-Control-Expose-Headers', 'Content-Range, Content-Length, Accept-Ranges');
  res.header('Access-Control-Allow-Credentials', 'false');
}

/**
 * Stream video file with proper range support
 */
async function streamVideo(req, res) {
  try {
    const filename = req.params.filename;
    const videoPath = path.join(__dirname, '../../uploads/videos', filename);

    console.log('API Video stream request for:', filename);
    console.log('Video path:', videoPath);
    console.log('Request origin:', req.headers.origin);

    // Set CORS headers first - very permissive
    setCorsHeaders(res);

    // Check if file exists
    try {
      await fs.access(videoPath);
    } catch (error) {
      console.error('Video file not found:', videoPath);
      return res.status(404).json({ error: 'Video file not found' });
    }

    // Get file stats
    const stats = await fs.stat(videoPath);
    const fileSize = stats.size;

    // Handle range requests for video streaming
    const range = req.headers.range;

    if (range) {
      console.log('Range request:', range);

      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;

      const contentType = getContentType(filename);

      res.status(206);
      res.header('Content-Range', `bytes ${start}-${end}/${fileSize}`);
      res.header('Accept-Ranges', 'bytes');
      res.header('Content-Length', chunksize);
      res.header('Content-Type', contentType);

      const stream = require('fs').createReadStream(videoPath, { start, end });
      stream.pipe(res);
    } else {
      console.log('Full file request');

      const contentType = getContentType(filename);

      res.header('Content-Length', fileSize);
      res.header('Content-Type', contentType);
      res.header('Accept-Ranges', 'bytes');

      const stream = require('fs').createReadStream(videoPath);
      stream.pipe(res);
    }

  } catch (error) {
    console.error('Video streaming error:', error);
    res.status(500).json({ error: 'Failed to stream video' });
  }
}

module.exports = {
  handleVideoOptions,
  streamVideo,
  getContentType,
  setCorsHeaders
};
