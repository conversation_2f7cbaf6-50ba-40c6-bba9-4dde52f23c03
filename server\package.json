{"name": "training-system-server", "version": "1.0.0", "description": "Backend server for Internal Training System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:routes": "node tests/runTests.js", "test:modules": "node tests/simpleTest.js", "test:e2e": "node tests/comprehensive-e2e-test.js", "data:wipe": "node scripts/data-wipe.js"}, "dependencies": {"@azure/storage-blob": "^12.17.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "ffprobe-static": "^3.1.0", "get-video-duration": "^4.1.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-ffprobe": "^3.0.0", "nodemailer": "^6.9.7", "pg": "^8.11.3", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["training", "api", "express"], "author": "Internal Training Team", "license": "MIT"}