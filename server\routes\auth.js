const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Joi = require('joi');
const { query } = require('../database/init');
const { requireAuth, requireAdmin, optionalAuth } = require('../middleware/auth');
const systemConfigService = require('../services/systemConfigService');

const router = express.Router();

// Validation schemas
const loginSchema = Joi.object({
  email: Joi.string().required(), // Accept any string as username/email
  password: Joi.string().min(1).required(),
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(6).required(),
  confirmPassword: Joi.string().optional(), // Allow confirmPassword but don't validate it (client-side validation handles this)
});

// Login endpoint
router.post('/login', async (req, res) => {
  try {
    console.log('=== LOGIN ENDPOINT HIT ===');
    console.log('Request body type:', typeof req.body);
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('Request body keys:', Object.keys(req.body || {}));
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Request method:', req.method);
    console.log('Request URL:', req.url);

    // Validate input
    console.log('About to validate with schema...');
    const { error, value } = loginSchema.validate(req.body);
    console.log('Validation result - error:', error);
    console.log('Validation result - value:', value);

    if (error) {
      console.log('VALIDATION FAILED:', error.details[0].message);
      console.log('Full error details:', JSON.stringify(error.details, null, 2));
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    console.log('Validation passed, proceeding with login...');

    const { email, password } = value;
    console.log('Extracted email:', email);
    console.log('Extracted password:', password);

    // Find user in database
    const userResult = await query(
      'SELECT email, password_hash, first_name, last_name, full_name, is_admin, is_active, password_created_at, language FROM users WHERE email = $1',
      [email.toLowerCase()]
    );

    console.log('Database query result:', userResult.rows.length, 'rows found');
    if (userResult.rows.length === 0) {
      console.log('No user found with email:', email.toLowerCase());
      return res.status(401).json({ error: 'Invalid email or password' });
    }

    const user = userResult.rows[0];
    console.log('Found user:', {
      email: user.email,
      first_name: user.first_name,
      is_admin: user.is_admin,
      is_active: user.is_active,
      password_hash_length: user.password_hash ? user.password_hash.length : 'null'
    });

    // Check if user is active
    if (!user.is_active) {
      console.log('User account is deactivated');
      return res.status(401).json({ error: 'Account is deactivated' });
    }

    // Check password expiration for non-admin users using configurable expiration days
    if (!user.is_admin && user.password_created_at) {
      const isExpired = await systemConfigService.isPasswordExpired(user.password_created_at, user.is_admin);

      if (isExpired) {
        console.log('User password has expired');
        return res.status(401).json({
          error: 'Password expired',
          message: 'Your password has expired. Please contact an administrator to reset it.',
          passwordExpired: true
        });
      }
    }

    // Verify password
    console.log('🔐 About to verify password...');
    console.log('   Input password:', password);
    console.log('   Input password length:', password.length);
    console.log('   Input password chars:', password.split('').map(c => `${c}(${c.charCodeAt(0)})`).join(' '));
    console.log('   Stored hash (first 20 chars):', user.password_hash ? user.password_hash.substring(0, 20) : 'null');
    console.log('   Stored hash length:', user.password_hash ? user.password_hash.length : 'null');

    // Verify password using bcrypt
    console.log('⚡ Performing bcrypt comparison...');
    const startTime = Date.now();
    let isValidPassword = false;
    try {
      isValidPassword = await bcrypt.compare(password, user.password_hash);
      const endTime = Date.now();
      console.log('✅ Bcrypt comparison completed successfully');
      console.log('   Comparison took:', endTime - startTime, 'ms');
    } catch (bcryptError) {
      console.error('❌ Bcrypt comparison error:', bcryptError);
      isValidPassword = false;
    }
    console.log('🔍 Password verification result:', isValidPassword);

    if (!isValidPassword) {
      console.log('PASSWORD VERIFICATION FAILED');
      return res.status(401).json({ error: 'Invalid email or password' });
    }

    console.log('Password verification successful!');

    // Update last login timestamp
    await query(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE email = $1',
      [email.toLowerCase()]
    );

    // Create JWT token
    const tokenPayload = {
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      fullName: user.full_name,
      isAdmin: user.is_admin,
      language: user.language || 'en',
    };

    const jwtSecret = process.env.JWT_SECRET || 'fallback_jwt_secret_change_in_production';
    const token = jwt.sign(tokenPayload, jwtSecret, {
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    });

    console.log('JWT token generated successfully');

    // Return user info and token
    res.json({
      message: 'Login successful',
      user: {
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        fullName: user.full_name,
        isAdmin: user.is_admin,
        language: user.language || 'en',
      },
      token: token,
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Logout endpoint
router.post('/logout', (req, res) => {
  // With JWT, logout is handled client-side by removing the token
  // We just return success here
  res.json({ message: 'Logout successful' });
});

// Get current user info
router.get('/me', requireAuth, (req, res) => {
  res.json({ user: req.user });
});

// Change password (admin only)
router.post('/change-password', requireAuth, async (req, res) => {
  try {
    console.log('=== CHANGE PASSWORD ENDPOINT HIT ===');
    console.log('User:', req.user.email);
    console.log('Request body:', req.body);

    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Only administrators can change passwords'
      });
    }

    // Validate input
    const { error, value } = changePasswordSchema.validate(req.body);
    if (error) {
      console.log('Validation error:', error.details[0].message);
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    const { currentPassword, newPassword } = value;
    const userEmail = req.user.email;
    console.log('Current password:', currentPassword);
    console.log('New password:', newPassword);

    // Get current password hash
    const userResult = await query(
      'SELECT password_hash FROM users WHERE email = $1',
      [userEmail]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = userResult.rows[0];
    console.log('Stored hash (first 20 chars):', user.password_hash ? user.password_hash.substring(0, 20) : 'null');

    // Verify current password using bcrypt
    console.log('⚡ Performing bcrypt comparison for current password...');
    const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);

    console.log('Current password verification result:', isValidPassword);
    if (!isValidPassword) {
      console.log('Current password verification failed');
      return res.status(401).json({ error: 'Current password is incorrect' });
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    console.log('Hashing new password with salt rounds:', saltRounds);
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
    console.log('New password hash (first 20 chars):', newPasswordHash.substring(0, 20));

    // Update password in database with new timestamp
    console.log('Updating password in database for user:', userEmail);
    const updateResult = await query(
      'UPDATE users SET password_hash = $1, password_created_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE email = $2',
      [newPasswordHash, userEmail]
    );
    console.log('Database update result:', updateResult);

    console.log('Password change completed successfully');
    res.json({ message: 'Password changed successfully' });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Verify token endpoint
router.get('/verify', requireAuth, (req, res) => {
  res.json({
    valid: true,
    user: req.user
  });
});

// Check authentication status
router.get('/status', optionalAuth, async (req, res) => {
  if (req.user) {
    try {
      // Fetch fresh user data from database to ensure we have the latest language preference
      const userResult = await query(
        'SELECT email, first_name, last_name, full_name, is_admin, language FROM users WHERE email = $1 AND is_active = true',
        [req.user.email]
      );

      if (userResult.rows.length === 0) {
        return res.json({ authenticated: false });
      }

      const user = userResult.rows[0];
      res.json({
        authenticated: true,
        user: {
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          fullName: user.full_name,
          isAdmin: user.is_admin,
          language: user.language || 'en',
        }
      });
    } catch (error) {
      console.error('Status check error:', error);
      res.json({ authenticated: false });
    }
  } else {
    res.json({ authenticated: false });
  }
});

module.exports = router;
