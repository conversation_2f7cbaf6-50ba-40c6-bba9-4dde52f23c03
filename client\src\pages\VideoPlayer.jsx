import React, { useState, useRef, useEffect, useMemo } from 'react'
import { Card, Button, Typography, Space, Alert, message, Form, Modal, Select } from 'antd'
import { ArrowLeftOutlined, QuestionCircleOutlined, SaveOutlined, LogoutOutlined, UnlockOutlined, ExclamationCircleOutlined, TranslationOutlined } from '@ant-design/icons'
import ReactPlayer from 'react-player'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import { trainingAPI, authAPI, handleAPIError } from '../services/api'
import { useAuth } from '../contexts/AuthContext'
import LoadingImage from '../components/LoadingImage'

// Import modular components
import {
  VideoControls,
  VideoProgressBar,
  LanguageSelectionModal,
  AdminControlsModal
} from '../components/VideoPlayer'

// Import utilities
import {
  getVideoUrl,
  getFallbackVideoUrl,
  formatTime
} from '../utils/videoHelpers'

const { Title, Text } = Typography



function VideoPlayer() {
  const { moduleId, languageCode } = useParams()
  const navigate = useNavigate()
  const playerRef = useRef(null)
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const { t } = useTranslation()

  const [playing, setPlaying] = useState(false)
  const [duration, setDuration] = useState(0)
  const [watchTime, setWatchTime] = useState(0)
  const [maxWatched, setMaxWatched] = useState(0)
  const [videoCompleted, setVideoCompleted] = useState(false)
  const [playerReady, setPlayerReady] = useState(false)
  const [videoError, setVideoError] = useState(null)
  const [adminControlsActive, setAdminControlsActive] = useState(false)
  const [showAdminLogin, setShowAdminLogin] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [volume, setVolume] = useState(1) // Volume from 0 to 1
  const [muted, setMuted] = useState(false)
  const [showLanguageModal, setShowLanguageModal] = useState(true)
  const [languageConfirmed, setLanguageConfirmed] = useState(false)
  const [selectedLanguage, setSelectedLanguage] = useState(languageCode)
  const [adminLoginForm] = Form.useForm()

  // Fetch module details
  const { data: moduleData, isLoading, error } = useQuery(
    ['training-module', moduleId],
    () => trainingAPI.getModule(moduleId),
    {
      select: (response) => response.data,
      enabled: !!moduleId,
    }
  )

  // Fetch user progress for this module
  const { data: progressData, isLoading: progressLoading } = useQuery(
    ['training-progress', moduleId, user?.email],
    () => trainingAPI.getProgress(moduleId),
    {
      select: (response) => response.data.progress,
      enabled: !!moduleId && !!user?.email,
    }
  )

  // Update progress mutation
  const updateProgressMutation = useMutation(
    (progressData) => trainingAPI.updateProgress(moduleId, progressData),
    {
      onSuccess: (_, variables) => {
        // Invalidate user-specific queries to ensure fresh data
        queryClient.invalidateQueries(['training-progress', moduleId, user?.email])
        queryClient.invalidateQueries(['user-profile', user?.email])

        // Only show success message for manual saves, not auto-saves
        if (variables?.showMessage) {
          message.success(t('videoPlayer.progressSaved'))
        }
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(t('videoPlayer.failedToSaveProgress', { error: errorInfo.message }))
      }
    }
  )

  // Delete other languages progress mutation
  const deleteOtherLanguagesProgressMutation = useMutation(
    (keepLanguageCode) => trainingAPI.deleteOtherLanguagesProgress(moduleId, keepLanguageCode),
    {
      onSuccess: (response) => {
        // Invalidate progress queries to refresh data
        queryClient.invalidateQueries(['training-progress', moduleId, user?.email])
        queryClient.invalidateQueries(['user-profile', user?.email])

        const deletedLanguages = response.data.deletedLanguages
        if (deletedLanguages.length > 0) {
          message.success(t('videoPlayer.progressDeletedForLanguages', { languages: deletedLanguages.join(', ') }))
        } else {
          message.info(t('videoPlayer.noOtherLanguageProgress'))
        }
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(t('videoPlayer.failedToDeleteProgress', { error: errorInfo.message }))
      }
    }
  )

  // Load saved progress when data is available - optimized with useMemo for progress lookup
  const currentProgress = useMemo(() => {
    return progressData?.find(p => p.language_code === languageCode)
  }, [progressData, languageCode])

  useEffect(() => {
    if (currentProgress && playerRef.current) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Loading saved progress:', currentProgress)
      }

      // Set the saved watch time and max watched position
      const savedWatchTime = currentProgress.video_watch_time_seconds || 0
      setWatchTime(savedWatchTime)
      setMaxWatched(savedWatchTime)
      setVideoCompleted(currentProgress.video_watched || false)

      // Seek to saved position when player is ready
      if (savedWatchTime > 0 && playerReady) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Seeking to saved position:', savedWatchTime)
        }
        playerRef.current.seekTo(savedWatchTime)
      }
    }
  }, [currentProgress, playerReady])



  // Save progress on unmount - use ref to capture current values without causing re-renders
  const watchTimeRef = useRef(watchTime)
  const videoCompletedRef = useRef(videoCompleted)
  const languageCodeRef = useRef(languageCode)

  // Update refs when values change
  useEffect(() => {
    watchTimeRef.current = watchTime
  }, [watchTime])

  useEffect(() => {
    videoCompletedRef.current = videoCompleted
  }, [videoCompleted])

  useEffect(() => {
    languageCodeRef.current = languageCode
  }, [languageCode])

  // Save progress on unmount - no dependencies to prevent recreation
  useEffect(() => {
    return () => {
      if (watchTimeRef.current > 0 && languageCodeRef.current) {
        // Use a simple fetch instead of mutation to avoid React Query issues on unmount
        const saveProgress = async () => {
          try {
            const token = localStorage.getItem('token')
            if (token) {
              await fetch(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:3002/api'}/training/modules/${moduleId}/progress`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                  watchTime: Math.round(watchTimeRef.current),
                  completed: videoCompletedRef.current,
                  languageCode: languageCodeRef.current
                })
              })
            }
          } catch (error) {
            console.error('Failed to save progress on unmount:', error)
          }
        }
        saveProgress()
      }
    }
  }, [moduleId]) // Only moduleId as dependency

  // Prevent keyboard shortcuts that could control video - memoized handler
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Prevent common video control shortcuts
      const controlKeys = ['Space', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'KeyK', 'KeyJ', 'KeyL', 'KeyM', 'KeyF', 'Escape']

      if (controlKeys.includes(e.code)) {
        // Only prevent if the target is the video or video container
        if (e.target.tagName === 'VIDEO' || e.target.closest('.video-player-wrapper')) {
          e.preventDefault()
          e.stopPropagation()
          message.warning(t('videoPlayer.useProvidedControls'))
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown, { passive: false })
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [t])

  // Auto-pause when user leaves tab or switches windows - optimized event handlers
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (playing && playerRef.current && document.hidden) {
        setPlaying(false)
        message.info(t('videoPlayer.videoPausedTabSwitch'))
      }
    }

    const handleBlur = () => {
      if (playing && playerRef.current) {
        setPlaying(false)
        message.info(t('videoPlayer.videoPausedFocusLost'))
      }
    }

    if (playing) {
      document.addEventListener('visibilitychange', handleVisibilityChange)
      window.addEventListener('blur', handleBlur)
    }

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('blur', handleBlur)
    }
  }, [playing, t])

  const handleProgress = (state) => {
    if (!state || typeof state.playedSeconds !== 'number') {
      return
    }

    // CRITICAL: If video is already completed, don't process any more progress events
    if (videoCompleted) {
      return
    }

    const currentTime = state.playedSeconds
    setWatchTime(currentTime)

    // Check if video is completed (reached 99.9% or more) - stop playback immediately
    const progressPercent = duration > 0 ? Math.round((currentTime / duration) * 100) : 0

    if (progressPercent >= 100) {
      setVideoCompleted(true)
      setMaxWatched(duration)
      setPlaying(false) // Stop playback immediately

      // Force pause the player to prevent further progress events
      if (playerRef.current && playerRef.current.getInternalPlayer) {
        try {
          const internalPlayer = playerRef.current.getInternalPlayer()
          if (internalPlayer && internalPlayer.pause) {
            internalPlayer.pause()
          }
        } catch (e) {
          console.log('Could not pause internal player:', e)
        }
      }

      // Save completion progress immediately

      updateProgressMutation.mutate({
        watchTime: Math.round(duration),
        completed: true,
        languageCode
      })

      message.success('Video completed! You can now take the quiz.')
      return // Exit early to prevent further processing
    }

    // Track maximum watched position (prevent skipping forward)
    if (currentTime > maxWatched) {
      setMaxWatched(currentTime)
    } else if (currentTime > maxWatched + 2) {
      // Check if user has completed the video before (allow free navigation after completion)
      const currentProgress = progressData?.find(p => p.language_code === languageCode)
      const hasCompletedBefore = currentProgress?.video_watched === true

      if (!hasCompletedBefore && !adminControlsActive) {
        // First viewing: prevent skipping ahead (unless admin controls are active)
        if (playerRef.current && typeof playerRef.current.seekTo === 'function') {
          playerRef.current.seekTo(maxWatched)
        }
        message.warning(t('videoPlayer.cannotSkipAhead'))
      }
      // If user has completed before or admin controls are active, allow free navigation
    }
  }



  const handleDuration = (duration) => {
    setDuration(duration)
  }

  const handleEnded = () => {
    // Only trigger completion if not already completed in this session
    if (!videoCompleted) {
      setVideoCompleted(true)
      setMaxWatched(duration)

      // Save final progress immediately

      // Save final progress
      updateProgressMutation.mutate({
        watchTime: Math.round(duration),
        completed: true,
        languageCode
      })

      message.success(t('videoPlayer.videoCompleted'))
    }
  }

  const handlePlay = () => {
    setPlaying(true)
  }

  const handlePause = () => {
    setPlaying(false)
  }

  const handleQuizClick = () => {
    navigate(`/training/${moduleId}/quiz/${languageCode}`)
  }

  // Removed unused handleSaveProgress function

  const handleForceSaveProgress = () => {
    // Force save progress even if video is completed, and overwrite existing saved position
    updateProgressMutation.mutate({
      watchTime: Math.round(watchTime),
      completed: videoCompleted,
      languageCode,
      showMessage: true,
      forceOverwrite: true  // This will overwrite the saved position with current position
    })
  }

  // Custom video control functions
  const handlePlayPause = () => {
    // Check if video is completed and not playing - restart from beginning
    if (videoCompleted && !playing) {
      // Video is completed and user wants to restart playback
      if (playerRef.current && typeof playerRef.current.seekTo === 'function') {
        // Restart from the beginning (0 seconds)
        playerRef.current.seekTo(0)
        setWatchTime(0)
        setVideoCompleted(false) // Reset completion status to allow continued playback
        setPlaying(true)
        console.log('Restarting video from beginning')
      }
    } else {
      // Normal play/pause behavior
      setPlaying(!playing)
    }
  }

  const handleBackward = () => {
    if (playerRef.current && typeof playerRef.current.seekTo === 'function') {
      const newTime = Math.max(0, watchTime - 5)
      playerRef.current.seekTo(newTime)
      setWatchTime(newTime)
      // Update maxWatched if we're going back to a position we've already watched
      if (newTime <= maxWatched) {
        // This is allowed since we're going backward
      }
    }
  }

  const handleForward = () => {
    if (playerRef.current && typeof playerRef.current.seekTo === 'function') {
      // Check if user has completed the video before
      const currentProgress = progressData?.find(p => p.language_code === languageCode)
      const hasCompletedBefore = currentProgress?.video_watched === true

      if (hasCompletedBefore || adminControlsActive) {
        // Allow fast forward for completed videos or when admin controls are active
        const newTime = Math.min(duration, watchTime + 10)
        playerRef.current.seekTo(newTime)
        setWatchTime(newTime)
      } else {
        // For first viewing, only allow forward to maxWatched position
        const newTime = Math.min(maxWatched, watchTime + 10)
        playerRef.current.seekTo(newTime)
        setWatchTime(newTime)
        if (watchTime + 10 > maxWatched) {
          message.warning(t('videoPlayer.cannotSkipAheadFirstViewing'))
        }
      }
    }
  }

  const handleSeekTo = (seconds) => {
    if (playerRef.current && typeof playerRef.current.seekTo === 'function') {
      // Check if user has completed the video before
      const currentProgress = progressData?.find(p => p.language_code === languageCode)
      const hasCompletedBefore = currentProgress?.video_watched === true

      if (hasCompletedBefore || adminControlsActive) {
        // Allow free navigation for completed videos or when admin controls are active
        playerRef.current.seekTo(seconds)
        setWatchTime(seconds)
      } else {
        // Only allow seeking to positions we've already watched for first viewing
        const seekTime = Math.min(seconds, maxWatched)
        playerRef.current.seekTo(seekTime)
        setWatchTime(seekTime)
      }
    }
  }

  const handleLanguageSwitch = (newLanguageCode) => {
    if (newLanguageCode === languageCode) {
      return // No change needed
    }

    // Check if user has any progress in other languages
    const hasOtherLanguageProgress = progressData?.some(p => p.language_code !== newLanguageCode)

    if (hasOtherLanguageProgress) {
      Modal.confirm({
        title: t('videoPlayer.switchLanguage'),
        icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
        content: (
          <div>
            <p><strong>{t('videoPlayer.warning')}:</strong> {t('videoPlayer.switchLanguageWarning')}</p>
            <p>{t('videoPlayer.thisIncludes')}:</p>
            <ul style={{ marginLeft: 20, marginTop: 8 }}>
              <li>{t('videoPlayer.videoWatchProgress')}</li>
              <li>{t('videoPlayer.quizCompletionStatus')}</li>
              <li>{t('videoPlayer.quizScores')}</li>
              <li>{t('videoPlayer.certificates')}</li>
            </ul>
            <p style={{ marginTop: 12, color: '#fa541c' }}>
              <strong>{t('videoPlayer.actionCannotBeUndone')}</strong>
            </p>
          </div>
        ),
        okText: t('videoPlayer.switchLanguageButton'),
        cancelText: t('common.cancel'),
        okType: 'danger',
        width: 500,
        onOk: () => {
          // Delete progress for other languages and then navigate
          deleteOtherLanguagesProgressMutation.mutate(newLanguageCode, {
            onSuccess: () => {
              navigate(`/training/${moduleId}/video/${newLanguageCode}`)
              // Hard refresh the page after language change and progress deletion
              setTimeout(() => {
                window.location.reload()
              }, 100) // Small delay to ensure navigation is processed
            }
          })
        }
      })
    } else {
      // No other language progress, safe to switch
      navigate(`/training/${moduleId}/video/${newLanguageCode}`)
    }
  }

  const handleExitVideo = () => {
    Modal.confirm({
      title: t('videoPlayer.exitVideo'),
      content: t('videoPlayer.saveProgressBeforeLeaving'),
      okText: t('videoPlayer.saveAndExit'),
      cancelText: t('videoPlayer.exitWithoutSaving'),
      onOk: () => {
        if (watchTime > 0 && languageCode) {
          updateProgressMutation.mutate({
            watchTime: Math.round(watchTime),
            completed: videoCompleted,
            languageCode,
            showMessage: true,
            forceOverwrite: true  // Manual save on exit should overwrite
          })
        }
        navigate('/training-modules')
      },
      onCancel: () => {
        navigate('/training-modules')
      }
    })
  }

  const handleLanguageSelection = () => {
    if (selectedLanguage !== languageCode) {
      // Navigate to the selected language
      navigate(`/training/${moduleId}/video/${selectedLanguage}`)
    } else {
      // Same language, just confirm and continue
      setLanguageConfirmed(true)
      setShowLanguageModal(false)
    }
  }

  const handleLanguageModalCancel = () => {
    // Go back to training modules if user cancels language selection
    navigate('/training-modules')
  }





  const handlePlayerError = (error) => {
    console.error('=== VIDEO PLAYER ERROR ===');
    console.error('Error object:', error);
    console.error('Error type:', typeof error);
    console.error('Error message:', error?.message);
    console.error('Error stack:', error?.stack);
    console.error('Current video URL:', videoUrl);
    console.error('========================');

    // Try fallback URL
    const fallbackUrl = getFallbackVideoUrl(video);
    if (fallbackUrl && fallbackUrl !== videoUrl) {
      // console.log('Trying fallback URL:', fallbackUrl);
      // Force re-render with fallback URL
      setVideoError(`Primary video source failed. Trying alternative source...`);

      // You could implement URL switching logic here
      setTimeout(() => {
        window.location.reload(); // Simple fallback - reload page
      }, 2000);
    } else {
      setVideoError(t('videoPlayer.videoLoadFailed'));
      message.error(t('videoPlayer.videoFailedRefresh'));
    }
  }

  // Admin controls functionality
  const handleAdminLogin = async (values) => {
    try {
      const response = await authAPI.login({
        email: values.email,
        password: values.password
      })

      if (response.data.user.isAdmin) {
        setAdminControlsActive(true)
        setShowAdminLogin(false)
        adminLoginForm.resetFields()
        message.success(t('videoPlayer.adminControlsActivated'))
      } else {
        message.error(t('videoPlayer.adminPrivilegesRequired'))
      }
    } catch (error) {
      message.error(t('videoPlayer.invalidAdminCredentials'))
    }
  }

  const handleShowAdminLogin = () => {
    setShowAdminLogin(true)
  }

  const handleCancelAdminLogin = () => {
    setShowAdminLogin(false)
    adminLoginForm.resetFields()
  }

  // Fullscreen functionality
  const handleFullscreen = () => {
    const videoContainer = document.querySelector('.video-player-wrapper')
    if (!videoContainer) return

    if (!isFullscreen) {
      if (videoContainer.requestFullscreen) {
        videoContainer.requestFullscreen()
      } else if (videoContainer.webkitRequestFullscreen) {
        videoContainer.webkitRequestFullscreen()
      } else if (videoContainer.mozRequestFullScreen) {
        videoContainer.mozRequestFullScreen()
      } else if (videoContainer.msRequestFullscreen) {
        videoContainer.msRequestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    }
  }

  // Volume control functions
  const handleMuteToggle = () => {
    setMuted(!muted)
  }

  const handleVolumeUp = () => {
    setVolume(prev => Math.min(1, prev + 0.1))
    if (muted) setMuted(false)
  }

  const handleVolumeDown = () => {
    setVolume(prev => Math.max(0, prev - 0.1))
  }

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
    }
  }, [])

  // Show language selection modal when component mounts and module data is available
  useEffect(() => {
    if (moduleData && !languageConfirmed) {
      setShowLanguageModal(true)
    }
  }, [moduleData, languageConfirmed])

  // Update selected language when languageCode changes (from URL)
  useEffect(() => {
    if (languageCode) {
      setSelectedLanguage(languageCode)
    }
  }, [languageCode])



  if (isLoading || progressLoading) {
    return <LoadingImage text={t('videoPlayer.loadingVideo')} />
  }

  if (error) {
    const errorInfo = handleAPIError(error)
    return (
      <Alert
        message={t('videoPlayer.errorLoadingVideo')}
        description={errorInfo.message}
        type="error"
        showIcon
        action={
          <Button onClick={() => navigate('/training-modules')}>
            {t('videoPlayer.backToModules')}
          </Button>
        }
      />
    )
  }

  const { module, videos } = moduleData
  const video = videos?.find(v => v.language_code === languageCode)
  const quiz = video?.quiz_id ? video : null

  if (!video) {
    return (
      <Alert
        message={t('videoPlayer.videoNotFound')}
        description={t('videoPlayer.noVideoAvailable', { language: languageCode })}
        type="error"
        showIcon
        action={
          <Button onClick={() => navigate('/training-modules')}>
            {t('videoPlayer.backToModules')}
          </Button>
        }
      />
    )
  }

  // Safely get video URL with proper null checks
  const videoUrl = getVideoUrl(video)

  // If video URL is invalid, show error
  if (!videoUrl) {
    return (
      <Alert
        message={t('videoPlayer.videoUrlError')}
        description={t('videoPlayer.videoFileNotLoaded')}
        type="error"
        showIcon
        action={
          <Button onClick={() => navigate('/training-modules')}>
            {t('videoPlayer.backToModules')}
          </Button>
        }
      />
    )
  }

  // Remove unused progressPercent calculation

  return (
    <div>
      {/* Language Selection Modal */}
      <LanguageSelectionModal
        visible={showLanguageModal}
        availableLanguages={videos || []}
        selectedLanguage={selectedLanguage}
        onLanguageChange={setSelectedLanguage}
        onConfirm={handleLanguageSelection}
        onCancel={handleLanguageModalCancel}
        hasExistingProgress={progressData && progressData.length > 0}
      />

      {/* Main Video Player Content - Only show when language is confirmed */}
      {languageConfirmed && (
        <>
          {/* Custom styles for video security */}
          <style jsx>{`
        .video-player-wrapper video {
          pointer-events: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-panel {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-play-button {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-start-playback-button {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-timeline {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-current-time-display {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-time-remaining-display {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-mute-button {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-volume-slider {
          display: none !important;
        }
        .video-player-wrapper video::-webkit-media-controls-fullscreen-button {
          display: none !important;
        }
        .video-player-wrapper video::-moz-media-controls {
          display: none !important;
        }
        .video-player-wrapper {
          position: relative;
        }
        .video-player-wrapper::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
          z-index: 1;
        }
      `}</style>
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/training-modules')}
          >
            {t('videoPlayer.backToModules')}
          </Button>

          <Space>
            <Button
              icon={<SaveOutlined />}
              onClick={handleForceSaveProgress}
              loading={updateProgressMutation.isLoading}
            >
              {t('videoPlayer.saveProgress')}
            </Button>
            {!adminControlsActive && (
              <Button
                icon={<UnlockOutlined />}
                onClick={handleShowAdminLogin}
                type="dashed"
              >
                {t('videoPlayer.adminControls')}
              </Button>
            )}
            {adminControlsActive && (
              <Button
                icon={<UnlockOutlined />}
                type="primary"
                disabled
              >
                {t('videoPlayer.adminModeActive')}
              </Button>
            )}
            {videoCompleted && quiz && (
              <Button
                type="primary"
                icon={<QuestionCircleOutlined />}
                onClick={handleQuizClick}
              >
                {t('videoPlayer.goToQuiz')}
              </Button>
            )}
            <Button
              icon={<LogoutOutlined />}
              onClick={handleExitVideo}
              type="primary"
              danger
            >
              {t('videoPlayer.exitVideo')}
            </Button>
          </Space>
        </div>

        <Title level={2}>{module.title}</Title>

        <div style={{ display: 'flex', alignItems: 'center', gap: 16, marginBottom: 8 }}>
          <Text type="secondary">
            {t('videoPlayer.duration')}: {video.duration_seconds ? formatTime(video.duration_seconds) : t('videoPlayer.unknown')}
          </Text>

          {videos && videos.length > 1 && (
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <TranslationOutlined style={{ color: '#1890ff' }} />
              <Text strong>{t('videoPlayer.language')}:</Text>
              <Select
                value={languageCode}
                onChange={handleLanguageSwitch}
                style={{ minWidth: 120 }}
                size="small"
              >
                {videos.map(v => (
                  <Select.Option key={v.language_code} value={v.language_code}>
                    {v.language_name}
                  </Select.Option>
                ))}
              </Select>
            </div>
          )}

          {videos && videos.length === 1 && (
            <Text type="secondary">
              {t('videoPlayer.language')}: {video.language_name}
            </Text>
          )}
        </div>
      </div>

      <Card>
        <div className="video-player-container">
          <div
            className="video-player-wrapper"
            onContextMenu={(e) => e.preventDefault()}
            style={{
              userSelect: 'none',
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none'
            }}
          >
            <ReactPlayer
              ref={playerRef}
              url={videoUrl}
              width="100%"
              height="100%"
              playing={playing}
              volume={volume}
              muted={muted}
              controls={false}
              onPlay={handlePlay}
              onPause={handlePause}
              onProgress={handleProgress}
              onDuration={handleDuration}
              onEnded={handleEnded}
              onError={handlePlayerError}
              onReady={() => {
                // console.log('ReactPlayer is ready');
                setPlayerReady(true);
              }}
              config={{
                file: {
                  attributes: {
                    controlsList: 'nodownload noremoteplayback noplaybackrate',
                    disablePictureInPicture: true,
                    crossOrigin: 'anonymous',
                    preload: 'metadata'
                  }
                }
              }}
              progressInterval={1000}
              fallback={
                <div style={{
                  width: '100%',
                  height: '400px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f0f0f0',
                  border: '1px solid #d9d9d9'
                }}>
                  <div style={{ textAlign: 'center' }}>
                    <p>{t('videoPlayer.playerLoading')}</p>
                    <p>{t('videoPlayer.refreshIfPersists')}</p>
                  </div>
                </div>
              }
            />
          </div>
        </div>

        {/* Video Controls */}
        <VideoControls
          playing={playing}
          volume={volume}
          muted={muted}
          isFullscreen={isFullscreen}
          canSkipForward={(() => {
            const currentProgress = progressData?.find(p => p.language_code === languageCode)
            const hasCompletedBefore = currentProgress?.video_watched === true
            return hasCompletedBefore || adminControlsActive
          })()}
          onPlayPause={handlePlayPause}
          onSkipBackward={handleBackward}
          onSkipForward={handleForward}
          onVolumeUp={handleVolumeUp}
          onVolumeDown={handleVolumeDown}
          onToggleMute={handleMuteToggle}
          onToggleFullscreen={handleFullscreen}
          disabled={!playerReady}
        />

        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text strong>{t('videoPlayer.watchProgress')}</Text>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Text type="secondary">
                    {formatTime(watchTime)} / {formatTime(duration)}
                  </Text>
                  {updateProgressMutation.isLoading && (
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {t('videoPlayer.saving')}
                    </Text>
                  )}
                </div>
              </div>
              <VideoProgressBar
                watchTime={watchTime}
                maxWatched={maxWatched}
                duration={duration}
                onSeek={handleSeekTo}
                videoCompleted={videoCompleted}
                hasCompletedBefore={(() => {
                  const currentProgress = progressData?.find(p => p.language_code === languageCode)
                  return currentProgress?.video_watched === true
                })()}
                adminControlsActive={adminControlsActive}
              />

              {/* Show navigation status */}
              {(() => {
                const currentProgress = progressData?.find(p => p.language_code === languageCode)
                const hasCompletedBefore = currentProgress?.video_watched === true

                if (adminControlsActive) {
                  return (
                    <Text type="warning" style={{ fontSize: 12, display: 'block', marginTop: 4 }}>
                      {t('videoPlayer.adminControlsActiveStatus')}
                    </Text>
                  )
                } else if (hasCompletedBefore) {
                  return (
                    <Text type="success" style={{ fontSize: 12, display: 'block', marginTop: 4 }}>
                      {t('videoPlayer.videoCompletedNavigation')}
                    </Text>
                  )
                } else {
                  return (
                    <Text type="secondary" style={{ fontSize: 12, display: 'block', marginTop: 4 }}>
                      {t('videoPlayer.firstViewingLimitedForward')}
                    </Text>
                  )
                }
              })()}
            </div>

            {videoCompleted && quiz && (
              <div style={{ 
                background: '#f6ffed', 
                border: '1px solid #b7eb8f',
                borderRadius: 6,
                padding: 16,
                textAlign: 'center'
              }}>
                <Text strong style={{ color: '#52c41a', display: 'block', marginBottom: 12 }}>
                  {t('videoPlayer.videoCompletedQuizAvailable')}
                </Text>
                <Button
                  type="primary"
                  size="large"
                  icon={<QuestionCircleOutlined />}
                  onClick={handleQuizClick}
                >
                  {t('videoPlayer.takeQuiz')}
                </Button>
              </div>
            )}

            {!videoCompleted && (
              <Alert
                message={t('videoPlayer.videoViewingRequirements')}
                description={(() => {
                  const currentProgress = progressData?.find(p => p.language_code === languageCode)
                  const hasCompletedBefore = currentProgress?.video_watched === true

                  if (hasCompletedBefore) {
                    return t('videoPlayer.completedBeforeDescription')
                  } else {
                    return t('videoPlayer.mustWatchCompleteDescription')
                  }
                })()}
                type="info"
                showIcon
              />
            )}
          </Space>
        </div>
      </Card>
        </>
      )}



      {/* Admin Controls Modal */}
      <AdminControlsModal
        visible={showAdminLogin}
        onSubmit={handleAdminLogin}
        onCancel={handleCancelAdminLogin}
        loading={false}
      />
    </div>
  )
}

export default VideoPlayer
