/**
 * Admin Controls Modal Component
 * Extracted from VideoPlayer.jsx for better modularity
 */

import React from 'react'
import { Modal, Form, Input, Button, Space, Alert } from 'antd'
import { UnlockOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

function AdminControlsModal({
  visible,
  onSubmit,
  onCancel,
  loading = false
}) {
  const { t } = useTranslation()
  const [form] = Form.useForm()

  const handleSubmit = async (values) => {
    try {
      await onSubmit?.(values)
      form.resetFields()
    } catch (error) {
      // Error handling is done in parent component
      console.error('Admin login error:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel?.()
  }

  return (
    <Modal
      title={
        <Space>
          <UnlockOutlined />
          {t('videoPlayer.administratorLogin')}
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={400}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="large"
      >
        <Form.Item
          name="email"
          label={t('videoPlayer.adminEmail')}
          rules={[
            { required: true, message: t('videoPlayer.pleaseEnterAdminEmail') },
            { type: 'email', message: t('validation.email') }
          ]}
        >
          <Input 
            placeholder={t('videoPlayer.enterAdminEmail')}
            autoComplete="email"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label={t('videoPlayer.password')}
          rules={[
            { required: true, message: t('videoPlayer.pleaseEnterPassword') }
          ]}
        >
          <Input.Password 
            placeholder={t('videoPlayer.enterPassword')}
            autoComplete="current-password"
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0 }}>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button onClick={handleCancel}>
              {t('common.cancel')}
            </Button>
            <Button 
              type="primary" 
              htmlType="submit"
              loading={loading}
              icon={<UnlockOutlined />}
            >
              {t('videoPlayer.activateAdminControls')}
            </Button>
          </Space>
        </Form.Item>
      </Form>

      <Alert
        message={t('videoPlayer.adminControls')}
        description={t('videoPlayer.adminControlsDescription')}
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </Modal>
  )
}

export default AdminControlsModal
