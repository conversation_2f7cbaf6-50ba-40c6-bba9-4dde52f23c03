/**
 * Reusable Data Table Component
 * Standardized table with common configurations and features
 */

import React from 'react'
import { Table, Card, Typography, Space, Button } from 'antd'
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { getTableConfig } from '../../utils/tableHelpers'

const { Title } = Typography

function DataTable({
  title,
  columns = [],
  dataSource = [],
  loading = false,
  error = null,
  rowKey = 'id',
  pageSize = 10,
  showPagination = true,
  showSelection = false,
  selectedRowKeys = [],
  onSelectionChange,
  onRefresh,
  onExport,
  expandable,
  scroll,
  size = 'middle',
  showHeader = true,
  showRefresh = true,
  showExport = false,
  cardProps = {},
  tableProps = {},
  headerActions = [],
  totalCount,
  ...props
}) {
  const { t } = useTranslation()

  const tableConfig = getTableConfig({
    t,
    columns,
    dataSource,
    loading,
    rowKey,
    pageSize,
    showPagination,
    showSelection,
    onSelectionChange,
    selectedRowKeys,
    expandable,
    scroll,
    size
  })

  const handleRefresh = () => {
    onRefresh?.()
  }

  const handleExport = () => {
    onExport?.()
  }

  const renderHeader = () => {
    if (!showHeader) return null

    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: 16 
      }}>
        <div>
          {title && <Title level={4} style={{ margin: 0 }}>{title}</Title>}
          {totalCount !== undefined && (
            <Typography.Text type="secondary" style={{ fontSize: 12 }}>
              {t('components.tables.pagination.total', { total: totalCount })}
            </Typography.Text>
          )}
        </div>
        
        <Space>
          {headerActions.map((action, index) => (
            <Button
              key={index}
              type={action.type || 'default'}
              icon={action.icon}
              onClick={action.onClick}
              loading={action.loading}
              disabled={action.disabled}
              {...action.props}
            >
              {action.text}
            </Button>
          ))}
          
          {showRefresh && (
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              disabled={loading}
              title={t('common.refresh')}
            />
          )}
          
          {showExport && (
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
              disabled={loading || !dataSource.length}
              title={t('common.download')}
            />
          )}
        </Space>
      </div>
    )
  }

  const renderSelectionInfo = () => {
    if (!showSelection || !selectedRowKeys.length) return null

    return (
      <div style={{ 
        marginBottom: 16,
        padding: 8,
        backgroundColor: '#e6f7ff',
        borderRadius: 4,
        border: '1px solid #91d5ff'
      }}>
        <Typography.Text>
          {t('components.tables.selected', { count: selectedRowKeys.length })}
        </Typography.Text>
      </div>
    )
  }

  if (error) {
    return (
      <Card {...cardProps}>
        {renderHeader()}
        <div style={{ textAlign: 'center', padding: 40 }}>
          <Typography.Text type="danger">
            {error.message || t('messages.loadingError')}
          </Typography.Text>
          {showRefresh && (
            <div style={{ marginTop: 16 }}>
              <Button 
                type="primary" 
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              >
                {t('common.refresh')}
              </Button>
            </div>
          )}
        </div>
      </Card>
    )
  }

  return (
    <Card {...cardProps}>
      {renderHeader()}
      {renderSelectionInfo()}
      
      <Table
        {...tableConfig}
        {...tableProps}
        {...props}
      />
    </Card>
  )
}

export default DataTable
