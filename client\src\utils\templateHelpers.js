/**
 * Template and String Utilities
 * Helper functions for managing templates and extracting hardcoded strings
 */

/**
 * Common email templates that can be imported as modules
 */
export const emailTemplates = {
  userCreation: {
    subject: {
      en: 'Your Training System Login Credentials',
      zh: '您的培训系统登录凭据'
    },
    body: {
      en: `Dear {{fullName}},

You have been granted access to our Internal Training System. This system contains important cybersecurity training materials that all team members are required to complete.

Please use the following credentials to log in:

Login Credentials:
Email: {{email}}
Password: {{password}}

Important Security Notice:
• Your password will expire in {{passwordExpirationDays}} days
• Contact your administrator if you need assistance
• Keep your login credentials secure and do not share them with others
• Please log in promptly to begin your training

If you have any questions or technical issues, please feel free to contact the IT support team.

Best regards,
IT Administration Team

This email contains sensitive information. Please handle securely and delete after use.`,
      zh: `亲爱的 {{fullName}}，

您已被授权访问我们的内部培训系统。该系统包含所有团队成员都需要完成的重要网络安全培训材料。

请使用以下凭据登录：

登录凭据：
邮箱：{{email}}
密码：{{password}}

重要安全提示：
• 您的密码将在 {{passwordExpirationDays}} 天后过期
• 如需帮助请联系管理员
• 请妥善保管您的登录凭据，不要与他人分享
• 请及时登录开始您的培训

如果您有任何问题或技术问题，请随时联系IT支持团队。

此致
敬礼，
IT管理团队

此邮件包含敏感信息。请安全处理并在使用后删除。`
    }
  },
  
  passwordReset: {
    subject: {
      en: 'Password Reset - Training System',
      zh: '密码重置 - 培训系统'
    },
    body: {
      en: `Dear {{fullName}},

Your password has been reset for the Internal Training System.

New Login Credentials:
Email: {{email}}
Password: {{password}}

Security Information:
• This password will expire in {{passwordExpirationDays}} days
• Please log in and change your password if desired
• Contact your administrator if you did not request this reset

Best regards,
IT Administration Team`,
      zh: `亲爱的 {{fullName}}，

您的内部培训系统密码已重置。

新登录凭据：
邮箱：{{email}}
密码：{{password}}

安全信息：
• 此密码将在 {{passwordExpirationDays}} 天后过期
• 请登录并根据需要更改密码
• 如果您没有请求此重置，请联系管理员

此致
敬礼，
IT管理团队`
    }
  }
}

/**
 * Replace template variables with actual values
 * @param {string} template - Template string with {{variable}} placeholders
 * @param {Object} variables - Object with variable values
 * @returns {string} - Template with variables replaced
 */
export const replaceTemplateVariables = (template, variables = {}) => {
  if (!template || typeof template !== 'string') return ''
  
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return variables[key] !== undefined ? variables[key] : match
  })
}

/**
 * Get available template variables for email templates
 * @returns {Object} - Object describing available variables
 */
export const getTemplateVariables = () => ({
  firstName: 'User\'s first name',
  lastName: 'User\'s last name',
  fullName: 'User\'s full name',
  email: 'User\'s email address',
  password: 'Generated password',
  passwordExpirationDays: 'Number of days until password expires',
  passwordExpirationDate: 'Date when password expires',
  date: 'Current date',
  time: 'Current time',
  year: 'Current year'
})

/**
 * Generate template variables from user data
 * @param {Object} userData - User data object
 * @param {Object} systemData - System configuration data
 * @returns {Object} - Template variables object
 */
export const generateTemplateVariables = (userData = {}, systemData = {}) => {
  const now = new Date()
  const expirationDate = new Date(now.getTime() + (systemData.passwordExpirationDays || 8) * 24 * 60 * 60 * 1000)

  // Handle both camelCase and snake_case property names for compatibility
  const firstName = userData.firstName || userData.first_name || ''
  const lastName = userData.lastName || userData.last_name || ''
  const fullName = userData.fullName || userData.full_name || `${firstName} ${lastName}`.trim() || userData.email || ''

  return {
    firstName: firstName,
    lastName: lastName,
    fullName: fullName,
    email: userData.email || '',
    password: userData.password || '',
    passwordExpirationDays: systemData.passwordExpirationDays || 8,
    passwordExpirationDate: expirationDate.toLocaleDateString(),
    date: now.toLocaleDateString(),
    time: now.toLocaleTimeString(),
    year: now.getFullYear()
  }
}

/**
 * Validate template syntax
 * @param {string} template - Template string to validate
 * @returns {Object} - Validation result with isValid and errors
 */
export const validateTemplate = (template) => {
  if (!template || typeof template !== 'string') {
    return { isValid: false, errors: ['Template must be a non-empty string'] }
  }
  
  const errors = []
  const variablePattern = /\{\{(\w+)\}\}/g
  const availableVars = Object.keys(getTemplateVariables())
  let match
  
  while ((match = variablePattern.exec(template)) !== null) {
    const variable = match[1]
    if (!availableVars.includes(variable)) {
      errors.push(`Unknown variable: {{${variable}}}`)
    }
  }
  
  // Check for unclosed braces
  const openBraces = (template.match(/\{\{/g) || []).length
  const closeBraces = (template.match(/\}\}/g) || []).length
  
  if (openBraces !== closeBraces) {
    errors.push('Mismatched template braces')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Extract hardcoded strings from component for i18n
 * This is a utility function to help identify strings that should be moved to i18n
 * @param {string} componentCode - Component source code
 * @returns {Array} - Array of potential hardcoded strings
 */
export const extractHardcodedStrings = (componentCode) => {
  if (!componentCode || typeof componentCode !== 'string') return []
  
  const strings = []
  
  // Match strings in quotes that are not imports, variables, or JSX props
  const stringPatterns = [
    /"([^"]{3,})"/g, // Double quoted strings
    /'([^']{3,})'/g, // Single quoted strings
    /`([^`]{3,})`/g  // Template literals
  ]
  
  stringPatterns.forEach(pattern => {
    let match
    while ((match = pattern.exec(componentCode)) !== null) {
      const string = match[1]
      
      // Skip if it looks like a technical string (imports, URLs, etc.)
      if (
        string.includes('/') || // URLs or paths
        string.includes('.') && string.length < 20 || // File extensions or short technical strings
        string.match(/^[A-Z_]+$/) || // Constants
        string.match(/^\w+\.\w+/) || // Object properties
        string.includes('import') ||
        string.includes('export') ||
        string.includes('require')
      ) {
        continue
      }
      
      // Add if it looks like user-facing text
      if (string.length >= 3 && !strings.includes(string)) {
        strings.push(string)
      }
    }
  })
  
  return strings.sort()
}

/**
 * Generate i18n key from string
 * @param {string} text - Text to generate key for
 * @param {string} namespace - Namespace for the key
 * @returns {string} - Generated i18n key
 */
export const generateI18nKey = (text, namespace = 'common') => {
  if (!text || typeof text !== 'string') return ''
  
  const key = text
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 50) // Limit length
  
  return `${namespace}.${key}`
}
