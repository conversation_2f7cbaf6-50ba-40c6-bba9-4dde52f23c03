import React, { useState } from 'react'
import { Card, Form, Input, Switch, Button, Select, Typography, Space, message, Modal, Spin, Alert, InputNumber } from 'antd'
import { MailOutlined, ExperimentOutlined, SaveOutlined, InfoCircleOutlined } from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import { emailConfigAPI, handleAPIError } from '../../services/api'

const { Title, Text, Paragraph } = Typography
const { Option } = Select

function EmailConfiguration() {
  const { t } = useTranslation()
  const queryClient = useQueryClient()
  const [form] = Form.useForm()
  const [testForm] = Form.useForm()
  const [testModalVisible, setTestModalVisible] = useState(false)
  const [testingEmail, setTestingEmail] = useState(false)
  const [hasExistingPassword, setHasExistingPassword] = useState(false)

  // Fetch email configuration
  const { data: configData, isLoading, error } = useQuery({
    queryKey: ['email-config'],
    queryFn: emailConfigAPI.getConfig,
    select: (response) => response.data.config,
    onSuccess: (config) => {
      form.setFieldsValue(config)
      setHasExistingPassword(config.has_existing_password || false)
    }
  })

  // Update configuration mutation
  const updateConfigMutation = useMutation(
    (config) => emailConfigAPI.updateConfig(config),
    {
      onSuccess: () => {
        message.success(t('emailConfig.configurationUpdatedSuccessfully'))
        queryClient.invalidateQueries('email-config')
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        message.error(`${t('emailConfig.failedToUpdateConfiguration')}: ${errorInfo.message}`)
      }
    }
  )

  // Test email mutation (server-side)
  const testEmailMutation = useMutation(
    (testConfig) => emailConfigAPI.testConfig(testConfig),
    {
      onSuccess: (response) => {
        message.success(`${t('emailConfig.testEmailSentSuccessfully')} (Server-side)`)
        setTestModalVisible(false)
        testForm.resetFields()
        console.log('Server-side email test successful:', response.data)
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error)
        console.error('Server-side email test error:', error)
        message.error(`Server-side test failed: ${errorInfo.message}`)
      }
    }
  )

  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      updateConfigMutation.mutate(values)
    } catch (error) {
      console.error('Form validation error:', error)
    }
  }

  const handleTestEmail = async () => {
    try {
      const configValues = await form.validateFields()
      const testValues = await testForm.validateFields()
      
      const testConfig = {
        ...configValues,
        test_email: testValues.test_email
      }
      
      setTestingEmail(true)
      testEmailMutation.mutate(testConfig)
    } catch (error) {
      console.error('Test form validation error:', error)
    } finally {
      setTestingEmail(false)
    }
  }

  const emailMethodOptions = [
    { value: 'clipboard', label: t('emailConfig.clipboardMethod') },
    { value: 'smtp', label: t('emailConfig.smtpMethod') }
  ]

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message={t('emailConfig.failedToLoadConfiguration')}
        description={error.message}
        type="error"
        showIcon
      />
    )
  }

  return (
    <div>
      <Title level={2}>{t('emailConfig.title')}</Title>
      
      <Card>
        <Form
          form={form}
          layout="vertical"
          initialValues={configData}
        >
          {/* Email System Toggle */}
          <Form.Item
            name="email_enabled"
            label={t('emailConfig.enableEmailSystem')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          {/* Email Method Selection */}
          <Form.Item
            name="email_method"
            label={t('emailConfig.emailMethod')}
            tooltip={t('emailConfig.emailMethodTooltip')}
          >
            <Select>
              {emailMethodOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* SMTP Configuration - Only show when SMTP is selected */}
          <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => 
            prevValues.email_method !== currentValues.email_method
          }>
            {({ getFieldValue }) => {
              const emailMethod = getFieldValue('email_method')
              
              if (emailMethod === 'smtp') {
                return (
                  <>
                    <Title level={4} style={{ marginTop: 24 }}>
                      <MailOutlined /> {t('emailConfig.smtpConfiguration')}
                    </Title>
                    
                    <Form.Item
                      name="smtp_host"
                      label={t('emailConfig.smtpHost')}
                      rules={[
                        { required: true, message: t('emailConfig.smtpHostRequired') }
                      ]}
                    >
                      <Input placeholder="smtp.gmail.com" />
                    </Form.Item>

                    <Form.Item
                      name="smtp_port"
                      label={t('emailConfig.smtpPort')}
                      rules={[
                        { required: true, message: t('emailConfig.smtpPortRequired') }
                      ]}
                    >
                      <InputNumber min={1} max={65535} style={{ width: '100%' }} />
                    </Form.Item>

                    <Form.Item
                      name="smtp_secure"
                      label={t('emailConfig.smtpSecure')}
                      valuePropName="checked"
                      tooltip={t('emailConfig.smtpSecureTooltip')}
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="smtp_user"
                      label={t('emailConfig.smtpUsername')}
                    >
                      <Input placeholder={t('emailConfig.smtpUsernamePlaceholder')} />
                    </Form.Item>

                    <Form.Item
                      name="smtp_pass"
                      label={hasExistingPassword ? t('emailConfig.newSmtpPassword') : t('emailConfig.smtpPassword')}
                      extra={hasExistingPassword ? t('emailConfig.newPasswordHint') : undefined}
                    >
                      <Input.Password
                        placeholder={hasExistingPassword ? t('emailConfig.newPasswordPlaceholder') : t('emailConfig.smtpPasswordPlaceholder')}
                      />
                    </Form.Item>

                    <Form.Item
                      name="email_from_name"
                      label={t('emailConfig.fromName')}
                      rules={[
                        { required: true, message: t('emailConfig.fromNameRequired') }
                      ]}
                    >
                      <Input placeholder="Training System" />
                    </Form.Item>

                    <Form.Item
                      name="email_from_address"
                      label={t('emailConfig.fromAddress')}
                      rules={[
                        { required: true, message: t('emailConfig.fromAddressRequired') },
                        { type: 'email', message: t('emailConfig.fromAddressInvalid') }
                      ]}
                    >
                      <Input placeholder="<EMAIL>" />
                    </Form.Item>

                    <Form.Item
                      name="email_reply_to_address"
                      label={t('emailConfig.replyToAddress')}
                      tooltip={t('emailConfig.replyToAddressTooltip')}
                      rules={[
                        { type: 'email', message: t('emailConfig.replyToAddressInvalid') }
                      ]}
                    >
                      <Input placeholder={t('emailConfig.replyToAddressPlaceholder')} />
                    </Form.Item>

                    <Form.Item
                      name="email_cc_addresses"
                      label={t('emailConfig.ccAddresses')}
                      tooltip={t('emailConfig.ccAddressesTooltip')}
                    >
                      <Input.TextArea
                        placeholder={t('emailConfig.ccAddressesPlaceholder')}
                        rows={2}
                      />
                    </Form.Item>

                    <Title level={5} style={{ marginTop: 24 }}>
                      📝 {t('emailConfig.emailTemplate')}
                    </Title>

                    <Title level={6} style={{ marginTop: 16, color: '#1890ff' }}>
                      🇺🇸 {t('emailConfig.englishTemplate')}
                    </Title>

                    <Form.Item
                      name="email_template_subject_en"
                      label={t('emailConfig.templateSubjectEn')}
                      tooltip={t('emailConfig.templateSubjectTooltip')}
                    >
                      <Input placeholder="Your Training System Login Credentials" />
                    </Form.Item>

                    <Form.Item
                      name="email_template_body_en"
                      label={t('emailConfig.templateBodyEn')}
                      tooltip={t('emailConfig.templateBodyTooltip')}
                    >
                      <Input.TextArea
                        placeholder="Dear {{fullName}},&#10;&#10;Your account has been created for the Internal Training System.&#10;&#10;Login Credentials:&#10;Email: {{email}}&#10;Password: {{password}}&#10;&#10;Best regards,&#10;Training System Administration"
                        rows={6}
                        style={{ fontFamily: 'monospace' }}
                      />
                    </Form.Item>

                    <Title level={6} style={{ marginTop: 24, color: '#1890ff' }}>
                      🇨🇳 {t('emailConfig.chineseTemplate')}
                    </Title>

                    <Form.Item
                      name="email_template_subject_zh"
                      label={t('emailConfig.templateSubjectZh')}
                      tooltip={t('emailConfig.templateSubjectTooltip')}
                    >
                      <Input placeholder="您的培训系统登录凭据" />
                    </Form.Item>

                    <Form.Item
                      name="email_template_body_zh"
                      label={t('emailConfig.templateBodyZh')}
                      tooltip={t('emailConfig.templateBodyTooltip')}
                    >
                      <Input.TextArea
                        placeholder="亲爱的 {{fullName}}，&#10;&#10;您的内部培训系统账户已创建。&#10;&#10;登录凭据：&#10;邮箱：{{email}}&#10;密码：{{password}}&#10;&#10;此致，&#10;培训系统管理员"
                        rows={6}
                        style={{ fontFamily: 'monospace' }}
                      />
                    </Form.Item>

                    {/* Email Template Variables Information */}
                    <Alert
                      message={t('emailConfig.emailTemplateVariablesNote')}
                      description={
                        <div>
                          <p>{t('emailConfig.emailTemplateVariablesDescription')}</p>
                          <div style={{ marginTop: 12 }}>
                            <Text strong>{t('emailConfig.availableVariables')}:</Text>
                            <div style={{ marginTop: 8, display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '8px' }}>
                              <Text code>{'{{firstName}}'}</Text> - {t('emailConfig.userFirstName')}
                              <Text code>{'{{lastName}}'}</Text> - {t('emailConfig.userLastName')}
                              <Text code>{'{{fullName}}'}</Text> - {t('emailConfig.userFullName')}
                              <Text code>{'{{email}}'}</Text> - {t('emailConfig.userEmail')}
                              <Text code>{'{{password}}'}</Text> - {t('emailConfig.generatedPassword')}
                              <Text code>{'{{passwordExpirationDays}}'}</Text> - {t('emailConfig.passwordExpirationDaysVar')}
                              <Text code>{'{{passwordExpirationDate}}'}</Text> - {t('emailConfig.passwordExpirationDateVar')}
                              <Text code>{'{{date}}'}</Text> - {t('emailConfig.currentDate')}
                              <Text code>{'{{time}}'}</Text> - {t('emailConfig.currentTime')}
                              <Text code>{'{{year}}'}</Text> - {t('emailConfig.currentYear')}
                            </div>
                          </div>
                        </div>
                      }
                      type="info"
                      showIcon
                      style={{ marginTop: 24, marginBottom: 0 }}
                    />
                  </>
                )
              }
              return null
            }}
          </Form.Item>

          {/* Action Buttons */}
          <Form.Item style={{ marginTop: 32 }}>
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={updateConfigMutation.isLoading}
              >
                {t('emailConfig.saveConfiguration')}
              </Button>
              
              <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => 
                prevValues.email_method !== currentValues.email_method
              }>
                {({ getFieldValue }) => {
                  const emailMethod = getFieldValue('email_method')
                  
                  if (emailMethod === 'smtp') {
                    return (
                      <Button
                        icon={<ExperimentOutlined />}
                        onClick={() => setTestModalVisible(true)}
                      >
                        {t('emailConfig.testConfiguration')}
                      </Button>
                    )
                  }
                  return null
                }}
              </Form.Item>
            </Space>
          </Form.Item>
        </Form>

        {/* Information Section */}
        <Card type="inner" style={{ marginTop: 24 }}>
          <Title level={5}>
            <InfoCircleOutlined /> {t('emailConfig.informationTitle')}
          </Title>
          <Paragraph>
            <Text strong>{t('emailConfig.clipboardMethod')}:</Text> {t('emailConfig.clipboardMethodDescription')}
          </Paragraph>
          <Paragraph>
            <Text strong>{t('emailConfig.smtpMethod')}:</Text> {t('emailConfig.smtpMethodDescription')}
          </Paragraph>
          <Paragraph type="secondary">
            {t('emailConfig.securityNote')}
          </Paragraph>
        </Card>
      </Card>

      {/* Test Email Modal */}
      <Modal
        title={t('emailConfig.testEmailConfiguration')}
        open={testModalVisible}
        onCancel={() => {
          setTestModalVisible(false)
          testForm.resetFields()
        }}
        footer={[
          <Button key="cancel" onClick={() => setTestModalVisible(false)}>
            {t('common.cancel')}
          </Button>,
          <Button
            key="test"
            type="primary"
            icon={<ExperimentOutlined />}
            onClick={handleTestEmail}
            loading={testingEmail || testEmailMutation.isLoading}
          >
            {t('emailConfig.sendTestEmail')}
          </Button>
        ]}
      >
        <Form form={testForm} layout="vertical">
          <Form.Item
            name="test_email"
            label={t('emailConfig.testEmailAddress')}
            rules={[
              { required: true, message: t('emailConfig.testEmailRequired') },
              { type: 'email', message: t('emailConfig.testEmailInvalid') }
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>
        </Form>
        <Alert
          message={`${t('emailConfig.testEmailNote')} (Server-side processing)`}
          description="Email will be sent directly from the server using your SMTP configuration."
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      </Modal>
    </div>
  )
}

export default EmailConfiguration
