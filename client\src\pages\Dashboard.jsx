import React, { useState, useEffect } from 'react'
import { Card, Row, Col, <PERSON>po<PERSON>, Button, Progress, Alert, Modal, Space } from 'antd'
import { BookOutlined, TrophyOutlined, PlayCircleOutlined, QuestionCircleOutlined, FileTextOutlined, TranslationOutlined } from '@ant-design/icons'
import { useQuery } from 'react-query'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { trainingAPI, usersAPI, handleAPIError } from '../services/api'
import { useAuth } from '../contexts/AuthContext'
import { useTutorial } from '../contexts/TutorialContext'
import LoadingImage from '../components/LoadingImage'

const { Title, Text, Paragraph } = Typography

function Dashboard() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { t } = useTranslation()
  const {
    startTutorial
  } = useTutorial()

  // Local state for quiz demo
  const [showQuizDemo, setShowQuizDemo] = useState(false)

  // Fetch training modules
  const { data: modulesData, isLoading: modulesLoading, error: modulesError } = useQuery({
    queryKey: ['training-modules'],
    queryFn: trainingAPI.getModules,
    select: (response) => response.data.modules,
  })

  // Fetch user profile with progress
  const { data: profileData, isLoading: profileLoading, error: profileError } = useQuery({
    queryKey: ['user-profile', user?.email],
    queryFn: usersAPI.getProfile,
    select: (response) => response.data,
    enabled: !!user?.email, // Only fetch when user is available
  })

  // Show tutorial for non-admin users on first visit - MUST be before early returns
  useEffect(() => {
    if (user?.email && user.isAdmin !== undefined && !user.isAdmin) {
      // Check if user has seen the tutorial before
      const hasSeenTutorial = localStorage.getItem(`tutorial_seen_${user.email}`)
      if (!hasSeenTutorial) {
        startTutorial()
      }
    }
  }, [user?.email, user?.isAdmin, startTutorial])

  const isLoading = modulesLoading || profileLoading
  const error = modulesError || profileError

  if (isLoading) {
    return <LoadingImage text={t('dashboard.loadingDashboard')} />
  }

  if (error) {
    const errorInfo = handleAPIError(error)
    return (
      <Alert
        message={t('dashboard.errorLoadingDashboard')}
        description={errorInfo.message}
        type="error"
        showIcon
      />
    )
  }

  const modules = modulesData || []
  const userProgress = profileData?.progress || []

  // Calculate statistics
  const totalModules = modules.length
  // Count modules where user has actual certificate (certificate_number exists)
  const completedModules = userProgress.filter(p => p.certificate_number).length
  const inProgressModules = userProgress.filter(p => p.video_watched && !p.certificate_number).length

  // Calculate average score only from completed quizzes (where quiz_score is not null)
  const completedQuizzes = userProgress.filter(p => p.quiz_score !== null && p.quiz_score !== undefined)
  const averageScore = completedQuizzes.length > 0
    ? Math.round(completedQuizzes.reduce((sum, p) => sum + p.quiz_score, 0) / completedQuizzes.length)
    : 0





  const handleShowQuizDemo = () => {
    setShowQuizDemo(true)
  }

  const handleCloseQuizDemo = () => {
    setShowQuizDemo(false)
  }



  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          {t('dashboard.welcomeBack', { name: user?.fullName || user?.email })}
        </Title>
        <Paragraph type="secondary">
          {t('dashboard.trackProgressDescription')}
        </Paragraph>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <div className="stat-card">
              <div className="stat-number">{totalModules}</div>
              <div className="stat-label">{t('dashboard.totalModules')}</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <div className="stat-card">
              <div className="stat-number">{completedModules}</div>
              <div className="stat-label">{t('dashboard.completed')}</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <div className="stat-card">
              <div className="stat-number">{inProgressModules}</div>
              <div className="stat-label">{t('dashboard.inProgress')}</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <div className="stat-card">
              <div className="stat-number">{averageScore}%</div>
              <div className="stat-label">{t('dashboard.averageScore')}</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Progress Overview */}
      {totalModules > 0 && (
        <Card style={{ marginBottom: 24 }}>
          <Title level={4}>{t('dashboard.overallProgress')}</Title>
          <Progress
            percent={Math.round((completedModules / totalModules) * 100)}
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
          <Text type="secondary">
            {t('dashboard.modulesCompleted', { completed: completedModules, total: totalModules })}
          </Text>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <Title level={4} style={{ marginBottom: 16 }}>
          <BookOutlined style={{ marginRight: 8 }} />
          {t('dashboard.quickActions')}
        </Title>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={8}>
            <Button
              type="primary"
              size="large"
              icon={<BookOutlined />}
              onClick={() => navigate('/training-modules')}
              style={{ width: '100%', height: 80 }}
            >
              <div>
                <div style={{ fontSize: 16, fontWeight: 'bold' }}>{t('navigation.trainingModules')}</div>
                <div style={{ fontSize: 12, opacity: 0.8 }}>{t('dashboard.browseAllModules')}</div>
              </div>
            </Button>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Button
              size="large"
              icon={<TrophyOutlined />}
              onClick={() => navigate('/profile')}
              style={{ width: '100%', height: 80 }}
            >
              <div>
                <div style={{ fontSize: 16, fontWeight: 'bold' }}>{t('dashboard.viewProgress')}</div>
                <div style={{ fontSize: 12, opacity: 0.8 }}>{t('dashboard.trackYourAchievements')}</div>
              </div>
            </Button>
          </Col>
        </Row>
      </Card>



      {/* Quiz Demo Modal */}
      <Modal
        title={t('loginTutorial.quizExampleTitle')}
        open={showQuizDemo}
        onCancel={handleCloseQuizDemo}
        footer={
          <Button type="primary" onClick={handleCloseQuizDemo}>
            {t('common.close')}
          </Button>
        }
        width={500}
      >
        <div style={{ padding: '16px 0' }}>
          <div style={{ marginBottom: 16 }}>
            <div style={{ fontWeight: 'bold', marginBottom: 8 }}>
              {t('loginTutorial.sampleQuestionTitle')}
            </div>
            <div style={{
              padding: 16,
              backgroundColor: '#f9f9f9',
              borderRadius: 6,
              border: '1px solid #e8e8e8'
            }}>
              {t('loginTutorial.sampleQuestionText')}
            </div>
          </div>

          <div style={{ marginBottom: 16 }}>
            <div style={{ fontWeight: 'bold', marginBottom: 8 }}>
              {t('loginTutorial.answerOptionsTitle')}
            </div>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ padding: '8px 12px', border: '1px solid #d9d9d9', borderRadius: 4 }}>
                A) {t('loginTutorial.sampleAnswerA')}
              </div>
              <div style={{ padding: '8px 12px', border: '2px solid #52c41a', borderRadius: 4, backgroundColor: '#f6ffed' }}>
                B) {t('loginTutorial.sampleAnswerB')} ✓
              </div>
              <div style={{ padding: '8px 12px', border: '1px solid #d9d9d9', borderRadius: 4 }}>
                C) {t('loginTutorial.sampleAnswerC')}
              </div>
            </Space>
          </div>

          <Alert
            message={t('loginTutorial.quizFeatures')}
            description={t('loginTutorial.quizFeaturesDescription')}
            type="info"
            showIcon
          />
        </div>
      </Modal>
    </div>
  )
}

export default Dashboard
