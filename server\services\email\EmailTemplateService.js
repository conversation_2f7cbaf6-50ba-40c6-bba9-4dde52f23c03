const fs = require('fs');
const path = require('path');
const systemConfigService = require('../systemConfigService');

class EmailTemplateService {
  // Process template variables with improved stability
  processTemplate(template, variables) {
    if (!template || typeof template !== 'string') {
      console.warn('Invalid template provided to processTemplate');
      return '';
    }

    let processed = template;

    // Process variables in a specific order - password last to avoid conflicts
    const sortedKeys = Object.keys(variables).sort((a, b) => {
      if (a === 'password') return 1;
      if (b === 'password') return -1;
      return a.localeCompare(b);
    });

    sortedKeys.forEach(key => {
      const value = variables[key];
      if (value === null || value === undefined) {
        console.warn(`Variable ${key} is null or undefined, skipping`);
        return;
      }

      const regex = new RegExp(`{{${key}}}`, 'g');
      const stringValue = String(value);

      // Special handling for password to prevent corruption
      if (key === 'password') {
        // For passwords, use direct string replacement without any escaping
        // This prevents HTML entity corruption
        processed = processed.split(`{{${key}}}`).join(stringValue);
        console.log(`Replaced {{${key}}} with password value (${stringValue.length} chars)`);
      } else {
        // For other variables, use safer replacement
        processed = processed.replace(regex, stringValue);
      }
    });

    return processed;
  }

  // Alternative template processing method using simple string replacement
  processTemplateSimple(template, variables) {
    if (!template || typeof template !== 'string') {
      return '';
    }

    let processed = template;

    // Use simple string replacement to avoid regex complications
    Object.keys(variables).forEach(key => {
      const value = variables[key];
      if (value !== null && value !== undefined) {
        const placeholder = `{{${key}}}`;
        const stringValue = String(value);

        // Use split/join instead of replace to avoid regex issues
        processed = processed.split(placeholder).join(stringValue);
      }
    });

    return processed;
  }

  // Extract name from email address as fallback
  extractNameFromEmail(email) {
    try {
      // Get the part before @ symbol
      const localPart = email.split('@')[0];

      // Handle common email patterns
      if (localPart.includes('.')) {
        // <EMAIL> -> John Doe
        const parts = localPart.split('.');
        return {
          firstName: this.capitalizeFirst(parts[0]),
          lastName: this.capitalizeFirst(parts[1] || '')
        };
      } else if (localPart.includes('_')) {
        // <EMAIL> -> John Doe
        const parts = localPart.split('_');
        return {
          firstName: this.capitalizeFirst(parts[0]),
          lastName: this.capitalizeFirst(parts[1] || '')
        };
      } else {
        // <EMAIL> -> Johndoe
        return {
          firstName: this.capitalizeFirst(localPart),
          lastName: ''
        };
      }
    } catch (error) {
      return { firstName: '', lastName: '' };
    }
  }

  // Capitalize first letter of a string
  capitalizeFirst(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  // Get TradeLink logo as base64 for email embedding
  getTradeLinkLogoBase64() {
    try {
      const logoPath = path.join(__dirname, '..', '..', 'assets', 'email-sig_tradelink_logo_-_colour.png');
      if (fs.existsSync(logoPath)) {
        const logoBuffer = fs.readFileSync(logoPath);
        return `data:image/png;base64,${logoBuffer.toString('base64')}`;
      }
    } catch (error) {
      console.error('Error loading TradeLink email signature logo:', error);
    }
    return null;
  }

  // Get logo as attachment for email (better compatibility)
  getTradeLinkLogoAttachment() {
    try {
      const logoPath = path.join(__dirname, '..', '..', 'assets', 'email-sig_tradelink_logo_-_colour.png');
      if (fs.existsSync(logoPath)) {
        return {
          filename: 'tradelink-logo.png',
          path: logoPath,
          cid: 'tradelink-logo' // Content-ID for referencing in HTML
        };
      }
    } catch (error) {
      console.error('Error loading TradeLink logo attachment:', error);
    }
    return null;
  }

  // Get logo URL for hosted version (if available)
  getTradeLinkLogoUrl() {
    // For now, return null - hosted approach has middleware conflicts
    // Focus on CID attachment approach which is more reliable for emails
    return null;
  }

  // Generate HTML email template for login credentials using configurable template
  async generateLoginEmailHTML(config, userEmail, password, firstName = '', lastName = '', language = 'en') {
    try {
      // Get template from config or use default based on language
      let subject, bodyTemplate;

      if (language === 'zh') {
        subject = config?.email_template_subject_zh || '您的TradeLink培训系统登录凭据';
        bodyTemplate = config?.email_template_body_zh || this.getDefaultEmailTemplate('zh');
      } else {
        subject = config?.email_template_subject_en || 'Your TradeLink Training System Login Credentials';
        bodyTemplate = config?.email_template_body_en || this.getDefaultEmailTemplate('en');
      }

      // Debug: Check if templates contain malformed content
      console.log('Email template debug:', {
        language,
        subjectLength: subject?.length,
        bodyTemplateLength: bodyTemplate?.length,
        subjectContainsDoublePassword: subject?.includes('{{password}}{{password}}'),
        bodyContainsDoublePassword: bodyTemplate?.includes('{{password}}{{password}}'),
        bodyTemplatePreview: bodyTemplate?.substring(0, 200)
      });

      // If names are not provided, try to extract from email
      let finalFirstName = firstName;
      let finalLastName = lastName;

      if (!firstName && !lastName) {
        console.log('Names not provided, extracting from email address...');
        const extracted = this.extractNameFromEmail(userEmail);
        finalFirstName = extracted.firstName;
        finalLastName = extracted.lastName;
        console.log(`Extracted names: ${finalFirstName} ${finalLastName}`);
      }

      // Prepare variables for template processing
      const fullName = `${finalFirstName} ${finalLastName}`.trim();
      const passwordExpirationDays = await systemConfigService.getPasswordExpirationDays();

      // Calculate password expiration date
      const passwordExpirationDate = new Date();
      passwordExpirationDate.setDate(passwordExpirationDate.getDate() + passwordExpirationDays);

      const variables = {
        firstName: finalFirstName,
        lastName: finalLastName,
        email: userEmail,
        password: password,
        fullName: fullName || 'Team Member', // Fallback if no name available
        date: new Date().toLocaleDateString(),
        time: new Date().toLocaleTimeString(),
        year: new Date().getFullYear(),
        passwordExpirationDays: passwordExpirationDays,
        passwordExpirationDate: passwordExpirationDate.toLocaleDateString()
      };

      // Debug logging
      console.log('Email template processing debug:', {
        userEmail,
        password: password,
        passwordLength: password?.length,
        containsTemplateSyntax: password?.includes('{{') || password?.includes('}}'),
        bodyTemplate: bodyTemplate?.substring(0, 100) + '...'
      });

      // Process both subject and body templates
      const processedSubject = this.processTemplate(subject, variables);
      const processedBody = this.processTemplate(bodyTemplate, variables);

      // Debug the processed content
      console.log('Template processing result:', {
        processedSubjectLength: processedSubject?.length,
        processedBodyContainsPassword: processedBody?.includes(password),
        processedBodyContainsTemplateSyntax: processedBody?.includes('{{password}}'),
        passwordInTemplate: processedBody?.includes(password) ? 'FOUND' : 'NOT_FOUND',
        passwordValue: password ? `[${password.length} chars]` : 'EMPTY'
      });

      // Additional debug: Check for malformed password patterns
      if (processedBody?.includes('< p="">') || processedBody?.includes('<p="">')) {
        console.error('🚨 DETECTED MALFORMED HTML IN EMAIL TEMPLATE!');
        console.error('Processed body preview:', processedBody?.substring(0, 500));
        console.error('Password value:', password);
      }

      return this.wrapInHTML(processedSubject, processedBody);

    } catch (error) {
      console.error('Error generating email template:', error);
      // Fallback to simple template
      return this.generateSimpleLoginEmail(userEmail, password, firstName, lastName);
    }
  }

  // Wrap processed content in HTML structure
  wrapInHTML(subject, processedBody) {
    // Convert to HTML (preserve line breaks and basic formatting)
    // Only escape HTML if it's not already properly formatted
    let htmlBody;
    if (processedBody.includes('<') || processedBody.includes('>')) {
      // If the body already contains HTML tags, be more careful with escaping
      htmlBody = processedBody
        .replace(/\n\n/g, '</p>\n<p>')
        .replace(/\n/g, '<br>')
        .replace(/•/g, '&bull;');
    } else {
      // If it's plain text, escape HTML entities first
      const escapedBody = processedBody
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');

      htmlBody = escapedBody
        .replace(/\n\n/g, '</p>\n<p>')
        .replace(/\n/g, '<br>')
        .replace(/•/g, '&bull;');
    }

    // Wrap in paragraph tags properly
    const wrappedHtmlBody = `<p>${htmlBody}</p>`;

    // Get logo options (CID for attachment, base64 as fallback)
    const logoBase64 = this.getTradeLinkLogoBase64();

    // Use CID reference for attachment (will be included as attachment)
    // If attachment fails, email clients may fall back to alt text
    const logoSrc = 'cid:tradelink-logo';

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .content { background-color: #ffffff; padding: 20px; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; text-align: center; }
        .logo-signature { margin-top: 20px; text-align: center; }
        .logo-signature img { max-width: 150px; height: auto; }
        p { margin-bottom: 15px; }
        strong { color: #2c3e50; }
    </style>
</head>
<body>
    <div class="content">
        ${wrappedHtmlBody}
    </div>

    <div class="footer">
        <div class="logo-signature">
            <img src="${logoSrc}" width="150" height="auto" alt="TradeLink Logo" onerror="this.style.display='none'" />
        </div>
    </div>
</body>
</html>
    `.trim();
  }

  // Fallback simple email template
  generateSimpleLoginEmail(userEmail, password, firstName = '', lastName = '') {
    // If names are not provided, try to extract from email
    let finalFirstName = firstName;
    let finalLastName = lastName;

    if (!firstName && !lastName) {
      const extracted = this.extractNameFromEmail(userEmail);
      finalFirstName = extracted.firstName;
      finalLastName = extracted.lastName;
    }

    const fullName = `${finalFirstName} ${finalLastName}`.trim() || 'Team Member';

    // Use CID reference for attachment
    const logoSrc = 'cid:tradelink-logo';

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Your TradeLink Training System Login Credentials</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .logo-signature { margin-top: 20px; text-align: center; }
        .logo-signature img { height: 50px; width: auto; max-width: 180px; }
    </style>
</head>
<body>
    <p>Dear ${fullName},</p>
    <p>Your account has been created for the TradeLink Internal Training System.</p>
    <p><strong>Email:</strong> ${userEmail}<br>
    <strong>Password:</strong> ${password}</p>
    <p>Please log in promptly to begin your training.</p>
    <p>Best regards,<br>TradeLink Training System Administration</p>
    <div class="logo-signature">
        <img src="${logoSrc}" alt="TradeLink Logo" style="height: 50px; width: auto; max-width: 180px;" onerror="this.style.display='none'" />
    </div>
</body>
</html>
    `.trim();
  }

  // Get default email template
  getDefaultEmailTemplate(language = 'en') {
    if (language === 'zh') {
      return `亲爱的 {{fullName}}，

您的TradeLink内部培训系统账户已创建。

登录凭据：
邮箱：{{email}}
密码：{{password}}

重要安全信息：
• 您的密码将在8天后过期
• 请及时登录开始您的培训
• 请保护您的凭据安全，不要与他人分享
• 如需帮助请联系您的管理员

如果您有任何问题或需要帮助，请联系您的系统管理员。

此致，
TradeLink培训系统管理员

---
这是一封自动发送的邮件，请勿回复。`;
    }

    // Default English template
    return `Dear {{fullName}},

Your account has been created for the TradeLink Internal Training System.

Login Credentials:
Email: {{email}}
Password: {{password}}

Important Security Information:
• Your password will expire in 8 days
• Please log in promptly to begin your training
• Keep your credentials secure and do not share them
• Contact your administrator if you need assistance

If you have any questions or need assistance, please contact your system administrator.

Best regards,
TradeLink Training System Administration

---
This is an automated message. Please do not reply to this email.`;
  }
}

module.exports = EmailTemplateService;
