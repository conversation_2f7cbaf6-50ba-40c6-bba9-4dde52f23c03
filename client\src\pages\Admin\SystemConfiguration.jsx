import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  InputNumber,
  Button,
  message,
  Typography,
  Alert,
  Space,
  Divider,
  Tooltip,
  Select,
  Input
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  CloudUploadOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import systemConfigAPI from '../../services/systemConfigAPI';
import { handleAPIError, trainingAPI } from '../../services/api';

const { Title, Text } = Typography;

const SystemConfiguration = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [hasChanges, setHasChanges] = useState(false);

  // Fetch system configuration
  const { data: configData, isLoading, error, refetch } = useQuery({
    queryKey: ['system-config'],
    queryFn: systemConfigAPI.getSystemConfig,
    select: (response) => response.data.data,
    onSuccess: (data) => {
      form.setFieldsValue(data);
      setHasChanges(false);
    }
  });

  // Update system configuration mutation
  const updateConfigMutation = useMutation(
    systemConfigAPI.updateSystemConfig,
    {
      onSuccess: (response) => {
        message.success(t('systemConfig.updateSuccess'));
        queryClient.invalidateQueries('system-config');
        queryClient.invalidateQueries('users'); // Refresh user list to show updated password status
        setHasChanges(false);
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error);
        message.error(`${t('systemConfig.updateFailed')}: ${errorInfo.message}`);
      }
    }
  );

  // Cleanup orphaned chunks mutation
  const cleanupChunksMutation = useMutation(
    () => trainingAPI.cleanupOrphanedChunks(),
    {
      onSuccess: (response) => {
        const { cleaned, errors } = response.data;
        if (cleaned > 0) {
          message.success(`Cleaned up ${cleaned} orphaned chunk directories`);
        } else {
          message.info('No orphaned chunks found to clean up');
        }
        if (errors > 0) {
          message.warning(`${errors} errors occurred during cleanup`);
        }
      },
      onError: (error) => {
        const errorInfo = handleAPIError(error);
        message.error(`Failed to cleanup orphaned chunks: ${errorInfo.message}`);
      }
    }
  );

  // Handle form submission
  const handleSubmit = (values) => {
    updateConfigMutation.mutate(values);
  };

  // Handle form value changes
  const handleFormChange = () => {
    setHasChanges(true);
  };

  // Handle refresh
  const handleRefresh = () => {
    refetch();
    setHasChanges(false);
  };

  // Handle cleanup orphaned chunks
  const handleCleanupChunks = () => {
    cleanupChunksMutation.mutate();
  };

  if (error) {
    return (
      <Alert
        message={t('systemConfig.failedToLoadConfiguration')}
        description={error.message}
        type="error"
        showIcon
      />
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>
          <ClockCircleOutlined style={{ marginRight: 8 }} />
          {t('systemConfig.title')}
        </Title>
        <Button
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={isLoading}
        >
          {t('common.refresh')}
        </Button>
      </div>

      <Card loading={isLoading}>
        <Alert
          message={t('systemConfig.configurationNote')}
          description={t('systemConfig.configurationDescription')}
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onValuesChange={handleFormChange}
          initialValues={configData}
        >
          <Title level={4}>{t('systemConfig.passwordSettings')}</Title>
          
          <Form.Item
            name="password_expiration_days"
            label={
              <Space>
                {t('systemConfig.passwordExpirationDays')}
                <Tooltip title={t('systemConfig.passwordExpirationTooltip')}>
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              { required: true, message: t('systemConfig.passwordExpirationRequired') },
              { type: 'number', min: 1, max: 365, message: t('systemConfig.passwordExpirationRange') }
            ]}
            extra={t('systemConfig.passwordExpirationExtra')}
          >
            <InputNumber
              min={1}
              max={365}
              style={{ width: 200 }}
              addonAfter={t('systemConfig.days')}
              placeholder={t('systemConfig.enterDays')}
            />
          </Form.Item>

          <Title level={4} style={{ marginTop: 24 }}>{t('systemConfig.systemSettings')}</Title>

          <Form.Item
            name="timezone"
            label={
              <Space>
                {t('systemConfig.timezone')}
                <Tooltip title={t('systemConfig.timezoneTooltip')}>
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              { required: true, message: t('systemConfig.timezoneRequired') }
            ]}
            extra={t('systemConfig.timezoneExtra')}
          >
            <Select
              style={{ width: 300 }}
              placeholder={t('systemConfig.selectTimezone')}
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              <Select.Option value="Asia/Shanghai">Asia/Shanghai (UTC+08:00)</Select.Option>
              <Select.Option value="Asia/Hong_Kong">Asia/Hong_Kong (UTC+08:00)</Select.Option>
              <Select.Option value="Asia/Taipei">Asia/Taipei (UTC+08:00)</Select.Option>
              <Select.Option value="Asia/Singapore">Asia/Singapore (UTC+08:00)</Select.Option>
              <Select.Option value="Asia/Tokyo">Asia/Tokyo (UTC+09:00)</Select.Option>
              <Select.Option value="Asia/Seoul">Asia/Seoul (UTC+09:00)</Select.Option>
              <Select.Option value="UTC">UTC (UTC+00:00)</Select.Option>
              <Select.Option value="America/New_York">America/New_York (UTC-05:00)</Select.Option>
              <Select.Option value="America/Los_Angeles">America/Los_Angeles (UTC-08:00)</Select.Option>
              <Select.Option value="Europe/London">Europe/London (UTC+00:00)</Select.Option>
              <Select.Option value="Europe/Paris">Europe/Paris (UTC+01:00)</Select.Option>
              <Select.Option value="Europe/Berlin">Europe/Berlin (UTC+01:00)</Select.Option>
              <Select.Option value="Australia/Sydney">Australia/Sydney (UTC+11:00)</Select.Option>
              <Select.Option value="Australia/Melbourne">Australia/Melbourne (UTC+11:00)</Select.Option>
            </Select>
          </Form.Item>

          <Title level={4} style={{ marginTop: 24 }}>{t('systemConfig.fileUploadSettings')}</Title>

          <Form.Item
            name="max_file_size"
            label={
              <Space>
                <CloudUploadOutlined />
                {t('systemConfig.maxFileSize')}
                <Tooltip title={t('systemConfig.maxFileSizeTooltip')}>
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              { required: true, message: t('systemConfig.maxFileSizeRequired') },
              {
                pattern: /^\d+(\.\d+)?\s*(B|KB|MB|GB|TB)$/i,
                message: t('systemConfig.maxFileSizeInvalid')
              }
            ]}
            extra={t('systemConfig.maxFileSizeExtra')}
          >
            <Input
              style={{ width: 200 }}
              placeholder={t('systemConfig.enterFileSize')}
              addonBefore={<CloudUploadOutlined />}
            />
          </Form.Item>

          <Title level={4} style={{ marginTop: 24 }}>System Maintenance</Title>

          <Form.Item
            label={
              <Space>
                <DeleteOutlined />
                Cleanup Orphaned Upload Chunks
                <Tooltip title="Remove abandoned upload chunks from cancelled or failed uploads">
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            extra="This will clean up chunk files from cancelled or failed uploads that are older than 1 hour"
          >
            <Button
              type="default"
              icon={<DeleteOutlined />}
              onClick={handleCleanupChunks}
              loading={cleanupChunksMutation.isLoading}
            >
              Cleanup Orphaned Chunks
            </Button>
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={updateConfigMutation.isLoading}
                disabled={!hasChanges}
              >
                {t('common.save')}
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  setHasChanges(false);
                }}
                disabled={!hasChanges}
              >
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>

        {configData && (
          <>
            <Divider />
            <div style={{ background: '#fafafa', padding: 16, borderRadius: 6 }}>
              <Title level={5}>{t('systemConfig.currentSettings')}</Title>
              <Space direction="vertical" size="small">
                <Text>
                  <strong>{t('systemConfig.passwordExpirationDays')}:</strong> {configData.password_expiration_days} {t('systemConfig.days')}
                </Text>
                <Text>
                  <strong>{t('systemConfig.timezone')}:</strong> {configData.timezone || 'Asia/Shanghai'}
                </Text>
                <Text>
                  <strong>{t('systemConfig.maxFileSize')}:</strong> {configData.max_file_size || '500MB'}
                </Text>
                <Text type="secondary">
                  <strong>{t('systemConfig.lastUpdated')}:</strong> {new Date(configData.updated_at).toLocaleString()}
                </Text>
              </Space>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default SystemConfiguration;
