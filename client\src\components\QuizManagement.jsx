import React, { useState, useEffect } from 'react'
import { Modal, Form, Input, Select, Space, Radio, Checkbox, Button, Card, Divider, message, Typography, Switch, Upload } from 'antd'
import { PlusOutlined, DeleteOutlined, QuestionCircleOutlined, UploadOutlined, EyeOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

// Helper function to convert file to base64 with proper data URL format
const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      // FileReader.readAsDataURL already returns the full data URL format
      // e.g., "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
      resolve(reader.result)
    }
    reader.onerror = error => reject(error)
  })
}

// Helper function to ensure base64 data has proper data URL format
const ensureDataURL = (base64Data) => {
  if (!base64Data) return ''

  // If it already starts with data:, return as is
  if (base64Data.startsWith('data:')) {
    return base64Data
  }

  // If it's raw base64, add the data URL prefix
  // Default to PNG if we can't determine the type
  return `data:image/png;base64,${base64Data}`
}

// Helper function to open image in new tab (works around browser security restrictions)
const openImageInNewTab = (base64Data) => {
  const dataUrl = ensureDataURL(base64Data)

  // Create a new window with the image
  const newWindow = window.open('', '_blank')
  if (newWindow) {
    newWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Image Viewer</title>
          <style>
            body { margin: 0; padding: 20px; background: #f0f0f0; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
            img { max-width: 100%; max-height: 100%; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
          </style>
        </head>
        <body>
          <img src="${dataUrl}" alt="Image" />
        </body>
      </html>
    `)
    newWindow.document.close()
  }
}

// Helper function to handle image paste
const handleImagePaste = async (e, onImageSet) => {
  const items = e.clipboardData?.items
  if (!items) return

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.type.indexOf('image') !== -1) {
      e.preventDefault()
      const file = item.getAsFile()
      if (file) {
        try {
          const base64 = await fileToBase64(file)
          onImageSet(base64)
        } catch (error) {
          console.error('Error converting image to base64:', error)
        }
      }
      break
    }
  }
}

function QuizManagement({
  visible,
  onCancel,
  onSubmit,
  mode,
  video,
  initialData,
  loading
}) {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const [questions, setQuestions] = useState([])

  useEffect(() => {
    if (visible && mode === 'edit' && initialData) {
      // Populate form with existing quiz data
      form.setFieldsValue({
        title: initialData.title,
        description: initialData.description,
        passingScore: initialData.passingScore || initialData.passing_score,
        timeLimitMinutes: initialData.timeLimitMinutes || initialData.time_limit_minutes
      })



      // Set questions if available
      if (initialData.questions) {
        setQuestions(initialData.questions.map(q => {
          const questionType = q.question_type || q.questionType;
          return {
            id: q.id || Date.now() + Math.random(),
            questionText: q.question_text || q.questionText,
            questionType: questionType,
            points: questionType === 'short_answer' ? 0 : q.points, // Ensure short answer questions have 0 points
            questionImage: q.question_image || q.questionImage || '',
            imageModeEnabled: !!(q.question_image || q.questionImage || (q.options && q.options.some(o => o.option_image || o.optionImage))),
            options: q.options ? q.options.map(o => ({
              id: o.id || Date.now() + Math.random(),
              optionText: o.option_text || o.optionText,
              isCorrect: o.is_correct || o.isCorrect,
              optionImage: o.option_image || o.optionImage || ''
            })) : []
          };
        }))
      }
    } else if (visible && mode === 'create') {
      form.resetFields()
      setQuestions([])
    }
  }, [visible, mode, initialData, form])

  const addQuestion = () => {
    const newQuestion = {
      id: Date.now(),
      questionText: '',
      questionType: 'multiple_choice',
      points: 1,
      questionImage: '',
      imageModeEnabled: false,
      options: [
        { id: Date.now() + 1, optionText: '', isCorrect: false, optionImage: '' },
        { id: Date.now() + 2, optionText: '', isCorrect: false, optionImage: '' }
      ]
    }
    setQuestions([...questions, newQuestion])
  }

  const removeQuestion = (questionId) => {
    setQuestions(questions.filter(q => q.id !== questionId))
  }

  const updateQuestion = (questionId, field, value) => {
    setQuestions(questions.map(q => 
      q.id === questionId ? { ...q, [field]: value } : q
    ))
  }

  const updateQuestionType = (questionId, type) => {
    setQuestions(questions.map(q => {
      if (q.id === questionId) {
        let newOptions = []
        let newPoints = q.points

        if (type === 'true_false') {
          newOptions = [
            { id: Date.now() + 1, optionText: 'True', isCorrect: false, optionImage: '' },
            { id: Date.now() + 2, optionText: 'False', isCorrect: false, optionImage: '' }
          ]
        } else if (type === 'multiple_choice' || type === 'multi_choice') {
          newOptions = [
            { id: Date.now() + 1, optionText: '', isCorrect: false, optionImage: '' },
            { id: Date.now() + 2, optionText: '', isCorrect: false, optionImage: '' }
          ]
        } else if (type === 'short_answer') {
          // Short answer questions have fixed 0 points
          newPoints = 0
          newOptions = []
        }

        return { ...q, questionType: type, options: newOptions, points: newPoints }
      }
      return q
    }))
  }

  const addOption = (questionId) => {
    setQuestions(questions.map(q => {
      if (q.id === questionId) {
        const newOption = { id: Date.now(), optionText: '', isCorrect: false, optionImage: '' }
        return { ...q, options: [...q.options, newOption] }
      }
      return q
    }))
  }

  const removeOption = (questionId, optionId) => {
    setQuestions(questions.map(q => {
      if (q.id === questionId) {
        return { ...q, options: q.options.filter(o => o.id !== optionId) }
      }
      return q
    }))
  }

  const updateOption = (questionId, optionId, field, value) => {
    setQuestions(questions.map(q => {
      if (q.id === questionId) {
        return {
          ...q,
          options: q.options.map(o => 
            o.id === optionId ? { ...o, [field]: value } : o
          )
        }
      }
      return q
    }))
  }

  const setCorrectOption = (questionId, optionId) => {
    setQuestions(questions.map(q => {
      if (q.id === questionId) {
        if (q.questionType === 'multi_choice') {
          // For multi-choice, toggle the option
          return {
            ...q,
            options: q.options.map(o => ({
              ...o,
              isCorrect: o.id === optionId ? !o.isCorrect : o.isCorrect
            }))
          }
        } else {
          // For single choice (multiple_choice, true_false), only one can be correct
          return {
            ...q,
            options: q.options.map(o => ({
              ...o,
              isCorrect: o.id === optionId
            }))
          }
        }
      }
      return q
    }))
  }

  const handleSubmit = (values) => {
    console.log('=== QUIZ FORM SUBMIT DEBUG ===')
    console.log('Form values:', values)
    console.log('Questions state:', questions)

    if (questions.length === 0) {
      message.error(t('messages.validationError'))
      return
    }

    // Validate questions
    for (const question of questions) {
      if (!question.questionText.trim()) {
        message.error(t('messages.validationError'))
        return
      }

      if (question.questionType !== 'short_answer') {
        if (question.options.length < 2) {
          message.error(t('messages.validationError'))
          return
        }

        const hasCorrectAnswer = question.options.some(o => o.isCorrect)
        if (!hasCorrectAnswer) {
          message.error(t('messages.validationError'))
          return
        }

        // For multi_choice questions, ensure at least one correct answer
        if (question.questionType === 'multi_choice') {
          const correctCount = question.options.filter(o => o.isCorrect).length
          if (correctCount === 0) {
            message.error(t('quiz.multiChoiceValidationError'))
            return
          }
        }

        for (const option of question.options) {
          if (!option.optionText.trim()) {
            message.error(t('messages.validationError'))
            return
          }
        }
      }
    }

    const quizData = {
      ...values,
      questions: questions.map(q => ({
        questionText: q.questionText,
        questionType: q.questionType,
        points: q.questionType === 'short_answer' ? 0 : q.points, // Ensure short answer questions have 0 points
        questionImage: q.imageModeEnabled ? (q.questionImage || '') : '',
        options: q.questionType === 'short_answer' ? [] : q.options.map(o => ({
          optionText: o.optionText,
          isCorrect: o.isCorrect,
          optionImage: q.imageModeEnabled ? (o.optionImage || '') : ''
        }))
      }))
    }

    console.log('Final quiz data being submitted:', quizData)
    onSubmit(quizData)
  }

  return (
    <Modal
      title={`${mode === 'create' ? t('quiz.createQuizTitle') : t('quiz.editQuizTitle')} - ${video?.language_name || 'Video'}`}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      style={{ top: 20 }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="title"
          label={t('quiz.quizTitle')}
          rules={[{ required: true, message: t('quiz.pleaseEnterQuizTitle') }]}
        >
          <Input placeholder={t('quiz.enterQuizTitle')} />
        </Form.Item>

        <Form.Item
          name="description"
          label={t('quiz.description')}
        >
          <TextArea rows={3} placeholder={t('quiz.enterQuizDescription')} />
        </Form.Item>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 24 }}>
          <Form.Item
            name="passingScore"
            label={t('quiz.passingScore')}
            initialValue={70}
            rules={[{ required: true, message: t('validation.required') }]}
          >
            <Input type="number" min={1} max={100} />
          </Form.Item>

          <Form.Item
            name="timeLimitMinutes"
            label={t('quiz.timeLimit')}
          >
            <Input type="number" min={1} placeholder={t('quiz.timeLimit')} />
          </Form.Item>
        </div>



        <Divider />

        <div style={{ marginBottom: 16 }}>
          <Title level={4}>{t('quiz.questions')}</Title>
        </div>

        {questions.map((question, index) => (
          <Card
            key={question.id}
            size="small"
            style={{ marginBottom: 16 }}
            title={t('quiz.questionNumber', { number: index + 1 })}
            extra={
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => removeQuestion(question.id)}
              />
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Input
                placeholder={t('quiz.enterQuestionText')}
                value={question.questionText}
                onChange={(e) => updateQuestion(question.id, 'questionText', e.target.value)}
              />

              {/* Per-Question Image Mode Toggle */}
              <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
                <Text style={{ fontSize: 12, color: '#666' }}>{t('quiz.toggleImageMode')}:</Text>
                <Switch
                  size="small"
                  checked={question.imageModeEnabled}
                  onChange={(checked) => updateQuestion(question.id, 'imageModeEnabled', checked)}
                  checkedChildren={t('quiz.imageModeOn')}
                  unCheckedChildren={t('quiz.imageModeOff')}
                />
              </div>

              {/* Question Image Upload - only show if image mode is enabled */}
              {question.imageModeEnabled && (
                <div>
                  <Text strong style={{ display: 'block', marginBottom: 8 }}>
                    {t('quiz.questionImageOptional')}
                  </Text>
                  {question.questionImage ? (
                    <div style={{ position: 'relative', display: 'inline-block' }}>
                      <img
                        src={ensureDataURL(question.questionImage)}
                        alt="Question"
                        style={{
                          maxWidth: '100%',
                          maxHeight: 200,
                          border: '1px solid #d9d9d9',
                          borderRadius: 4
                        }}
                      />
                      <div style={{ marginTop: 8 }}>
                        <Button
                          size="small"
                          danger
                          onClick={() => updateQuestion(question.id, 'questionImage', '')}
                        >
                          {t('quiz.deleteImage')}
                        </Button>
                        <Button
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={() => openImageInNewTab(question.questionImage)}
                          style={{ marginLeft: 8 }}
                        >
                          {t('quiz.openImageInNewTab')}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div
                      style={{
                        border: '2px dashed #d9d9d9',
                        borderRadius: 4,
                        padding: 20,
                        textAlign: 'center',
                        cursor: 'pointer',
                        backgroundColor: '#fafafa'
                      }}
                      onDrop={async (e) => {
                        e.preventDefault()
                        const files = e.dataTransfer.files
                        if (files.length > 0 && files[0].type.startsWith('image/')) {
                          try {
                            const base64 = await fileToBase64(files[0])
                            updateQuestion(question.id, 'questionImage', base64)
                          } catch (error) {
                            message.error('Failed to process image')
                          }
                        }
                      }}
                      onDragOver={(e) => e.preventDefault()}
                      onPaste={async (e) => {
                        await handleImagePaste(e, (base64) => {
                          updateQuestion(question.id, 'questionImage', base64)
                        })
                      }}
                      onClick={() => {
                        const input = document.createElement('input')
                        input.type = 'file'
                        input.accept = 'image/*'
                        input.onchange = async (e) => {
                          const file = e.target.files[0]
                          if (file) {
                            try {
                              const base64 = await fileToBase64(file)
                              updateQuestion(question.id, 'questionImage', base64)
                            } catch (error) {
                              message.error('Failed to process image')
                            }
                          }
                        }
                        input.click()
                      }}
                    >
                      <UploadOutlined style={{ fontSize: 24, color: '#d9d9d9', marginBottom: 8 }} />
                      <div>{t('quiz.dragDropOrPasteImage')}</div>
                    </div>
                  )}
                </div>
              )}

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
                <Select
                  value={question.questionType}
                  onChange={(value) => updateQuestionType(question.id, value)}
                >
                  <Option value="multiple_choice">{t('quiz.multipleChoice')}</Option>
                  <Option value="multi_choice">{t('quiz.multiChoice')}</Option>
                  <Option value="true_false">{t('quiz.trueFalse')}</Option>
                  <Option value="short_answer">{t('quiz.shortAnswer')}</Option>
                </Select>

                <Input
                  type="number"
                  min={question.questionType === 'short_answer' ? 0 : 1}
                  max={question.questionType === 'short_answer' ? 0 : undefined}
                  value={question.points}
                  onChange={(e) => {
                    // Prevent changing points for short answer questions
                    if (question.questionType !== 'short_answer') {
                      updateQuestion(question.id, 'points', parseInt(e.target.value))
                    }
                  }}
                  addonBefore={t('quiz.points')}
                  disabled={question.questionType === 'short_answer'}
                  style={{
                    backgroundColor: question.questionType === 'short_answer' ? '#f5f5f5' : undefined
                  }}
                />
              </div>

              {question.questionType === 'short_answer' ? (
                <div style={{
                  background: '#fff7e6',
                  border: '1px solid #ffd591',
                  borderRadius: 4,
                  padding: 12,
                  fontSize: 12,
                  color: '#d46b08'
                }}>
                  {t('quiz.shortAnswerWarning')}
                </div>
              ) : (
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                    <Text strong>{t('quiz.options')}:</Text>
                    {(question.questionType === 'multiple_choice' || question.questionType === 'multi_choice') && (
                      <Button
                        size="small"
                        type="dashed"
                        onClick={() => addOption(question.id)}
                      >
                        {t('quiz.addOption')}
                      </Button>
                    )}
                  </div>

                  {question.options.map((option) => (
                    <div key={option.id} style={{ marginBottom: 16 }}>
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                        {question.questionType === 'multi_choice' ? (
                          <Checkbox
                            checked={option.isCorrect}
                            onChange={() => setCorrectOption(question.id, option.id)}
                            style={{ marginRight: 8 }}
                          />
                        ) : (
                          <Radio
                            checked={option.isCorrect}
                            onChange={() => setCorrectOption(question.id, option.id)}
                            style={{ marginRight: 8 }}
                          />
                        )}
                        <Input
                          placeholder={t('quiz.enterOptionText')}
                          value={option.optionText}
                          onChange={(e) => updateOption(question.id, option.id, 'optionText', e.target.value)}
                          style={{ flex: 1, marginRight: 8 }}
                          disabled={question.questionType === 'true_false'}
                        />
                        {(question.questionType === 'multiple_choice' || question.questionType === 'multi_choice') && question.options.length > 2 && (
                          <Button
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => removeOption(question.id, option.id)}
                          />
                        )}
                      </div>

                      {/* Option Image Upload - only show if image mode is enabled */}
                      {question.imageModeEnabled && (
                        <div style={{ marginLeft: 32 }}>
                          <Text style={{ fontSize: 12, color: '#666', display: 'block', marginBottom: 4 }}>
                            {t('quiz.optionImageOptional')}
                          </Text>
                          {option.optionImage ? (
                            <div style={{ position: 'relative', display: 'inline-block' }}>
                              <img
                                src={ensureDataURL(option.optionImage)}
                                alt="Option"
                                style={{
                                  maxWidth: 150,
                                  maxHeight: 50,
                                  border: '1px solid #d9d9d9',
                                  borderRadius: 4
                                }}
                              />
                              <div style={{ marginTop: 4 }}>
                                <Button
                                  size="small"
                                  danger
                                  onClick={() => updateOption(question.id, option.id, 'optionImage', '')}
                                  style={{ fontSize: 10, height: 20 }}
                                >
                                  {t('quiz.deleteImage')}
                                </Button>
                                <Button
                                  size="small"
                                  icon={<EyeOutlined />}
                                  onClick={() => openImageInNewTab(option.optionImage)}
                                  style={{ marginLeft: 4, fontSize: 10, height: 20 }}
                                >
                                  {t('quiz.openImageInNewTab')}
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div
                              style={{
                                border: '1px dashed #d9d9d9',
                                borderRadius: 4,
                                padding: 8,
                                textAlign: 'center',
                                cursor: 'pointer',
                                backgroundColor: '#fafafa',
                                fontSize: 11,
                                maxWidth: 150
                              }}
                              onDrop={async (e) => {
                                e.preventDefault()
                                const files = e.dataTransfer.files
                                if (files.length > 0 && files[0].type.startsWith('image/')) {
                                  try {
                                    const base64 = await fileToBase64(files[0])
                                    updateOption(question.id, option.id, 'optionImage', base64)
                                  } catch (error) {
                                    message.error('Failed to process image')
                                  }
                                }
                              }}
                              onDragOver={(e) => e.preventDefault()}
                              onPaste={async (e) => {
                                await handleImagePaste(e, (base64) => {
                                  updateOption(question.id, option.id, 'optionImage', base64)
                                })
                              }}
                              onClick={() => {
                                const input = document.createElement('input')
                                input.type = 'file'
                                input.accept = 'image/*'
                                input.onchange = async (e) => {
                                  const file = e.target.files[0]
                                  if (file) {
                                    try {
                                      const base64 = await fileToBase64(file)
                                      updateOption(question.id, option.id, 'optionImage', base64)
                                    } catch (error) {
                                      message.error('Failed to process image')
                                    }
                                  }
                                }
                                input.click()
                              }}
                            >
                              <UploadOutlined style={{ fontSize: 12, color: '#d9d9d9' }} />
                              <div style={{ fontSize: 10 }}>{t('quiz.dragDropOrPasteImage')}</div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Space>
          </Card>
        ))}

        {questions.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: 40,
            background: '#fafafa',
            borderRadius: 6,
            marginBottom: 24
          }}>
            <QuestionCircleOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
            <Title level={4} type="secondary">No Questions Added</Title>
            <Text type="secondary" style={{ display: 'block', marginBottom: 20 }}>
              Start creating your quiz by adding your first question
            </Text>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={addQuestion}
              size="large"
            >
              {t('quiz.addQuestion')}
            </Button>
          </div>
        )}

        {/* Add Question button - positioned at the bottom for better UX */}
        {questions.length > 0 && (
          <div style={{ textAlign: 'center', marginBottom: 32 }}>
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={addQuestion}
              size="large"
              style={{
                minWidth: 200,
                height: 48,
                borderStyle: 'dashed',
                borderWidth: 2
              }}
            >
              {t('quiz.addQuestion')}
            </Button>
          </div>
        )}

        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={questions.length === 0}
            >
              {mode === 'create' ? t('quiz.createQuizButton') : t('quiz.updateQuizButton')}
            </Button>
            <Button onClick={onCancel}>
              {t('common.cancel')}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default QuizManagement
