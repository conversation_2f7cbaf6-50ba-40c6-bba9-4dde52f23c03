/**
 * Performance utilities for React components
 */

import { useCallback, useMemo } from 'react'

/**
 * Debounce hook for performance optimization
 * @param {Function} callback - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @param {Array} deps - Dependencies array
 * @returns {Function} Debounced function
 */
export const useDebounce = (callback, delay, deps = []) => {
  return useCallback(
    debounce(callback, delay),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    deps
  )
}

/**
 * Throttle hook for performance optimization
 * @param {Function} callback - Function to throttle
 * @param {number} delay - Delay in milliseconds
 * @param {Array} deps - Dependencies array
 * @returns {Function} Throttled function
 */
export const useThrottle = (callback, delay, deps = []) => {
  return useCallback(
    throttle(callback, delay),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    deps
  )
}

/**
 * Memoized calculation hook
 * @param {Function} calculation - Function to calculate value
 * @param {Array} deps - Dependencies array
 * @returns {*} Calculated value
 */
export const useCalculation = (calculation, deps) => {
  return useMemo(calculation, deps)
}

/**
 * Debounce function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * Throttle function
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * Check if code is running in development mode
 * @returns {boolean} True if in development mode
 */
export const isDevelopment = () => {
  return process.env.NODE_ENV === 'development'
}

/**
 * Conditional console logging (only in development)
 * @param {...any} args - Arguments to log
 */
export const devLog = (...args) => {
  if (isDevelopment()) {
    console.log(...args)
  }
}

/**
 * Conditional console error logging (only in development)
 * @param {...any} args - Arguments to log
 */
export const devError = (...args) => {
  if (isDevelopment()) {
    console.error(...args)
  }
}

/**
 * Conditional console warning logging (only in development)
 * @param {...any} args - Arguments to log
 */
export const devWarn = (...args) => {
  if (isDevelopment()) {
    console.warn(...args)
  }
}

/**
 * Create a stable reference for objects/arrays to prevent unnecessary re-renders
 * @param {*} value - Value to stabilize
 * @param {Array} deps - Dependencies array
 * @returns {*} Stable reference
 */
export const useStableReference = (value, deps) => {
  return useMemo(() => value, deps)
}

/**
 * Optimize event handlers to prevent recreation on every render
 * @param {Function} handler - Event handler function
 * @param {Array} deps - Dependencies array
 * @returns {Function} Optimized event handler
 */
export const useOptimizedHandler = (handler, deps = []) => {
  return useCallback(handler, deps)
}

/**
 * Validate numeric ID and convert to number
 * @param {*} id - ID to validate
 * @returns {number} Validated numeric ID
 * @throws {Error} If ID is invalid
 */
export const validateNumericId = (id) => {
  const numericId = Number(id)
  if (isNaN(numericId)) {
    throw new Error('Invalid ID: must be a number')
  }
  return numericId
}

/**
 * Format time in YouTube style (MM:SS or HH:MM:SS)
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
export const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds) || seconds < 0) return '0:00'

  const totalSeconds = Math.floor(seconds)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const secs = totalSeconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

/**
 * Create optimized mutation options with common patterns
 * @param {Object} options - Mutation options
 * @param {Function} options.onSuccess - Success callback
 * @param {Function} options.onError - Error callback
 * @param {string} options.successMessage - Success message
 * @param {string} options.errorMessage - Error message prefix
 * @param {Function} options.invalidateQueries - Function to invalidate queries
 * @returns {Object} Optimized mutation options
 */
export const createMutationOptions = ({
  onSuccess,
  onError,
  successMessage,
  errorMessage,
  invalidateQueries
}) => ({
  onSuccess: (data, variables, context) => {
    if (invalidateQueries) {
      invalidateQueries()
    }
    if (successMessage) {
      // Import message dynamically to avoid dependency issues
      import('antd').then(({ message }) => {
        message.success(successMessage)
      }).catch(() => {
        console.log(successMessage)
      })
    }
    if (onSuccess) {
      onSuccess(data, variables, context)
    }
  },
  onError: (error, variables, context) => {
    const errorInfo = error.response?.data?.message || error.message || 'An error occurred'
    if (errorMessage) {
      import('antd').then(({ message }) => {
        message.error(`${errorMessage}: ${errorInfo}`)
      }).catch(() => {
        console.error(`${errorMessage}: ${errorInfo}`)
      })
    }
    if (onError) {
      onError(error, variables, context)
    }
  }
})

/**
 * Create a cleanup function for useEffect hooks
 * @param {Function[]} cleanupFunctions - Array of cleanup functions
 * @returns {Function} Combined cleanup function
 */
export const createCleanup = (...cleanupFunctions) => {
  return () => {
    cleanupFunctions.forEach(cleanup => {
      if (typeof cleanup === 'function') {
        try {
          cleanup()
        } catch (error) {
          devError('Cleanup function failed:', error)
        }
      }
    })
  }
}

/**
 * Safely get nested object property
 * @param {Object} obj - Object to get property from
 * @param {string} path - Dot-separated path to property
 * @param {*} defaultValue - Default value if property doesn't exist
 * @returns {*} Property value or default value
 */
export const safeGet = (obj, path, defaultValue = null) => {
  try {
    return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue
  } catch {
    return defaultValue
  }
}
