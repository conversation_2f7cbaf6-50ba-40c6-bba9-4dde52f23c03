import React, { useState } from 'react'
import { <PERSON>, But<PERSON>, Typo<PERSON>, DatePicker, Select, Space, Table, Spin, Alert, message, Tabs } from 'antd'
import { DownloadOutlined, FileTextOutlined, BarChartOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import { reportsAPI, trainingAPI, handleAPIError, downloadFile } from '../../services/api'
import LoadingImage from '../../components/LoadingImage'
import QuestionStatistics from '../../components/QuestionStatistics'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

function Reports() {
  const { t } = useTranslation()
  const [filters, setFilters] = useState({
    moduleId: null,
    startDate: null,
    endDate: null,
    userEmail: null
  })
  const [downloadingReport, setDownloadingReport] = useState(false)

  // Fetch training modules for filter
  const { data: modulesData } = useQuery(
    'training-modules',
    trainingAPI.getModules,
    {
      select: (response) => response.data.modules,
    }
  )

  // Fetch completion statistics
  const { data: statsData, isLoading: statsLoading, error: statsError } = useQuery(
    ['completion-stats', filters.moduleId],
    () => reportsAPI.getCompletionStats({ moduleId: filters.moduleId }),
    {
      select: (response) => response.data.stats,
    }
  )

  // Fetch user activity
  const { data: activityData, isLoading: activityLoading, error: activityError } = useQuery(
    ['user-activity', filters],
    () => reportsAPI.getUserActivity({
      startDate: filters.startDate,
      endDate: filters.endDate,
      userEmail: filters.userEmail
    }),
    {
      select: (response) => response.data.activity,
      enabled: !!(filters.startDate || filters.endDate || filters.userEmail)
    }
  )

  const handleDateRangeChange = (dates) => {
    setFilters(prev => ({
      ...prev,
      startDate: dates?.[0]?.format('YYYY-MM-DD'),
      endDate: dates?.[1]?.format('YYYY-MM-DD')
    }))
  }

  const handleDownloadComplianceReport = async () => {
    try {
      setDownloadingReport(true)
      const response = await reportsAPI.downloadComplianceReport(filters)
      const filename = `Training_Compliance_Report_${dayjs().format('YYYY-MM-DD')}.html`
      downloadFile(response.data, filename)
      message.success(t('reports.complianceReportDownloaded'))
    } catch (error) {
      const errorInfo = handleAPIError(error)
      message.error(`${t('reports.failedToDownloadReport')}: ${errorInfo.message}`)
    } finally {
      setDownloadingReport(false)
    }
  }

  const statsColumns = [
    {
      title: t('reports.module'),
      dataIndex: 'module_title',
      key: 'module_title',
      render: (title) => <Text strong>{title}</Text>
    },
    {
      title: t('reports.languages'),
      dataIndex: 'available_languages',
      key: 'available_languages',
      align: 'center'
    },
    {
      title: t('reports.totalUsers'),
      dataIndex: 'total_enrolled_users',
      key: 'total_enrolled_users',
      align: 'center'
    },
    {
      title: t('reports.videosWatched'),
      dataIndex: 'users_watched_video',
      key: 'users_watched_video',
      align: 'center'
    },
    {
      title: t('reports.quizAttempts'),
      dataIndex: 'users_attempted_quiz',
      key: 'users_attempted_quiz',
      align: 'center'
    },
    {
      title: t('reports.quizPassed'),
      dataIndex: 'users_passed_quiz',
      key: 'users_passed_quiz',
      align: 'center',
      render: (passed, record) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          {passed} ({record.total_enrolled_users > 0 ? Math.round((passed / record.total_enrolled_users) * 100) : 0}%)
        </span>
      )
    },
    {
      title: t('reports.certificates'),
      dataIndex: 'certificates_issued',
      key: 'certificates_issued',
      align: 'center',
      render: (count) => (
        <span style={{ color: '#faad14', fontWeight: 'bold' }}>{count}</span>
      )
    },
    {
      title: t('reports.avgScore'),
      dataIndex: 'average_score',
      key: 'average_score',
      align: 'center',
      render: (score) => score ? `${score}%` : '-'
    }
  ]

  const activityColumns = [
    {
      title: t('reports.user'),
      key: 'user',
      render: (_, record) => (
        <div>
          <Text strong>{record.email}</Text>
          {(record.first_name || record.last_name) && (
            <div style={{ fontSize: 12, color: '#8c8c8c' }}>
              {`${record.first_name || ''} ${record.last_name || ''}`.trim()}
            </div>
          )}
        </div>
      )
    },
    {
      title: t('reports.module'),
      dataIndex: 'module_title',
      key: 'module_title'
    },
    {
      title: t('reports.language'),
      dataIndex: 'language_code',
      key: 'language_code',
      render: (code) => code.toUpperCase()
    },
    {
      title: t('reports.attempt'),
      dataIndex: 'attempt_number',
      key: 'attempt_number',
      align: 'center'
    },
    {
      title: t('reports.score'),
      dataIndex: 'score',
      key: 'score',
      align: 'center',
      render: (score, record) => (
        <span style={{ 
          color: record.passed ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {score}%
        </span>
      )
    },
    {
      title: t('reports.status'),
      dataIndex: 'passed',
      key: 'passed',
      align: 'center',
      render: (passed) => (
        <span style={{
          color: passed ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {passed ? t('reports.passed') : t('reports.failed')}
        </span>
      )
    },
    {
      title: t('reports.completed'),
      dataIndex: 'completed_at',
      key: 'completed_at',
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: t('reports.duration'),
      dataIndex: 'time_taken_seconds',
      key: 'time_taken_seconds',
      align: 'center',
      render: (seconds) => {
        if (!seconds || isNaN(seconds) || seconds < 0) {
          return '-'
        }

        const totalSeconds = Math.floor(seconds)
        const hours = Math.floor(totalSeconds / 3600)
        const minutes = Math.floor((totalSeconds % 3600) / 60)
        const secs = totalSeconds % 60

        if (hours > 0) {
          // Format as HH:MM:SS for times longer than 1 hour
          return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
        } else {
          // Format as MM:SS for times under 1 hour
          return `${minutes}:${secs.toString().padStart(2, '0')}`
        }
      }
    }
  ]

  const tabItems = [
    {
      key: 'overview',
      label: (
        <span>
          <BarChartOutlined />
          Overview & Compliance
        </span>
      ),
      children: (
        <div>
          {/* Filters */}
          <Card style={{ marginBottom: 24 }}>
            <Title level={4}>{t('reports.filters')}</Title>
            <Space wrap>
              <Select
                placeholder={t('reports.selectModule')}
                style={{ width: 200 }}
                allowClear
                value={filters.moduleId}
                onChange={(value) => setFilters(prev => ({ ...prev, moduleId: value }))}
              >
                {(modulesData || []).map(module => (
                  <Option key={module.id} value={module.id}>
                    {module.title}
                  </Option>
                ))}
              </Select>

              <RangePicker
                placeholder={[t('reports.startDate'), t('reports.endDate')]}
                onChange={handleDateRangeChange}
              />

              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleDownloadComplianceReport}
                loading={downloadingReport}
              >
                {t('reports.downloadComplianceReport')}
              </Button>
            </Space>
          </Card>

          {/* Completion Statistics */}
          <Card style={{ marginBottom: 24 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <Title level={4}>
                <BarChartOutlined style={{ marginRight: 8 }} />
                {t('reports.trainingCompletionStatistics')}
              </Title>
            </div>

            {statsLoading ? (
              <div style={{ textAlign: 'center', padding: 40 }}>
                <Spin size="large" />
              </div>
            ) : statsError ? (
              <Alert
                message={t('reports.errorLoadingStatistics')}
                description={handleAPIError(statsError).message}
                type="error"
                showIcon
              />
            ) : (
              <Table
                columns={statsColumns}
                dataSource={statsData || []}
                rowKey="module_id"
                pagination={false}
                size="middle"
              />
            )}
          </Card>

          {/* User Activity */}
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <Title level={4}>
                <FileTextOutlined style={{ marginRight: 8 }} />
                {t('reports.recentQuizActivity')}
              </Title>
            </div>

            {activityLoading ? (
              <div style={{ textAlign: 'center', padding: 40 }}>
                <LoadingImage size="medium" />
              </div>
            ) : activityError ? (
              <Alert
                message={t('reports.errorLoadingActivity')}
                description={handleAPIError(activityError).message}
                type="error"
                showIcon
              />
            ) : !activityData ? (
              <div style={{ textAlign: 'center', padding: 40 }}>
                <FileTextOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <Title level={4} type="secondary">{t('reports.noActivityData')}</Title>
                <Text type="secondary">
                  {t('reports.selectDateRangeOrUser')}
                </Text>
              </div>
            ) : (
              <Table
                columns={activityColumns}
                dataSource={activityData}
                rowKey={(record) => `${record.email}-${record.completed_at}`}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showTotal: (total) => t('reports.totalAttempts', { total })
                }}
                size="middle"
              />
            )}
          </Card>
        </div>
      )
    },
    {
      key: 'questions',
      label: (
        <span>
          <QuestionCircleOutlined />
          Question Analytics
        </span>
      ),
      children: <QuestionStatistics />
    }
  ]

  return (
    <div>
      <Title level={2}>{t('reports.title')}</Title>
      <Tabs defaultActiveKey="overview" items={tabItems} />
    </div>
  )
}

export default Reports
