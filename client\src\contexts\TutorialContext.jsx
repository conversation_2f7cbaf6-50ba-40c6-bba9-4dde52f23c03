import React, { createContext, useContext, useState, useMemo, useCallback } from 'react'

const TutorialContext = createContext()

export const useTutorial = () => {
  const context = useContext(TutorialContext)
  if (!context) {
    throw new Error('useTutorial must be used within a TutorialProvider')
  }
  return context
}

export const TutorialProvider = ({ children }) => {
  const [showTutorial, setShowTutorial] = useState(false)
  const [tutorialStep, setTutorialStep] = useState(0)

  // Interactive tutorial state
  const [tutorialDemoState, setTutorialDemoState] = useState({
    videoPlaying: false,
    videoProgress: 30, // Demo progress at 30%
    volume: 75,
    muted: false,
    language: 'en',
    showQuizDemo: false
  })

  const startTutorial = useCallback(() => {
    setShowTutorial(true)
    setTutorialStep(0)
    // Reset demo state when starting tutorial
    setTutorialDemoState({
      videoPlaying: false,
      videoProgress: 30,
      volume: 75,
      muted: false,
      language: 'en',
      showQuizDemo: false
    })
  }, [])

  const closeTutorial = useCallback(() => {
    setShowTutorial(false)
  }, [])

  const nextStep = useCallback(() => {
    setTutorialStep(prev => prev + 1)
  }, [])

  const prevStep = useCallback(() => {
    setTutorialStep(prev => Math.max(0, prev - 1))
  }, [])

  const setStep = useCallback((step) => {
    setTutorialStep(step)
  }, [])

  // Tutorial demo actions
  const updateTutorialDemoState = useCallback((updates) => {
    setTutorialDemoState(prev => ({ ...prev, ...updates }))
  }, [])

  const value = useMemo(() => ({
    showTutorial,
    tutorialStep,
    tutorialDemoState,
    startTutorial,
    closeTutorial,
    nextStep,
    prevStep,
    setStep,
    setShowTutorial,
    setTutorialStep,
    updateTutorialDemoState
  }), [
    showTutorial,
    tutorialStep,
    tutorialDemoState,
    startTutorial,
    closeTutorial,
    nextStep,
    prevStep,
    setStep,
    updateTutorialDemoState
  ])

  return (
    <TutorialContext.Provider value={value}>
      {children}
    </TutorialContext.Provider>
  )
}
