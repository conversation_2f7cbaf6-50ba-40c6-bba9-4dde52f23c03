import React from 'react'
import { Result, But<PERSON> } from 'antd'
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons'
import { devError } from '../utils/performance'

/**
 * Error Boundary component to catch and handle React component errors
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null 
    }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Log error details in development
    devError('Error Boundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // You can also log the error to an error reporting service here
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.state.errorInfo)
      }

      // Default fallback UI
      return (
        <div style={{ padding: '50px', textAlign: 'center' }}>
          <Result
            status="error"
            title="Something went wrong"
            subTitle={
              this.props.showDetails && process.env.NODE_ENV === 'development'
                ? this.state.error?.message || 'An unexpected error occurred'
                : 'An unexpected error occurred. Please try refreshing the page.'
            }
            extra={[
              <Button 
                key="reload" 
                type="primary" 
                icon={<ReloadOutlined />}
                onClick={this.handleReload}
              >
                Reload Page
              </Button>,
              <Button 
                key="home" 
                icon={<HomeOutlined />}
                onClick={this.handleGoHome}
              >
                Go Home
              </Button>
            ]}
          />
          
          {/* Show error details in development */}
          {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
            <details style={{ 
              marginTop: '20px', 
              textAlign: 'left', 
              background: '#f5f5f5', 
              padding: '10px',
              borderRadius: '4px',
              maxWidth: '800px',
              margin: '20px auto'
            }}>
              <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                Error Details (Development Only)
              </summary>
              <pre style={{ 
                whiteSpace: 'pre-wrap', 
                fontSize: '12px',
                marginTop: '10px'
              }}>
                {this.state.error && this.state.error.toString()}
                <br />
                {this.state.errorInfo.componentStack}
              </pre>
            </details>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * Higher-order component to wrap components with error boundary
 * @param {React.Component} Component - Component to wrap
 * @param {Object} errorBoundaryProps - Props for error boundary
 * @returns {React.Component} Wrapped component
 */
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = React.forwardRef((props, ref) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} ref={ref} />
    </ErrorBoundary>
  ))

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Hook to create an error boundary for functional components
 * @returns {Function} Function to trigger error boundary
 */
export const useErrorHandler = () => {
  const [error, setError] = React.useState(null)

  React.useEffect(() => {
    if (error) {
      throw error
    }
  }, [error])

  return React.useCallback((error) => {
    setError(error)
  }, [])
}

/**
 * Async error boundary for handling promise rejections
 */
export const AsyncErrorBoundary = ({ children, onError, fallback }) => {
  const [error, setError] = React.useState(null)

  React.useEffect(() => {
    const handleUnhandledRejection = (event) => {
      setError(new Error(event.reason))
      if (onError) {
        onError(event.reason)
      }
    }

    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [onError])

  if (error) {
    if (fallback) {
      return fallback(error)
    }
    
    return (
      <Result
        status="error"
        title="Async Error"
        subTitle="An error occurred while processing your request."
        extra={
          <Button type="primary" onClick={() => setError(null)}>
            Try Again
          </Button>
        }
      />
    )
  }

  return children
}

export default ErrorBoundary
