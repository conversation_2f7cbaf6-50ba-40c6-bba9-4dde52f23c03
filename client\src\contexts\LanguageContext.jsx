import React, { createContext, useContext, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { ConfigProvider, message } from 'antd'
import enUS from 'antd/locale/en_US'
import zhCN from 'antd/locale/zh_CN'
import { usersAPI, handleAPIError } from '../services/api'

const LanguageContext = createContext()

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}



export const LanguageProvider = ({ children }) => {
  const { i18n } = useTranslation()
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'en')

  // Ant Design locale mapping
  const antdLocales = {
    en: enUS,
    zh: zhCN
  }

  // Language options for UI
  const languageOptions = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'zh', name: 'Simplified Chinese', nativeName: '简体中文' }
  ]

  // Language options for content creation (videos/quizzes)
  const contentLanguageOptions = [
    { code: 'en', name: 'English' },
    { code: 'zh', name: '简体中文' }
  ]

  useEffect(() => {
    // Sync with i18n language changes
    const handleLanguageChange = (lng) => {
      setCurrentLanguage(lng)
    }

    i18n.on('languageChanged', handleLanguageChange)
    return () => {
      i18n.off('languageChanged', handleLanguageChange)
    }
  }, [i18n])

  const changeLanguage = async (languageCode) => {
    try {
      // Change the UI language immediately
      await i18n.changeLanguage(languageCode)
      setCurrentLanguage(languageCode)
      // Store in localStorage for persistence
      localStorage.setItem('i18nextLng', languageCode)

      // Update user's language preference on the server
      try {
        await usersAPI.updateOwnLanguage(languageCode)
        console.log(`Language preference updated to ${languageCode} on server`)

        // Dispatch custom event to notify other parts of the app
        window.dispatchEvent(new CustomEvent('languageChanged', {
          detail: { language: languageCode }
        }))
      } catch (apiError) {
        // Don't show error to user for API failure, but log it
        console.warn('Failed to update language preference on server:', apiError)
        // The UI language change still works, just the server preference wasn't updated
      }
    } catch (error) {
      console.error('Failed to change language:', error)
      message.error('Failed to change language')
    }
  }

  const getCurrentLanguageName = () => {
    const lang = languageOptions.find(lang => lang.code === currentLanguage)
    return lang ? lang.nativeName : 'English'
  }

  const value = {
    currentLanguage,
    changeLanguage,
    languageOptions,
    contentLanguageOptions,
    getCurrentLanguageName,
    antdLocale: antdLocales[currentLanguage] || enUS
  }

  // Ant Design theme configuration - Gray color scheme
  const theme = {
    token: {
      colorPrimary: '#595959',        // Primary gray
      colorPrimaryHover: '#434343',   // Darker gray on hover
      colorPrimaryActive: '#262626',  // Darkest gray when active
      borderRadius: 6,
      wireframe: false,
    },
    components: {
      Layout: {
        headerBg: '#ffffff',
        headerHeight: 64,
        headerPadding: '0 24px',
        siderBg: '#f5f5f5',
        bodyBg: '#ffffff',
      },
      Menu: {
        itemBg: 'transparent',
        itemSelectedBg: '#f0f0f0',
        itemHoverBg: '#fafafa',
        itemSelectedColor: '#262626',
        itemColor: '#595959',
      },
      Button: {
        borderRadius: 6,
      },
      Card: {
        borderRadius: 8,
      },
    },
  }

  return (
    <LanguageContext.Provider value={value}>
      <ConfigProvider locale={value.antdLocale} theme={theme}>
        {children}
      </ConfigProvider>
    </LanguageContext.Provider>
  )
}
