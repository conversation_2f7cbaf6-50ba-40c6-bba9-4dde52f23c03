import React from 'react'
import { But<PERSON>, Space, Typography, Progress, message, Card, Radio, Select, Alert } from 'antd'
import {
  BookOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  BackwardOutlined,
  ForwardOutlined,
  QuestionCircleOutlined,
  SoundOutlined,
  SoundFilled,
  FullscreenOutlined,
  TranslationOutlined,
  SaveOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useTutorial } from '../contexts/TutorialContext'
import LanguageSwitch from './LanguageSwitch'

const { Text } = Typography

// Interactive Training Modules Button
export const InteractiveTrainingModulesButton = () => {
  const { t } = useTranslation()

  const handleClick = () => {
    message.success(t('tutorial.interactive.trainingModulesClicked'))
  }

  return (
    <div style={{ textAlign: 'center', margin: '20px 0' }}>
      <Button
        type="primary"
        size="large"
        icon={<BookOutlined />}
        onClick={handleClick}
        style={{
          width: 200,
          height: 60
        }}
      >
        <div>
          <div style={{ fontSize: 16, fontWeight: 'bold' }}>{t('navigation.trainingModules')}</div>
        </div>
      </Button>
      <div style={{ marginTop: '8px', fontSize: '12px', color: '#1890ff' }}>
        ✓ {t('tutorial.interactive.clickToTry')}
      </div>
    </div>
  )
}

// Interactive Quiz Button - Using exact Quiz.jsx structure
export const InteractiveQuizButton = () => {
  const { t } = useTranslation()
  const { tutorialDemoState, updateTutorialDemoState } = useTutorial()

  const handleClick = () => {
    updateTutorialDemoState({ showQuizDemo: !tutorialDemoState.showQuizDemo })
    message.success(t('tutorial.interactive.quizButtonClicked'))
  }

  const handleAnswerChange = (value) => {
    updateTutorialDemoState({ selectedAnswer: value })
    // Simulate the real quiz feedback
    if (value === 2) { // Option B (correct answer)
      message.success(t('tutorial.interactive.correctAnswer'))
    } else {
      message.error(t('tutorial.interactive.incorrectAnswer'))
    }
  }

  // Mock quiz data structure matching real Quiz.jsx
  const mockQuestion = {
    id: 1,
    question_text: t('tutorial.interactive.sampleQuestionText'),
    question_type: 'multiple_choice',
    options: [
      { id: 1, optionText: t('tutorial.interactive.sampleAnswerA') },
      { id: 2, optionText: t('tutorial.interactive.sampleAnswerB') },
      { id: 3, optionText: t('tutorial.interactive.sampleAnswerC') }
    ]
  }

  return (
    <div style={{ textAlign: 'center', margin: '20px 0', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
      <Button
        type="primary"
        size="large"
        icon={<QuestionCircleOutlined />}
        onClick={handleClick}
        style={{
          backgroundColor: '#1890ff',
          borderColor: '#1890ff',
          color: 'white'
        }}
      >
        {t('loginTutorial.showQuizExample')}
      </Button>
      <div style={{ marginTop: '8px', fontSize: '12px', color: '#1890ff' }}>
        ✓ {t('tutorial.interactive.clickToTry')}
      </div>

      {tutorialDemoState.showQuizDemo && (
        <div style={{ marginTop: '16px', textAlign: 'left' }}>
          {/* Exact quiz container structure from Quiz.jsx */}
          <div className="quiz-container">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <Card className="quiz-question">
                <div className="quiz-question-title">
                  {t('quiz.question')} 1: {mockQuestion.question_text}
                </div>

                {/* Exact multiple choice structure from Quiz.jsx */}
                <Radio.Group
                  value={tutorialDemoState.selectedAnswer}
                  onChange={(e) => handleAnswerChange(e.target.value)}
                  style={{ width: '100%' }}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {mockQuestion.options.map((option) => (
                      <div
                        key={option.id}
                        style={{
                          border: '1px solid #d9d9d9',
                          borderRadius: 6,
                          padding: 12,
                          cursor: 'pointer',
                          backgroundColor: tutorialDemoState.selectedAnswer === option.id ? '#e6f7ff' : '#fafafa',
                          borderColor: tutorialDemoState.selectedAnswer === option.id ? '#1890ff' : '#d9d9d9',
                          transition: 'all 0.3s'
                        }}
                        onClick={() => handleAnswerChange(option.id)}
                        title={t('quiz.clickToSelectOption')}
                      >
                        <Radio value={option.id} style={{ pointerEvents: 'none' }}>
                          {option.optionText}
                        </Radio>
                      </div>
                    ))}
                  </Space>
                </Radio.Group>
              </Card>

              {/* Submit button matching Quiz.jsx */}
              <Card style={{ textAlign: 'center' }}>
                <Button
                  type="primary"
                  size="large"
                  disabled={!tutorialDemoState.selectedAnswer}
                  style={{ marginRight: '8px' }}
                >
                  {t('quiz.submitQuiz')}
                </Button>
                <Button onClick={() => updateTutorialDemoState({ showQuizDemo: false, selectedAnswer: null })}>
                  {t('tutorial.interactive.closeQuiz')}
                </Button>
                {!tutorialDemoState.selectedAnswer && (
                  <div style={{ marginTop: 8, color: '#8c8c8c' }}>
                    {t('quiz.answerAllQuestions')}
                  </div>
                )}
              </Card>
            </Space>
          </div>
        </div>
      )}
    </div>
  )
}

// Interactive Language Switch - Using exact LanguageSwitch component
export const InteractiveLanguageSwitch = () => {
  const { t } = useTranslation()
  const { tutorialDemoState, updateTutorialDemoState } = useTutorial()

  // Mock language context for tutorial
  const mockLanguageContext = {
    currentLanguage: tutorialDemoState.language,
    changeLanguage: (languageCode) => {
      updateTutorialDemoState({ language: languageCode })
      message.success(t('tutorial.interactive.languageChanged', {
        language: languageCode === 'en' ? 'English' : '简体中文'
      }))
    },
    languageOptions: [
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'zh', name: 'Simplified Chinese', nativeName: '简体中文' }
    ]
  }

  // Create a tutorial version of LanguageSwitch that uses our mock context
  const TutorialLanguageSwitch = () => {
    return (
      <Space
        size="small"
        style={{
          border: '1px solid #40a9ff',
          borderRadius: '8px',
          padding: '2px 6px',
          backgroundColor: '#f0f8ff',
          height: '28px',
          alignItems: 'center',
          flexShrink: 0
        }}
        className="language-switch-container"
      >
        <TranslationOutlined style={{ color: '#1890ff', fontSize: '14px' }} />
        <Select
          value={mockLanguageContext.currentLanguage}
          onChange={mockLanguageContext.changeLanguage}
          size="small"
          style={{
            minWidth: 90,
            color: '#000000',
            fontSize: '12px'
          }}
          variant="borderless"
          suffixIcon={null}
        >
          {mockLanguageContext.languageOptions.map(lang => (
            <Select.Option key={lang.code} value={lang.code}>
              {lang.nativeName}
            </Select.Option>
          ))}
        </Select>
      </Space>
    )
  }

  const currentLanguageLabel = tutorialDemoState.language === 'en' ? 'English' : '简体中文'

  return (
    <div style={{ textAlign: 'center', margin: '20px 0', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
      <div style={{ marginBottom: '12px' }}>
        <Text strong>{t('tutorial.interactive.currentLanguage')}: {currentLanguageLabel}</Text>
      </div>

      {/* Exact LanguageSwitch component structure */}
      <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '12px' }}>
        <TutorialLanguageSwitch />
      </div>

      <div style={{ marginTop: '8px', fontSize: '12px', color: '#1890ff' }}>
        ✓ {t('tutorial.interactive.clickToTry')}
      </div>

      <div style={{ marginTop: '8px', fontSize: '11px', color: '#666', fontStyle: 'italic' }}>
        {t('tutorial.interactive.languageSelectorNote')}
      </div>
    </div>
  )
}

// Interactive Video Player Controls
export const InteractiveVideoControls = () => {
  const { t } = useTranslation()
  const { tutorialDemoState, updateTutorialDemoState } = useTutorial()

  const handlePlayPause = () => {
    const newPlaying = !tutorialDemoState.videoPlaying
    updateTutorialDemoState({ videoPlaying: newPlaying })
    message.success(newPlaying ? t('tutorial.interactive.videoPlaying') : t('tutorial.interactive.videoPaused'))
  }

  const handleSkipBackward = () => {
    const newProgress = Math.max(0, tutorialDemoState.videoProgress - 10)
    updateTutorialDemoState({ videoProgress: newProgress })
    message.success(t('tutorial.interactive.skippedBackward'))
  }

  const handleSkipForward = () => {
    const newProgress = Math.min(100, tutorialDemoState.videoProgress + 10)
    updateTutorialDemoState({ videoProgress: newProgress })
    message.success(t('tutorial.interactive.skippedForward'))
  }

  const handleSaveProgress = () => {
    message.success(t('tutorial.interactive.progressSaved'))
  }

  return (
    <div style={{ textAlign: 'center', margin: '20px 0', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
      <div style={{ marginBottom: '16px' }}>
        <Text strong>{t('tutorial.interactive.videoDemo')}</Text>
        <div style={{ margin: '8px 0' }}>
          <Progress
            percent={tutorialDemoState.videoProgress}
            size="small"
            status={tutorialDemoState.videoPlaying ? 'active' : 'normal'}
          />
          <Text style={{ fontSize: '12px', color: '#666' }}>
            {t('tutorial.interactive.progress')}: {tutorialDemoState.videoProgress}%
          </Text>
        </div>
      </div>

      {/* Video Controls Row */}
      <Space size="large" style={{ marginBottom: '16px' }}>
        <Button
          type="primary"
          size="large"
          icon={tutorialDemoState.videoPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          onClick={handlePlayPause}
        >
          {tutorialDemoState.videoPlaying ? t('videoPlayer.pause') : t('videoPlayer.play')}
        </Button>

        <Button
          size="large"
          icon={<BackwardOutlined />}
          onClick={handleSkipBackward}
        >
          {t('videoPlayer.back5s')}
        </Button>

        <Button
          size="large"
          icon={<ForwardOutlined />}
          onClick={handleSkipForward}
        >
          {t('videoPlayer.forward10s')}
        </Button>
      </Space>

      {/* Save Progress Button - Matching actual VideoPlayer implementation */}
      <div style={{ marginBottom: '8px' }}>
        <Button
          icon={<SaveOutlined />}
          onClick={handleSaveProgress}
          size="large"
          style={{
            backgroundColor: '#f0f0f0',
            borderColor: '#d9d9d9'
          }}
        >
          {t('videoPlayer.saveProgress')}
        </Button>
      </div>

      <div style={{ fontSize: '12px', color: '#1890ff' }}>
        ✓ {t('tutorial.interactive.clickToTry')}
      </div>
    </div>
  )
}

// Interactive Volume Controls
export const InteractiveVolumeControls = () => {
  const { t } = useTranslation()
  const { tutorialDemoState, updateTutorialDemoState } = useTutorial()
  
  const handleMuteToggle = () => {
    const newMuted = !tutorialDemoState.muted
    updateTutorialDemoState({ muted: newMuted })
    message.success(newMuted ? t('tutorial.interactive.videoMuted') : t('tutorial.interactive.videoUnmuted'))
  }

  const handleVolumeDown = () => {
    const newVolume = Math.max(0, tutorialDemoState.volume - 25)
    updateTutorialDemoState({ volume: newVolume })
    message.success(t('tutorial.interactive.volumeChanged', { volume: newVolume }))
  }

  const handleVolumeUp = () => {
    const newVolume = Math.min(100, tutorialDemoState.volume + 25)
    updateTutorialDemoState({ volume: newVolume })
    message.success(t('tutorial.interactive.volumeChanged', { volume: newVolume }))
  }

  return (
    <div style={{ textAlign: 'center', margin: '20px 0', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
      <div style={{ marginBottom: '12px' }}>
        <Text strong>{t('tutorial.interactive.volumeDemo')}</Text>
      </div>
      
      <Space size="large">
        <Button 
          size="large" 
          icon={tutorialDemoState.muted || tutorialDemoState.volume === 0 ? <SoundOutlined /> : <SoundFilled />}
          onClick={handleMuteToggle}
        />
        <Button 
          size="large" 
          onClick={handleVolumeDown}
          disabled={tutorialDemoState.volume === 0}
        >
          -
        </Button>
        <Text strong>{tutorialDemoState.muted ? '🔇' : `${tutorialDemoState.volume}%`}</Text>
        <Button 
          size="large" 
          onClick={handleVolumeUp}
          disabled={tutorialDemoState.volume === 100}
        >
          +
        </Button>
      </Space>
      
      <div style={{ marginTop: '8px', fontSize: '12px', color: '#1890ff' }}>
        ✓ {t('tutorial.interactive.clickToTry')}
      </div>
    </div>
  )
}
