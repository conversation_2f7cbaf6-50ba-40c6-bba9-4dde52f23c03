const { query } = require('../../database/init');

/**
 * Get video by module and language
 */
async function getVideoByModuleAndLanguage(moduleId, languageCode) {
  const result = await query(`
    SELECT tv.*, tm.title as module_title
    FROM training_videos tv
    JOIN training_modules tm ON tv.module_id = tm.id
    WHERE tv.module_id = $1 AND tv.language_code = $2
  `, [moduleId, languageCode]);
  
  return result.rows[0] || null;
}

/**
 * Check if user has completed video
 */
async function checkVideoCompletion(userEmail, moduleId, languageCode) {
  const result = await query(`
    SELECT video_watched FROM user_progress
    WHERE user_email = $1 AND module_id = $2 AND language_code = $3
  `, [userEmail, moduleId, languageCode]);
  
  return result.rows.length > 0 && result.rows[0].video_watched;
}

/**
 * Get quiz by video ID
 */
async function getQuizByVideoId(videoId) {
  const result = await query(`
    SELECT * FROM quizzes
    WHERE video_id = $1 AND is_active = true
  `, [videoId]);
  
  return result.rows[0] || null;
}

/**
 * Get quiz questions with options
 */
async function getQuizQuestionsWithOptions(quizId) {
  const result = await query(`
    SELECT
      qq.id,
      qq.quiz_id,
      qq.question_text,
      qq.question_type,
      qq.question_order,
      qq.points,
      qq.question_image,
      qq.created_at,
      COALESCE(
        json_agg(
          json_build_object(
            'id', qo.id,
            'optionText', qo.option_text,
            'optionOrder', qo.option_order,
            'optionImage', qo.option_image
          ) ORDER BY qo.option_order
        ) FILTER (WHERE qo.id IS NOT NULL),
        '[]'
      ) as options
    FROM quiz_questions qq
    LEFT JOIN quiz_question_options qo ON qq.id = qo.question_id
    WHERE qq.quiz_id = $1
    GROUP BY qq.id, qq.quiz_id, qq.question_text, qq.question_type, qq.question_order, qq.points, qq.question_image, qq.created_at
    ORDER BY qq.question_order
  `, [quizId]);

  return result.rows;
}

/**
 * Get quiz questions with correct answers for grading
 */
async function getQuizQuestionsForGrading(client, quizId) {
  const result = await client.query(`
    SELECT
      qq.id,
      qq.question_type,
      qq.points,
      qq.question_text,
      COALESCE(
        json_agg(
          json_build_object(
            'id', qo.id,
            'isCorrect', qo.is_correct
          )
        ) FILTER (WHERE qo.id IS NOT NULL),
        '[]'
      ) as options
    FROM quiz_questions qq
    LEFT JOIN quiz_question_options qo ON qq.id = qo.question_id
    WHERE qq.quiz_id = $1
    GROUP BY qq.id, qq.question_type, qq.points, qq.question_text
  `, [quizId]);

  return result.rows;
}

/**
 * Check if user has already passed quiz
 */
async function checkQuizPassed(userEmail, quizId) {
  const result = await query(`
    SELECT passed FROM quiz_attempts
    WHERE user_email = $1 AND quiz_id = $2 AND passed = true
    LIMIT 1
  `, [userEmail, quizId]);
  
  return result.rows.length > 0;
}

/**
 * Get quiz for editing (admin)
 */
async function getQuizForEditing(videoId) {
  const quizResult = await query(`
    SELECT * FROM quizzes
    WHERE video_id = $1 AND is_active = true
  `, [videoId]);

  if (quizResult.rows.length === 0) {
    return null;
  }

  const quiz = quizResult.rows[0];

  // Get quiz questions with options
  const questionsResult = await query(`
    SELECT
      qq.id,
      qq.question_text as "questionText",
      qq.question_type as "questionType",
      qq.question_order as "questionOrder",
      qq.points,
      qq.question_image as "questionImage",
      COALESCE(
        json_agg(
          json_build_object(
            'id', qo.id,
            'optionText', qo.option_text,
            'isCorrect', qo.is_correct,
            'optionOrder', qo.option_order,
            'optionImage', qo.option_image
          ) ORDER BY qo.option_order
        ) FILTER (WHERE qo.id IS NOT NULL),
        '[]'
      ) as options
    FROM quiz_questions qq
    LEFT JOIN quiz_question_options qo ON qq.id = qo.question_id
    WHERE qq.quiz_id = $1
    GROUP BY qq.id, qq.question_text, qq.question_type, qq.question_order, qq.points, qq.question_image
    ORDER BY qq.question_order
  `, [quiz.id]);

  return {
    quiz: {
      id: quiz.id,
      video_id: quiz.video_id,
      title: quiz.title,
      description: quiz.description,
      passingScore: quiz.passing_score,
      timeLimitMinutes: quiz.time_limit_minutes,
      created_at: quiz.created_at,
      updated_at: quiz.updated_at,
      is_active: quiz.is_active,
      questions: questionsResult.rows
    }
  };
}

/**
 * Check if quiz exists for video
 */
async function checkQuizExists(videoId) {
  const result = await query(
    'SELECT id FROM quizzes WHERE video_id = $1',
    [videoId]
  );
  
  return result.rows.length > 0;
}

/**
 * Get video by ID
 */
async function getVideoById(videoId) {
  const result = await query(`
    SELECT tv.*, tm.title as module_title
    FROM training_videos tv
    JOIN training_modules tm ON tv.module_id = tm.id
    WHERE tv.id = $1
  `, [videoId]);
  
  return result.rows[0] || null;
}

module.exports = {
  getVideoByModuleAndLanguage,
  checkVideoCompletion,
  getQuizByVideoId,
  getQuizQuestionsWithOptions,
  getQuizQuestionsForGrading,
  checkQuizPassed,
  getQuizForEditing,
  checkQuizExists,
  getVideoById
};
