-- Migration script to add is_default_admin column and update existing default admin
-- This script is safe to run multiple times

-- Add the is_default_admin column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'is_default_admin'
    ) THEN
        ALTER TABLE users ADD COLUMN is_default_admin BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added is_default_admin column to users table';
    ELSE
        RAISE NOTICE 'is_default_admin column already exists';
    END IF;
END $$;

-- Update the existing default admin user to have the flag set
UPDATE users 
SET is_default_admin = TRUE 
WHERE email = '<EMAIL>' AND is_admin = TRUE;

-- Verify the update
DO $$
DECLARE
    admin_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO admin_count 
    FROM users 
    WHERE email = '<EMAIL>' AND is_default_admin = TRUE;
    
    IF admin_count > 0 THEN
        RAISE NOTICE 'Default admin flag successfully <NAME_EMAIL>';
    ELSE
        RAISE NOTICE 'Warning: Default admin user not found or flag not set';
    END IF;
END $$;
