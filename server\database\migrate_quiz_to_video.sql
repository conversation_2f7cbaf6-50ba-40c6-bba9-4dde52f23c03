-- Migration script to convert quiz association from module+language to video-based
-- This script safely migrates existing quiz data to the new schema

-- Step 1: Create backup of existing quizzes table
CREATE TABLE IF NOT EXISTS quizzes_backup AS SELECT * FROM quizzes;

-- Step 2: Create new quizzes table with video_id association
DROP TABLE IF EXISTS quizzes_new;
CREATE TABLE quizzes_new (
    id SERIAL PRIMARY KEY,
    video_id INTEGER REFERENCES training_videos(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    passing_score INTEGER DEFAULT 70,
    time_limit_minutes INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(video_id)
);

-- Step 3: Migrate existing quiz data to new structure
-- Match quizzes to videos based on module_id and language_code
INSERT INTO quizzes_new (
    video_id,
    title,
    description,
    passing_score,
    time_limit_minutes,
    created_at,
    updated_at,
    is_active
)
SELECT 
    tv.id as video_id,
    q.title,
    q.description,
    q.passing_score,
    q.time_limit_minutes,
    q.created_at,
    q.updated_at,
    q.is_active
FROM quizzes q
INNER JOIN training_videos tv ON tv.module_id = q.module_id AND tv.language_code = q.language_code
WHERE q.is_active = true;

-- Step 4: Update quiz_questions to reference new quiz IDs
-- First, create mapping table for old quiz ID to new quiz ID
CREATE TEMP TABLE quiz_id_mapping AS
SELECT 
    q_old.id as old_quiz_id,
    q_new.id as new_quiz_id
FROM quizzes q_old
INNER JOIN training_videos tv ON tv.module_id = q_old.module_id AND tv.language_code = q_old.language_code
INNER JOIN quizzes_new q_new ON q_new.video_id = tv.id;

-- Update quiz_questions table with new quiz IDs
UPDATE quiz_questions 
SET quiz_id = mapping.new_quiz_id
FROM quiz_id_mapping mapping
WHERE quiz_questions.quiz_id = mapping.old_quiz_id;

-- Step 5: Update quiz_attempts table to reference new quiz IDs
UPDATE quiz_attempts 
SET quiz_id = mapping.new_quiz_id
FROM quiz_id_mapping mapping
WHERE quiz_attempts.quiz_id = mapping.old_quiz_id;

-- Step 6: Replace old quizzes table with new one
DROP TABLE quizzes;
ALTER TABLE quizzes_new RENAME TO quizzes;

-- Step 7: Update user_progress table structure to track video-based progress
-- Add video_id column to user_progress if it doesn't exist
ALTER TABLE user_progress ADD COLUMN IF NOT EXISTS video_id INTEGER REFERENCES training_videos(id) ON DELETE CASCADE;

-- Populate video_id in user_progress based on module_id and language_code
UPDATE user_progress 
SET video_id = tv.id
FROM training_videos tv 
WHERE user_progress.module_id = tv.module_id 
AND user_progress.language_code = tv.language_code
AND user_progress.video_id IS NULL;

-- Step 8: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_quizzes_video_id ON quizzes(video_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_video_id ON user_progress(video_id);

-- Step 9: Clean up temporary tables
DROP TABLE IF EXISTS quiz_id_mapping;

-- Migration complete
-- Note: Keep quizzes_backup table for safety until migration is verified
