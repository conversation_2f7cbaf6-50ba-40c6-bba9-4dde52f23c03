const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const systemConfigService = require('../../services/systemConfigService');

// Store for tracking active uploads
const activeUploads = new Map();

// Configure multer for chunk uploads
const chunkStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadId = req.body.uploadId;
    const chunkDir = path.join(__dirname, '../../uploads/chunks', uploadId);

    // Use synchronous fs operations for multer compatibility
    const fsSync = require('fs');
    try {
      fsSync.mkdirSync(chunkDir, { recursive: true });
      cb(null, chunkDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const chunkIndex = req.body.chunkIndex;
    cb(null, `chunk-${chunkIndex.toString().padStart(6, '0')}`);
  }
});

const chunkUpload = multer({
  storage: chunkStorage,
  limits: {
    fileSize: 15 * 1024 * 1024, // 15MB limit per chunk (slightly larger than 10MB for safety)
  }
});

/**
 * Initialize a chunked upload session
 */
async function initiateUpload(req, res) {
  try {
    console.log('=== INITIATE CHUNKED UPLOAD ===');
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    const { filename, fileSize, mimeType, totalChunks, chunkSize, moduleId, languageCode, languageName } = req.body;

    // Validate required fields
    if (!filename || !fileSize || !totalChunks || !moduleId) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'filename, fileSize, totalChunks, and moduleId are required'
      });
    }

    // Validate file type
    const allowedExtensions = /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i;
    const allowedMimeTypes = /^video\/(mp4|avi|quicktime|x-msvideo|x-ms-wmv|x-flv|webm|x-matroska|mp4v-es|3gpp|3gpp2)$/i;

    if (!allowedExtensions.test(filename) && !allowedMimeTypes.test(mimeType)) {
      return res.status(400).json({
        error: 'Invalid file type',
        details: `Only video files are allowed. Received: ${filename} (${mimeType})`
      });
    }

    // Validate file size (dynamic limit)
    const maxFileSize = await systemConfigService.getMaxFileSize();
    if (fileSize > maxFileSize) {
      const maxSizeFormatted = await systemConfigService.getMaxFileSizeFormatted();
      return res.status(400).json({
        error: 'File too large',
        details: `Maximum file size is ${maxSizeFormatted}`
      });
    }

    // Generate unique upload ID (fallback for older Node.js versions)
    const uploadId = crypto.randomUUID ? crypto.randomUUID() :
      'upload-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

    // Store upload metadata
    const uploadInfo = {
      uploadId,
      filename,
      fileSize,
      mimeType,
      totalChunks,
      chunkSize,
      moduleId,
      languageCode,
      languageName,
      receivedChunks: new Set(),
      createdAt: new Date(),
      status: 'initiated'
    };

    activeUploads.set(uploadId, uploadInfo);

    // Create chunks directory
    const chunkDir = path.join(__dirname, '../../uploads/chunks', uploadId);
    await fs.mkdir(chunkDir, { recursive: true });

    console.log(`Chunked upload initiated: ${uploadId} for file ${filename} (${fileSize} bytes, ${totalChunks} chunks)`);

    res.json({
      uploadId,
      message: 'Upload initiated successfully'
    });

  } catch (error) {
    console.error('Error initiating chunked upload:', error);
    res.status(500).json({
      error: 'Failed to initiate upload',
      details: error.message
    });
  }
}

/**
 * Handle individual chunk upload
 */
async function uploadChunk(req, res) {
  try {
    console.log('=== UPLOAD CHUNK ===');
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('File info:', req.file ? {
      originalname: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      path: req.file.path
    } : 'No file');

    const { uploadId, chunkIndex } = req.body;
    const chunkIndexNum = parseInt(chunkIndex);

    if (!req.file) {
      return res.status(400).json({ error: 'No chunk file uploaded' });
    }

    if (!uploadId || chunkIndex === undefined) {
      return res.status(400).json({ error: 'Missing uploadId or chunkIndex' });
    }

    // Get upload info
    const uploadInfo = activeUploads.get(uploadId);
    if (!uploadInfo) {
      // Clean up uploaded chunk
      await fs.unlink(req.file.path).catch(console.error);
      return res.status(404).json({ error: 'Upload session not found' });
    }

    // Validate chunk index
    if (chunkIndexNum < 0 || chunkIndexNum >= uploadInfo.totalChunks) {
      await fs.unlink(req.file.path).catch(console.error);
      return res.status(400).json({ error: 'Invalid chunk index' });
    }

    // Check if chunk already received
    if (uploadInfo.receivedChunks.has(chunkIndexNum)) {
      await fs.unlink(req.file.path).catch(console.error);
      return res.status(409).json({ error: 'Chunk already received' });
    }

    // Mark chunk as received
    uploadInfo.receivedChunks.add(chunkIndexNum);

    console.log(`Chunk ${chunkIndexNum}/${uploadInfo.totalChunks - 1} received for upload ${uploadId}`);

    res.json({
      message: 'Chunk uploaded successfully',
      chunkIndex: chunkIndexNum,
      receivedChunks: uploadInfo.receivedChunks.size,
      totalChunks: uploadInfo.totalChunks
    });

  } catch (error) {
    console.error('Error uploading chunk:', error);
    
    // Clean up uploaded file on error
    if (req.file) {
      await fs.unlink(req.file.path).catch(console.error);
    }

    res.status(500).json({
      error: 'Failed to upload chunk',
      details: error.message
    });
  }
}

/**
 * Finalize upload by reassembling chunks
 */
async function finalizeUpload(req, res) {
  try {
    const { uploadId } = req.body;

    // Get upload info
    const uploadInfo = activeUploads.get(uploadId);
    if (!uploadInfo) {
      return res.status(404).json({ error: 'Upload session not found' });
    }

    // Check if all chunks received
    if (uploadInfo.receivedChunks.size !== uploadInfo.totalChunks) {
      return res.status(400).json({
        error: 'Incomplete upload',
        details: `Received ${uploadInfo.receivedChunks.size}/${uploadInfo.totalChunks} chunks`
      });
    }

    // Reassemble file
    const finalFilename = await reassembleFile(uploadInfo);
    
    // Update upload info
    uploadInfo.status = 'completed';
    uploadInfo.finalFilename = finalFilename;

    console.log(`Upload ${uploadId} completed successfully: ${finalFilename}`);

    res.json({
      message: 'Upload completed successfully',
      filename: finalFilename,
      originalName: uploadInfo.filename,
      size: uploadInfo.fileSize,
      uploadId
    });

  } catch (error) {
    console.error('Error finalizing upload:', error);
    res.status(500).json({
      error: 'Failed to finalize upload',
      details: error.message
    });
  }
}

/**
 * Reassemble chunks into final file
 */
async function reassembleFile(uploadInfo) {
  const chunkDir = path.join(__dirname, '../../uploads/chunks', uploadInfo.uploadId);
  const videosDir = path.join(__dirname, '../../uploads/videos');
  
  // Ensure videos directory exists
  await fs.mkdir(videosDir, { recursive: true });

  // Generate unique filename
  const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
  const ext = path.extname(uploadInfo.filename);
  const finalFilename = `video-${uniqueSuffix}${ext}`;
  const finalPath = path.join(videosDir, finalFilename);

  // Create write stream for final file
  const writeStream = require('fs').createWriteStream(finalPath);

  try {
    // Read and write chunks in order
    for (let i = 0; i < uploadInfo.totalChunks; i++) {
      const chunkPath = path.join(chunkDir, `chunk-${i.toString().padStart(6, '0')}`);
      const chunkData = await fs.readFile(chunkPath);
      
      await new Promise((resolve, reject) => {
        writeStream.write(chunkData, (error) => {
          if (error) reject(error);
          else resolve();
        });
      });
    }

    // Close write stream
    await new Promise((resolve, reject) => {
      writeStream.end((error) => {
        if (error) reject(error);
        else resolve();
      });
    });

    // Verify file size
    const stats = await fs.stat(finalPath);
    if (stats.size !== uploadInfo.fileSize) {
      throw new Error(`File size mismatch: expected ${uploadInfo.fileSize}, got ${stats.size}`);
    }

    // Clean up chunks
    await cleanupChunks(uploadInfo.uploadId);

    return finalFilename;

  } catch (error) {
    // Clean up partial file on error
    await fs.unlink(finalPath).catch(console.error);
    throw error;
  }
}

/**
 * Clean up chunk files
 */
async function cleanupChunks(uploadId) {
  try {
    const chunkDir = path.join(__dirname, '../../uploads/chunks', uploadId);
    await fs.rm(chunkDir, { recursive: true, force: true });
    console.log(`Cleaned up chunks for upload ${uploadId}`);
  } catch (error) {
    console.error(`Failed to cleanup chunks for upload ${uploadId}:`, error);
  }
}

/**
 * Clean up failed upload
 */
async function cleanupUpload(req, res) {
  try {
    const { uploadId } = req.params;

    // Get upload info before deletion for logging
    const uploadInfo = activeUploads.get(uploadId);
    const reason = req.body?.reason || 'manual_cleanup';

    // Remove from active uploads
    activeUploads.delete(uploadId);

    // Clean up chunks
    await cleanupChunks(uploadId);

    // Log cleanup reason for monitoring
    if (uploadInfo) {
      console.log(`Upload ${uploadId} cleaned up - Reason: ${reason}, File: ${uploadInfo.filename}, Progress: ${uploadInfo.receivedChunks.size}/${uploadInfo.totalChunks} chunks`);
    } else {
      console.log(`Upload ${uploadId} cleaned up - Reason: ${reason} (upload info not found)`);
    }

    res.json({
      message: 'Upload cleaned up successfully',
      uploadId,
      reason
    });

  } catch (error) {
    console.error('Error cleaning up upload:', error);
    res.status(500).json({
      error: 'Failed to cleanup upload',
      details: error.message
    });
  }
}

/**
 * Get upload status
 */
function getUploadStatus(req, res) {
  const { uploadId } = req.params;
  const uploadInfo = activeUploads.get(uploadId);

  if (!uploadInfo) {
    return res.status(404).json({ error: 'Upload session not found' });
  }

  res.json({
    uploadId,
    status: uploadInfo.status,
    receivedChunks: uploadInfo.receivedChunks.size,
    totalChunks: uploadInfo.totalChunks,
    filename: uploadInfo.filename,
    fileSize: uploadInfo.fileSize
  });
}

/**
 * Cancel upload and clean up chunks immediately
 */
async function cancelUpload(req, res) {
  try {
    const { uploadId } = req.params;

    // Get upload info
    const uploadInfo = activeUploads.get(uploadId);
    if (!uploadInfo) {
      return res.status(404).json({ error: 'Upload session not found' });
    }

    // Mark as cancelled
    uploadInfo.status = 'cancelled';
    uploadInfo.cancelledAt = new Date();

    // Remove from active uploads
    activeUploads.delete(uploadId);

    // Clean up chunks immediately
    await cleanupChunks(uploadId);

    console.log(`Upload ${uploadId} cancelled by user - File: ${uploadInfo.filename}, Progress: ${uploadInfo.receivedChunks.size}/${uploadInfo.totalChunks} chunks`);

    res.json({
      message: 'Upload cancelled successfully',
      uploadId,
      filename: uploadInfo.filename,
      progress: {
        receivedChunks: uploadInfo.receivedChunks.size,
        totalChunks: uploadInfo.totalChunks
      }
    });

  } catch (error) {
    console.error('Error cancelling upload:', error);
    res.status(500).json({
      error: 'Failed to cancel upload',
      details: error.message
    });
  }
}

/**
 * Prune orphaned chunks - clean up chunks without active upload sessions
 */
async function pruneOrphanedChunks() {
  try {
    const chunksDir = path.join(__dirname, '../../uploads/chunks');

    // Check if chunks directory exists
    try {
      await fs.access(chunksDir);
    } catch {
      console.log('Chunks directory does not exist, skipping orphaned chunk cleanup');
      return { cleaned: 0, errors: 0 };
    }

    const chunkDirs = await fs.readdir(chunksDir);
    let cleaned = 0;
    let errors = 0;

    for (const dirName of chunkDirs) {
      const chunkDirPath = path.join(chunksDir, dirName);

      try {
        const stats = await fs.stat(chunkDirPath);
        if (!stats.isDirectory()) continue;

        // Check if this upload ID is still active
        if (!activeUploads.has(dirName)) {
          // Check directory age - clean up if older than 1 hour
          const ageMs = Date.now() - stats.mtime.getTime();
          const maxAge = 60 * 60 * 1000; // 1 hour

          if (ageMs > maxAge) {
            await fs.rm(chunkDirPath, { recursive: true, force: true });
            console.log(`Pruned orphaned chunks for upload: ${dirName} (age: ${Math.round(ageMs / 1000 / 60)} minutes)`);
            cleaned++;
          }
        }
      } catch (error) {
        console.error(`Error processing chunk directory ${dirName}:`, error);
        errors++;
      }
    }

    if (cleaned > 0 || errors > 0) {
      console.log(`Orphaned chunk cleanup completed: ${cleaned} cleaned, ${errors} errors`);
    }

    return { cleaned, errors };
  } catch (error) {
    console.error('Error during orphaned chunk cleanup:', error);
    return { cleaned: 0, errors: 1 };
  }
}

// Cleanup old uploads periodically (every 30 minutes)
setInterval(() => {
  const now = new Date();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  const staleAge = 2 * 60 * 60 * 1000; // 2 hours for stale uploads

  let expiredCount = 0;
  let staleCount = 0;

  for (const [uploadId, uploadInfo] of activeUploads.entries()) {
    const age = now - uploadInfo.createdAt;

    if (age > maxAge) {
      // Clean up very old uploads
      console.log(`Cleaning up expired upload: ${uploadId} (age: ${Math.round(age / 1000 / 60 / 60)} hours)`);
      activeUploads.delete(uploadId);
      cleanupChunks(uploadId).catch(console.error);
      expiredCount++;
    } else if (age > staleAge && uploadInfo.status === 'initiated') {
      // Clean up stale uploads that haven't made progress
      console.log(`Cleaning up stale upload: ${uploadId} (age: ${Math.round(age / 1000 / 60)} minutes, status: ${uploadInfo.status})`);
      activeUploads.delete(uploadId);
      cleanupChunks(uploadId).catch(console.error);
      staleCount++;
    }
  }

  // Also run orphaned chunk cleanup
  pruneOrphanedChunks().catch(console.error);

  if (expiredCount > 0 || staleCount > 0) {
    console.log(`Periodic cleanup completed: ${expiredCount} expired, ${staleCount} stale uploads cleaned`);
  }
}, 30 * 60 * 1000); // Run every 30 minutes

module.exports = {
  chunkUpload,
  initiateUpload,
  uploadChunk,
  finalizeUpload,
  cleanupUpload,
  cancelUpload,
  pruneOrphanedChunks,
  getUploadStatus,
  reassembleFile,
  activeUploads
};
