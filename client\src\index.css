/* ===== GLOBAL STYLES ===== */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ===== LAYOUT COMPONENTS ===== */
.app-layout {
  min-height: 100vh;
}

.ant-layout {
  background: transparent !important;
}

/* Fixed Header Styles - Ensure header stays at top */
.ant-layout-header,
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1001 !important;
  width: 100% !important;
  height: 64px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background-color: #ffffff !important;
  border-bottom: 1px solid #f0f0f0;
  /* Prevent any scrolling behavior */
  transform: translateZ(0);
  will-change: transform;
}

.app-logo {
  display: flex;
  align-items: center;
  color: #000000;
  font-size: 20px;
  font-weight: bold;
  margin-right: 24px;
}

.app-logo-image {
  height: 40px;
  width: auto;
  margin-right: 12px;
}

.app-logo-text {
  color: #000000;
  font-size: 20px;
  font-weight: bold;
}

.app-content {
  margin: 24px;
  margin-top: 88px; /* 64px header height + 24px margin */
  padding: 24px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  min-height: calc(100vh - 112px);
  transition: margin-left 0.2s;
}

/* ===== SIDEBAR COMPONENTS ===== */
.ant-layout-sider {
  background-color: #595959 !important;
  min-height: 100vh !important;
}

.ant-layout-sider-children {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  background-color: #595959 !important;
}

/* ===== SIDEBAR MENU STYLING ===== */
.ant-menu-dark {
  background-color: #595959 !important;
}

/* Default state for all menu items */
.ant-menu-dark .ant-menu-item {
  background-color: #595959 !important;
  color: #ffffff !important;
}

/* Selected state - higher specificity */
.ant-menu-dark .ant-menu-item.ant-menu-item-selected,
.ant-menu-dark .ant-menu-item-selected {
  background-color: #262626 !important;
  color: #ffffff !important;
  border-right: 3px solid #ffffff !important;
}

/* Hover state for non-selected items */
.ant-menu-dark .ant-menu-item:hover:not(.ant-menu-item-selected) {
  background-color: #434343 !important;
  color: #ffffff !important;
}

/* Submenu styling */
.ant-menu-dark .ant-menu-submenu {
  background-color: #595959 !important;
}

.ant-menu-dark .ant-menu-submenu-title {
  background-color: #595959 !important;
  color: #ffffff !important;
}

.ant-menu-dark .ant-menu-submenu-title:hover {
  background-color: #434343 !important;
  color: #ffffff !important;
}

/* Submenu items */
.ant-menu-dark .ant-menu-submenu .ant-menu-item {
  background-color: #595959 !important;
  color: #ffffff !important;
  padding-left: 48px !important;
}

.ant-menu-dark .ant-menu-submenu .ant-menu-item.ant-menu-item-selected,
.ant-menu-dark .ant-menu-submenu .ant-menu-item-selected {
  background-color: #262626 !important;
  color: #ffffff !important;
  border-right: 3px solid #ffffff !important;
}

.ant-menu-dark .ant-menu-submenu .ant-menu-item:hover:not(.ant-menu-item-selected) {
  background-color: #434343 !important;
  color: #ffffff !important;
}

/* ===== VIDEO PLAYER COMPONENTS ===== */
.video-player-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto 24px;
  background: #000;
  border-radius: 6px;
  overflow: hidden;
}

.video-player-wrapper {
  position: relative;
  width: 100%;
  height: 450px;
  min-height: 300px;
  max-height: 600px;
}

.react-player {
  position: absolute;
  top: 0;
  left: 0;
}

/* ===== QUIZ COMPONENTS ===== */
.quiz-container {
  max-width: 800px;
  margin: 0 auto;
}

.quiz-question {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #595959;
}

.quiz-question-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #262626;
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quiz-option {
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.quiz-option:hover {
  border-color: #595959;
  background: #f5f5f5;
}

.quiz-option.selected {
  border-color: #595959;
  background: #e8e8e8;
  color: #595959;
}

/* ===== CERTIFICATE COMPONENTS ===== */
.certificate-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 40px;
  background: white;
  border: 2px solid #595959;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.certificate-header {
  font-size: 28px;
  font-weight: bold;
  color: #595959;
  margin-bottom: 20px;
}

.certificate-title {
  font-size: 20px;
  color: #262626;
  margin-bottom: 16px;
}

.certificate-recipient {
  font-size: 24px;
  font-weight: bold;
  color: #595959;
  margin: 20px 0;
}

.certificate-details {
  font-size: 14px;
  color: #8c8c8c;
  margin-top: 20px;
}

/* Print styles for certificates - moved to Certificate.jsx component */
/* Commented out to avoid conflicts with component-specific print styles
@media print {
  .certificate-container {
    max-width: none !important;
    margin: 0 !important;
    padding: 20px !important;
    border: 2px solid #595959 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    page-break-inside: avoid;
    background: white !important;
  }

  .certificate-header {
    font-size: 24px !important;
    color: #595959 !important;
  }

  .certificate-recipient {
    font-size: 20px !important;
    color: #595959 !important;
  }

  .certificate-details {
    font-size: 12px !important;
    color: #8c8c8c !important;
  }
}
*/

/* ===== ADMIN COMPONENTS ===== */
.admin-section {
  margin-bottom: 32px;
}

.admin-section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 20px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #595959;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #595959;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

/* ===== LOGIN PAGE STYLES ===== */
.login-page {
  background-color: #ffffff !important;
}

.login-card {
  border: 1px solid #d9d9d9 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.login-card .ant-input {
  border-color: #d9d9d9;
  border-radius: 6px;
}

.login-card .ant-input:hover {
  border-color: #595959;
}

.login-card .ant-input:focus,
.login-card .ant-input-focused {
  border-color: #595959;
  box-shadow: 0 0 0 2px rgba(89, 89, 89, 0.2);
}

.login-card .ant-btn-primary {
  background-color: #595959;
  border-color: #595959;
}

.login-card .ant-btn-primary:hover {
  background-color: #434343;
  border-color: #434343;
}

.login-card .ant-btn-primary:active {
  background-color: #262626;
  border-color: #262626;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large screens (desktop) */
@media (min-width: 992px) {
  .app-content {
    margin: 24px;
    margin-left: 274px; /* 250px sidebar width + 24px margin */
    margin-top: 88px; /* 64px header height + 24px margin */
  }

  .app-content.sidebar-collapsed {
    margin-left: 104px; /* 80px collapsed sidebar width + 24px margin */
    margin-top: 88px;
  }
}

/* Medium screens (tablets) */
@media (max-width: 991px) {
  .app-content {
    margin: 16px;
    margin-top: 80px; /* 64px header height + 16px margin */
    margin-left: 16px;
    padding: 16px;
  }

  .app-content.sidebar-collapsed {
    margin-left: 16px;
    margin-top: 80px;
  }

  /* Header adjustments for tablets */
  .ant-layout-header,
  .app-header {
    padding: 0 16px;
  }

  .app-logo {
    margin-right: 16px;
  }

  .app-header .ant-space {
    gap: 12px !important;
  }
}

@media (max-width: 992px) and (min-width: 769px) {
  .video-player-wrapper {
    height: 350px;
  }

  /* Header adjustments for tablet range */
  .app-header .ant-typography {
    font-size: 13px;
  }

  .language-switch-container .ant-select {
    min-width: 80px !important;
  }
}

/* Small screens (mobile) */
@media (max-width: 768px) {
  /* Header responsive styles */
  .ant-layout-header,
  .app-header {
    padding: 0 12px;
    flex-wrap: nowrap;
    overflow: hidden;
  }

  .app-logo {
    margin-right: 12px;
    min-width: 0;
    flex-shrink: 1;
  }

  .app-logo-text {
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .app-logo-image {
    height: 32px;
    margin-right: 8px;
  }

  /* Header right section */
  .app-header .ant-space {
    gap: 8px !important;
    flex-wrap: nowrap;
    min-width: 0;
  }

  .app-header .ant-space .ant-space-item {
    flex-shrink: 0;
  }

  /* Welcome text - hide on very small screens */
  .app-header .ant-typography {
    display: none;
  }

  .video-player-container {
    margin-bottom: 16px;
  }

  .video-player-wrapper {
    height: 250px;
    min-height: 200px;
  }

  .certificate-container {
    padding: 24px;
    margin: 16px;
  }

  .certificate-header {
    font-size: 24px;
  }

  .certificate-recipient {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Very small screens (phones in portrait) */
@media (max-width: 480px) {
  .ant-layout-header,
  .app-header {
    padding: 0 8px;
  }

  .app-logo-text {
    font-size: 14px;
  }

  .app-logo-image {
    height: 28px;
    margin-right: 6px;
  }

  .app-header .ant-space {
    gap: 4px !important;
  }

  /* Language switch adjustments for very small screens */
  .language-switch-container {
    height: 24px !important;
    padding: 1px 4px !important;
  }

  .language-switch-container .ant-select {
    min-width: 70px !important;
    font-size: 11px !important;
  }

  .language-switch-container .anticon {
    font-size: 12px !important;
  }
}

/* ===== UI STATE COMPONENTS ===== */

/* Loading states */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-image-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  gap: 16px;
}

.loading-image {
  animation: pulse 0.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.loading-text {
  margin-left: 12px;
  color: #8c8c8c;
}

/* Error states */
.error-container {
  text-align: center;
  padding: 40px;
  color: #ff4d4f;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  margin-bottom: 16px;
}

/* Success states */
.success-container {
  text-align: center;
  padding: 40px;
  color: #52c41a;
}

.success-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* ===== FORM COMPONENTS ===== */
.form-section {
  margin-bottom: 24px;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #262626;
}

/* ===== TABLE COMPONENTS ===== */
.table-actions {
  display: flex;
  gap: 8px;
}

.table-status {
  font-weight: 600;
}

.status-completed {
  color: #52c41a;
}

.status-pending {
  color: #faad14;
}

.status-failed {
  color: #ff4d4f;
}

/* ===== QUIZ TIMER ANIMATIONS ===== */
@keyframes urgent-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
