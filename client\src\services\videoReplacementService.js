/**
 * Video Replacement Service with Chunked Upload Support
 * 
 * This service handles video replacement using the same chunked upload system
 * as regular video uploads, but with replacement-specific logic.
 */

import api from './api'

const CHUNK_SIZE = 10 * 1024 * 1024 // 10MB chunks

class VideoReplacementService {
  constructor() {
    this.file = null
    this.chunks = []
    this.uploadedChunks = new Set()
    this.uploadId = null
    this.aborted = false
    this.onProgress = null
    this.onError = null
    this.onComplete = null
  }

  /**
   * Calculate file chunks
   */
  calculateChunks(file) {
    const chunks = []
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE)
    
    for (let i = 0; i < totalChunks; i++) {
      const start = i * CHUNK_SIZE
      const end = Math.min(start + CHUNK_SIZE, file.size)
      chunks.push({
        index: i,
        start,
        end,
        size: end - start,
        blob: file.slice(start, end)
      })
    }
    
    return chunks
  }

  /**
   * Update progress and notify callback
   */
  updateProgress() {
    if (this.onProgress) {
      const progress = {
        uploadedChunks: this.uploadedChunks.size,
        totalChunks: this.chunks.length,
        uploadedBytes: this.uploadedChunks.size * CHUNK_SIZE,
        totalBytes: this.file.size,
        percentage: Math.round((this.uploadedChunks.size / this.chunks.length) * 100)
      }
      this.onProgress(progress)
    }
  }

  /**
   * Handle errors
   */
  handleError(message, error) {
    console.error('Video replacement error:', message, error)
    if (this.onError) {
      this.onError(message, error)
    }
  }

  /**
   * Delay utility for retries
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Initialize chunked replacement upload
   */
  async initiate(moduleId, languageCode, file, metadata) {
    try {
      this.file = file
      this.chunks = this.calculateChunks(file)
      this.uploadedChunks.clear()
      this.aborted = false

      const response = await api.post(`/training/modules/${moduleId}/videos/${languageCode}/replace/initiate`, {
        filename: file.name,
        fileSize: file.size,
        mimeType: file.type,
        totalChunks: this.chunks.length,
        chunkSize: CHUNK_SIZE,
        ...metadata
      })

      this.uploadId = response.data.uploadId
      
      console.log('Replacement upload initiated:', {
        uploadId: this.uploadId,
        totalChunks: this.chunks.length,
        fileSize: file.size,
        moduleId,
        languageCode
      })
      
      return {
        uploadId: this.uploadId,
        totalChunks: this.chunks.length,
        fileSize: file.size
      }
    } catch (error) {
      this.handleError('Failed to initiate replacement upload', error)
      throw error
    }
  }

  /**
   * Upload a single chunk with retry logic
   */
  async uploadChunk(moduleId, languageCode, chunkIndex, retryCount = 0) {
    if (this.aborted) {
      throw new Error('Upload aborted')
    }

    const chunk = this.chunks[chunkIndex]
    const formData = new FormData()
    formData.append('chunk', chunk.blob)
    formData.append('uploadId', this.uploadId)
    formData.append('chunkIndex', chunkIndex)
    formData.append('totalChunks', this.chunks.length)

    try {
      await api.post(`/training/modules/${moduleId}/videos/${languageCode}/replace/chunk`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 0, // No timeout - infinite wait
      })

      this.uploadedChunks.add(chunkIndex)
      this.updateProgress()
      
      return true
    } catch (error) {
      // Retry logic for failed chunks
      if (retryCount < 3) {
        console.warn(`Replacement chunk ${chunkIndex} failed, retrying (${retryCount + 1}/3)...`)
        await this.delay(1000 * (retryCount + 1)) // Exponential backoff
        return this.uploadChunk(moduleId, languageCode, chunkIndex, retryCount + 1)
      } else {
        this.handleError(`Failed to upload replacement chunk ${chunkIndex}`, error)
        throw error
      }
    }
  }

  /**
   * Upload all chunks with concurrency control
   */
  async uploadAllChunks(moduleId, languageCode, concurrency = 3) {
    let activeUploads = 0
    let chunkIndex = 0
    const errors = []

    return new Promise((resolve, reject) => {
      const uploadNext = async () => {
        if (this.aborted) {
          reject(new Error('Upload aborted'))
          return
        }

        if (chunkIndex >= this.chunks.length) {
          if (activeUploads === 0) {
            if (errors.length > 0) {
              reject(new Error(`Failed to upload ${errors.length} chunks`))
            } else {
              resolve()
            }
          }
          return
        }

        const currentChunkIndex = chunkIndex++
        activeUploads++

        try {
          await this.uploadChunk(moduleId, languageCode, currentChunkIndex)
        } catch (error) {
          errors.push({ chunkIndex: currentChunkIndex, error })
        }

        activeUploads--
        uploadNext()
      }

      // Start concurrent uploads
      for (let i = 0; i < Math.min(concurrency, this.chunks.length); i++) {
        uploadNext()
      }
    })
  }

  /**
   * Finalize replacement upload and reassemble file
   */
  async finalize(moduleId, languageCode, metadata) {
    try {
      const response = await api.post(`/training/modules/${moduleId}/videos/${languageCode}/replace/finalize`, {
        uploadId: this.uploadId,
        ...metadata
      })

      if (this.onComplete) {
        this.onComplete(response.data)
      }

      console.log('Video replacement completed:', response.data)

      return response.data
    } catch (error) {
      this.handleError('Failed to finalize replacement upload', error)
      throw error
    }
  }

  /**
   * Cancel replacement upload
   */
  async cancel(moduleId, languageCode) {
    try {
      this.aborted = true
      
      if (this.uploadId) {
        await api.post(`/training/modules/${moduleId}/videos/${languageCode}/replace/cancel`, {
          uploadId: this.uploadId
        })
      }
      
      console.log('Replacement upload cancelled')
    } catch (error) {
      console.error('Failed to cancel replacement upload:', error)
      throw error
    }
  }

  /**
   * Complete replacement upload process
   */
  async replace(moduleId, languageCode, file, metadata, options = {}) {
    try {
      // Set callbacks
      this.onProgress = options.onProgress
      this.onError = options.onError
      this.onComplete = options.onComplete

      console.log('Starting video replacement:', {
        moduleId,
        languageCode,
        filename: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)}MB`
      })

      // Step 1: Initiate replacement upload
      await this.initiate(moduleId, languageCode, file, metadata)
      
      // Step 2: Upload all chunks
      await this.uploadAllChunks(moduleId, languageCode, options.concurrency || 3)
      
      // Step 3: Finalize replacement upload
      const result = await this.finalize(moduleId, languageCode, metadata)
      
      return result
    } catch (error) {
      // Try to cancel on error
      try {
        await this.cancel(moduleId, languageCode)
      } catch (cancelError) {
        console.error('Failed to cancel after error:', cancelError)
      }
      throw error
    }
  }

  /**
   * Abort current replacement upload
   */
  async abort(moduleId, languageCode) {
    return this.cancel(moduleId, languageCode)
  }
}

export default VideoReplacementService
