import React, { useState } from 'react'
import { Card, Form, Input, Button, Typography, Space, Alert, message, Divider, Table, Tag, Row, Col } from 'antd'
import { UserOutlined, LockOutlined, BookOutlined, TrophyOutlined } from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useTranslation } from 'react-i18next'
import { usersAPI, handleAPIError } from '../services/api'
import { useAuth } from '../contexts/AuthContext'
import LoadingImage from '../components/LoadingImage'

const { Title, Text } = Typography

function Profile() {
  const [passwordForm] = Form.useForm()
  const [changingPassword, setChangingPassword] = useState(false)
  const { user, changePassword } = useAuth()
  const queryClient = useQueryClient()
  const { t } = useTranslation()

  // Extract full name from email using the same method as server
  const extractNameFromEmail = (email) => {
    try {
      const localPart = email.split('@')[0];

      const capitalizeFirst = (str) => {
        if (!str || typeof str !== 'string') return '';
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      };

      if (localPart.includes('.')) {
        // <EMAIL> -> John Doe (full name)
        const parts = localPart.split('.');
        const firstName = capitalizeFirst(parts[0]);
        const lastName = capitalizeFirst(parts[1] || '');
        return `${firstName} ${lastName}`.trim();
      } else if (localPart.includes('_')) {
        // <EMAIL> -> John Doe (full name)
        const parts = localPart.split('_');
        const firstName = capitalizeFirst(parts[0]);
        const lastName = capitalizeFirst(parts[1] || '');
        return `${firstName} ${lastName}`.trim();
      } else {
        // <EMAIL> -> Johndoe (everything before @ as first name)
        return capitalizeFirst(localPart);
      }
    } catch (error) {
      return 'Training Participant';
    }
  };

  // Fetch user profile with progress
  const { data: profileData, isLoading, error } = useQuery({
    queryKey: ['user-profile', user?.email],
    queryFn: usersAPI.getProfile,
    select: (response) => response.data,
    enabled: !!user?.email,
  })

  const handlePasswordChange = async (values) => {
    setChangingPassword(true)
    const result = await changePassword(values)
    
    if (result.success) {
      passwordForm.resetFields()
      message.success(t('profile.passwordChangedSuccessfully'))
    }
    
    setChangingPassword(false)
  }

  if (isLoading) {
    return <LoadingImage text={t('profile.loadingProfile')} />
  }

  if (error) {
    const errorInfo = handleAPIError(error)
    return (
      <Alert
        message={t('profile.errorLoadingProfile')}
        description={errorInfo.message}
        type="error"
        showIcon
      />
    )
  }

  const { user: userProfile, progress } = profileData

  // Prepare progress data for table
  const progressColumns = [
    {
      title: t('profile.trainingModule'),
      dataIndex: 'module_title',
      key: 'module_title',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: t('profile.language'),
      dataIndex: 'language_code',
      key: 'language_code',
      render: (code) => <Tag color="blue">{code.toUpperCase()}</Tag>
    },
    {
      title: t('profile.videoStatus'),
      dataIndex: 'video_watched',
      key: 'video_watched',
      render: (watched) => (
        <Tag color={watched ? 'success' : 'default'}>
          {watched ? t('profile.completed') : t('profile.pending')}
        </Tag>
      )
    },
    {
      title: t('profile.quizStatus'),
      dataIndex: 'quiz_passed',
      key: 'quiz_status',
      render: (passed, record) => {
        if (record.certificate_generated) {
          return <Tag color="success" icon={<TrophyOutlined />}>{t('profile.certified')}</Tag>
        }
        if (passed) {
          return <Tag color="success">{t('profile.passed')}</Tag>
        }
        if (record.quiz_completed) {
          return <Tag color="error">{t('profile.failed')}</Tag>
        }
        return <Tag color="default">{t('profile.pending')}</Tag>
      }
    },
    {
      title: t('profile.score'),
      dataIndex: 'quiz_score',
      key: 'quiz_score',
      render: (score) => score ? `${score}%` : '-'
    },
    {
      title: t('profile.completionDate'),
      dataIndex: 'quiz_completed_at',
      key: 'completion_date',
      render: (date) => date ? new Date(date).toLocaleDateString() : '-'
    }
  ]

  const completedTrainings = progress.filter(p => p.certificate_generated).length
  const totalTrainings = progress.length
  const averageScore = progress.length > 0 
    ? Math.round(progress.reduce((sum, p) => sum + (p.quiz_score || 0), 0) / progress.length)
    : 0

  return (
    <div>
      <Title level={2}>{t('profile.myProfile')}</Title>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          {/* User Information */}
          <Card title={t('profile.userInformation')} style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text type="secondary">{t('profile.emailAddress')}</Text>
                <br />
                <Text strong>{userProfile.email}</Text>
              </div>

              <div>
                <Text type="secondary">{t('profile.firstName')}</Text>
                <br />
                <Text strong>
                  {userProfile.first_name || t('profile.notProvided')}
                </Text>
              </div>
              <div>
                <Text type="secondary">{t('profile.lastName')}</Text>
                <br />
                <Text strong>
                  {userProfile.last_name || t('profile.notProvided')}
                </Text>
              </div>
              <div>
                <Text type="secondary">{t('profile.fullName')}</Text>
                <br />
                <Text strong>
                  {userProfile.full_name || t('profile.notProvided')}
                </Text>
              </div>

              <div>
                <Text type="secondary">{t('profile.accountType')}</Text>
                <br />
                <Tag color={userProfile.is_admin ? 'gold' : 'blue'}>
                  {userProfile.is_admin ? t('profile.administrator') : t('profile.user')}
                </Tag>
              </div>

              <div>
                <Text type="secondary">{t('profile.memberSince')}</Text>
                <br />
                <Text>{new Date(userProfile.created_at).toLocaleDateString()}</Text>
              </div>

              {userProfile.last_login && (
                <div>
                  <Text type="secondary">{t('profile.lastLogin')}</Text>
                  <br />
                  <Text>{new Date(userProfile.last_login).toLocaleString()}</Text>
                </div>
              )}
            </Space>
          </Card>

          {/* Training Statistics */}
          <Card title={t('profile.trainingStatistics')}>
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-number">{completedTrainings}</div>
                <div className="stat-label">{t('profile.completedTrainings')}</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">{totalTrainings}</div>
                <div className="stat-label">{t('profile.totalEnrolled')}</div>
              </div>
              <div className="stat-card">
                <div className="stat-number">{averageScore}%</div>
                <div className="stat-label">{t('profile.averageScore')}</div>
              </div>
            </div>
          </Card>
        </Col>

        {/* Change Password - Admin Only */}
        {user?.isAdmin && (
          <Col xs={24} lg={12}>
            <Card title={t('profile.changePasswordAdminOnly')}>
              <Form
                form={passwordForm}
                layout="vertical"
                onFinish={handlePasswordChange}
              >
                <Form.Item
                  name="currentPassword"
                  label={t('profile.currentPassword')}
                  rules={[
                    { required: true, message: t('profile.pleaseEnterCurrentPassword') }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder={t('profile.enterCurrentPassword')}
                  />
                </Form.Item>

                <Form.Item
                  name="newPassword"
                  label={t('profile.newPassword')}
                  rules={[
                    { required: true, message: t('profile.pleaseEnterNewPassword') },
                    { min: 6, message: t('profile.passwordMinLength') }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder={t('profile.enterNewPassword')}
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  label={t('profile.confirmNewPassword')}
                  dependencies={['newPassword']}
                  rules={[
                    { required: true, message: t('profile.pleaseConfirmNewPassword') },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('newPassword') === value) {
                          return Promise.resolve()
                        }
                        return Promise.reject(new Error(t('profile.passwordsDoNotMatch')))
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder={t('profile.confirmNewPassword')}
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={changingPassword}
                    block
                  >
                    {t('profile.changePassword')}
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </Col>
        )}

        {/* Password Information for Non-Admin Users */}
        {!user?.isAdmin && (
          <Col xs={24} lg={12}>
            <Card title={t('profile.passwordInformation')}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Alert
                  message={t('profile.passwordManagement')}
                  description={t('profile.passwordManagementDescription')}
                  type="info"
                  showIcon
                />
                <div style={{ textAlign: 'center', padding: 20 }}>
                  <LockOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                  <div style={{ color: '#8c8c8c' }}>
                    {t('profile.passwordManagedByAdmins')}
                  </div>
                </div>
              </Space>
            </Card>
          </Col>
        )}
      </Row>

      <Divider />

      {/* Training Progress */}
      <Card title={t('profile.trainingProgress')} style={{ marginTop: 16 }}>
        {progress.length === 0 ? (
          <div style={{ textAlign: 'center', padding: 40 }}>
            <BookOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
            <Title level={4} type="secondary">{t('profile.noTrainingProgress')}</Title>
            <Text type="secondary">
              {t('profile.noTrainingProgressDescription')}
            </Text>
          </div>
        ) : (
          <Table
            columns={progressColumns}
            dataSource={progress}
            rowKey={(record) => `${record.module_title}-${record.language_code}`}
            pagination={false}
            size="middle"
          />
        )}
      </Card>
    </div>
  )
}

export default Profile
