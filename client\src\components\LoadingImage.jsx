import React from 'react'
import tl1LoadingImage from '../assets/tl1-loading.png'

function LoadingImage({ text, size = 'large' }) {
  const imageSize = size === 'large' ? 80 : size === 'medium' ? 60 : 40

  return (
    <div className="loading-image-container">
      <img 
        src={tl1LoadingImage} 
        alt="Loading..." 
        className="loading-image"
        style={{ 
          width: imageSize, 
          height: imageSize 
        }}
      />
      {text && (
        <span className="loading-text">{text}</span>
      )}
    </div>
  )
}

export default LoadingImage
