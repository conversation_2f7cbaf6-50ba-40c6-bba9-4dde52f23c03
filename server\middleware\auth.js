// Authentication middleware
const jwt = require('jsonwebtoken');
const config = require('../../config/environment');

// Require user to be authenticated
function requireAuth(req, res, next) {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      error: 'Authentication required',
      message: 'Please log in to access this resource'
    });
  }

  try {
    const jwtSecret = config.get('security').jwtSecret;
    const decoded = jwt.verify(token, jwtSecret);
    req.user = decoded; // Store user info in request
    next();
  } catch (error) {
    console.error('JWT verification failed:', error.message);
    return res.status(401).json({
      error: 'Invalid token',
      message: 'Please log in again'
    });
  }
}

// Require user to be an admin
function requireAdmin(req, res, next) {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      error: 'Authentication required',
      message: 'Please log in to access this resource'
    });
  }

  try {
    const jwtSecret = config.get('security').jwtSecret;
    const decoded = jwt.verify(token, jwtSecret);
    req.user = decoded; // Store user info in request

    if (!decoded.isAdmin) {
      return res.status(403).json({
        error: 'Admin access required',
        message: 'You do not have permission to access this resource'
      });
    }
    next();
  } catch (error) {
    console.error('JWT verification failed:', error.message);
    return res.status(401).json({
      error: 'Invalid token',
      message: 'Please log in again'
    });
  }
}

// Optional authentication (user may or may not be logged in)
function optionalAuth(req, res, next) {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    // No token provided, continue without user info
    next();
    return;
  }

  try {
    const jwtSecret = process.env.JWT_SECRET || 'fallback_jwt_secret_change_in_production';
    const decoded = jwt.verify(token, jwtSecret);
    req.user = decoded; // Store user info in request
    next();
  } catch (error) {
    // Invalid token, but continue without user info
    console.error('Optional JWT verification failed:', error.message);
    next();
  }
}

module.exports = {
  requireAuth,
  requireAdmin,
  optionalAuth,
};
