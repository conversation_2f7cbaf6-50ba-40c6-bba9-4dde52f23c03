-- Create database schema for TradeLink Internal Training System

-- Users table (email as primary key)
CREATE TABLE IF NOT EXISTS users (
    email VARCHAR(255) PRIMARY KEY,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    full_name <PERSON><PERSON><PERSON><PERSON>(200),
    is_admin <PERSON><PERSON>OLEAN DEFAULT FALSE,
    language VARCHAR(10) DEFAULT 'en', -- User's preferred language (en, zh, etc.)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    password_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_default_admin BOOLEAN DEFAULT FALSE
);

-- Training modules table
CREATE TABLE IF NOT EXISTS training_modules (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Training videos table (multilingual support)
CREATE TABLE IF NOT EXISTS training_videos (
    id SERIAL PRIMARY KEY,
    module_id INTEGER REFERENCES training_modules(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL, -- e.g., 'en', 'zh', 'es'
    language_name VARCHAR(50) NOT NULL, -- e.g., 'English', 'Chinese', 'Spanish'
    video_filename VARCHAR(255) NOT NULL,
    video_url VARCHAR(500),
    duration_seconds INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(module_id, language_code)
);

-- Quizzes table (now associated with videos instead of modules)
CREATE TABLE IF NOT EXISTS quizzes (
    id SERIAL PRIMARY KEY,
    video_id INTEGER REFERENCES training_videos(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    passing_score INTEGER DEFAULT 70, -- Percentage required to pass
    time_limit_minutes INTEGER, -- Optional time limit
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(video_id) -- One quiz per video
);

-- Quiz questions table
CREATE TABLE IF NOT EXISTS quiz_questions (
    id SERIAL PRIMARY KEY,
    quiz_id INTEGER REFERENCES quizzes(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_type VARCHAR(20) NOT NULL CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'multi_choice')),
    question_order INTEGER NOT NULL,
    points INTEGER DEFAULT 1,
    question_image TEXT, -- Base64 encoded image data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Quiz question options table (for multiple choice questions)
CREATE TABLE IF NOT EXISTS quiz_question_options (
    id SERIAL PRIMARY KEY,
    question_id INTEGER REFERENCES quiz_questions(id) ON DELETE CASCADE,
    option_text TEXT NOT NULL,
    is_correct BOOLEAN DEFAULT FALSE,
    option_order INTEGER NOT NULL,
    option_image TEXT, -- Base64 encoded image data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User progress table
CREATE TABLE IF NOT EXISTS user_progress (
    id SERIAL PRIMARY KEY,
    user_email VARCHAR(255) REFERENCES users(email) ON DELETE CASCADE,
    module_id INTEGER REFERENCES training_modules(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL,
    video_watched BOOLEAN DEFAULT FALSE,
    video_watch_time_seconds INTEGER DEFAULT 0,
    video_completed_at TIMESTAMP,
    quiz_completed BOOLEAN DEFAULT FALSE,
    quiz_passed BOOLEAN DEFAULT FALSE,
    quiz_score INTEGER,
    quiz_completed_at TIMESTAMP,
    certificate_generated BOOLEAN DEFAULT FALSE,
    certificate_generated_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_email, module_id, language_code)
);

-- Quiz attempts table
CREATE TABLE IF NOT EXISTS quiz_attempts (
    id SERIAL PRIMARY KEY,
    user_email VARCHAR(255) REFERENCES users(email) ON DELETE CASCADE,
    quiz_id INTEGER REFERENCES quizzes(id) ON DELETE CASCADE,
    attempt_number INTEGER NOT NULL,
    score INTEGER NOT NULL,
    total_questions INTEGER NOT NULL,
    passed BOOLEAN NOT NULL,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    time_taken_seconds INTEGER,
    answers JSONB -- Store user answers as JSON
);

-- Quiz attempt answers table (detailed answer tracking)
CREATE TABLE IF NOT EXISTS quiz_attempt_answers (
    id SERIAL PRIMARY KEY,
    attempt_id INTEGER REFERENCES quiz_attempts(id) ON DELETE CASCADE,
    question_id INTEGER REFERENCES quiz_questions(id) ON DELETE CASCADE,
    selected_option_id INTEGER REFERENCES quiz_question_options(id),
    selected_option_ids INTEGER[], -- Array for multi-choice questions
    answer_text TEXT, -- For short answer questions
    is_correct BOOLEAN NOT NULL,
    points_earned INTEGER DEFAULT 0,
    answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Certificates table
CREATE TABLE IF NOT EXISTS certificates (
    id SERIAL PRIMARY KEY,
    user_email VARCHAR(255) REFERENCES users(email) ON DELETE CASCADE,
    module_id INTEGER REFERENCES training_modules(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL,
    certificate_number VARCHAR(100) UNIQUE NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP, -- Optional expiration date
    certificate_data JSONB -- Store certificate details as JSON
);

-- System configuration table
CREATE TABLE IF NOT EXISTS system_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    password_expiration_days INTEGER DEFAULT 8,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT single_system_config CHECK (id = 1)
);

-- Add timezone column if it doesn't exist (migration)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'system_config' AND column_name = 'timezone'
    ) THEN
        ALTER TABLE system_config ADD COLUMN timezone VARCHAR(50) DEFAULT 'Asia/Shanghai';
    END IF;
END $$;

-- Add max_file_size column if it doesn't exist (migration)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'system_config' AND column_name = 'max_file_size'
    ) THEN
        ALTER TABLE system_config ADD COLUMN max_file_size VARCHAR(20) DEFAULT '500MB';
    END IF;
END $$;

-- Email configuration table
CREATE TABLE IF NOT EXISTS email_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    email_enabled BOOLEAN DEFAULT FALSE,
    email_method VARCHAR(20) DEFAULT 'clipboard' CHECK (email_method IN ('clipboard', 'smtp')),
    smtp_host VARCHAR(255),
    smtp_port INTEGER DEFAULT 587,
    smtp_secure BOOLEAN DEFAULT FALSE,
    smtp_user VARCHAR(255),
    smtp_pass VARCHAR(255),
    email_from_name VARCHAR(255) DEFAULT 'TradeLink Training System',
    email_from_address VARCHAR(255),
    email_reply_to_address VARCHAR(255), -- Reply-to address for send-only accounts
    email_cc_addresses TEXT, -- Comma-separated list of CC email addresses
    email_template_subject_en VARCHAR(500) DEFAULT 'Your TradeLink Training System Login Credentials',
    email_template_body_en TEXT DEFAULT 'Dear {{fullName}},

Your account has been created for the TradeLink Internal Training System.

Login Credentials:
Email: {{email}}
Password: {{password}}

Important Security Information:
• Your password will expire in {{passwordExpirationDays}} days
• Please log in promptly to begin your training
• Keep your credentials secure and do not share them
• Contact your administrator if you need assistance

If you have any questions or need assistance, please contact your system administrator.

Best regards,
TradeLink Training System Administration

---
This is an automated message. Please do not reply to this email.',
    email_template_subject_zh VARCHAR(500) DEFAULT '您的TradeLink培训系统登录凭据',
    email_template_body_zh TEXT DEFAULT '亲爱的 {{fullName}}，

您的TradeLink内部培训系统账户已创建。

登录凭据：
邮箱：{{email}}
密码：{{password}}

重要安全信息：
• 您的密码将在{{passwordExpirationDays}}天后过期
• 请及时登录开始您的培训
• 请保护您的凭据安全，不要与他人分享
• 如需帮助请联系您的管理员

如果您有任何问题或需要帮助，请联系您的系统管理员。

此致，
TradeLink培训系统管理员

---
这是一封自动发送的邮件，请勿回复。',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT single_config CHECK (id = 1)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_progress_user_email ON user_progress(user_email);
CREATE INDEX IF NOT EXISTS idx_user_progress_module_id ON user_progress(module_id);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_user_email ON quiz_attempts(user_email);
CREATE INDEX IF NOT EXISTS idx_quiz_attempts_quiz_id ON quiz_attempts(quiz_id);
CREATE INDEX IF NOT EXISTS idx_certificates_user_email ON certificates(user_email);
CREATE INDEX IF NOT EXISTS idx_training_videos_module_language ON training_videos(module_id, language_code);
CREATE INDEX IF NOT EXISTS idx_quizzes_video_id ON quizzes(video_id);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply the trigger to relevant tables (with IF NOT EXISTS equivalent)
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_training_modules_updated_at ON training_modules;
CREATE TRIGGER update_training_modules_updated_at BEFORE UPDATE ON training_modules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_training_videos_updated_at ON training_videos;
CREATE TRIGGER update_training_videos_updated_at BEFORE UPDATE ON training_videos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_quizzes_updated_at ON quizzes;
CREATE TRIGGER update_quizzes_updated_at BEFORE UPDATE ON quizzes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_progress_updated_at ON user_progress;
CREATE TRIGGER update_user_progress_updated_at BEFORE UPDATE ON user_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_email_config_updated_at ON email_config;
CREATE TRIGGER update_email_config_updated_at BEFORE UPDATE ON email_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_system_config_updated_at ON system_config;
CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add password_created_at column if it doesn't exist (for existing databases)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'password_created_at') THEN
        ALTER TABLE users ADD COLUMN password_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        -- Set password_created_at for existing users to their created_at date
        UPDATE users SET password_created_at = created_at WHERE password_created_at IS NULL;
    END IF;
END $$;

-- Add language column if it doesn't exist (for existing databases)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'language') THEN
        ALTER TABLE users ADD COLUMN language VARCHAR(10) DEFAULT 'en';
        -- Set default language for existing users
        UPDATE users SET language = 'en' WHERE language IS NULL;
    END IF;
END $$;

-- Add full_name column if it doesn't exist (for existing databases)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'full_name') THEN
        ALTER TABLE users ADD COLUMN full_name VARCHAR(200);
    END IF;
END $$;

-- Function to extract names from email and return as record
CREATE OR REPLACE FUNCTION extract_names_from_email(email_address TEXT)
RETURNS TABLE(first_name TEXT, last_name TEXT, full_name TEXT) AS $$
DECLARE
    local_part TEXT;
    parts TEXT[];
    fname TEXT;
    lname TEXT;
BEGIN
    -- Get the part before @ symbol
    local_part := split_part(email_address, '@', 1);

    -- Handle common email patterns
    IF position('.' in local_part) > 0 THEN
        -- <EMAIL> -> First: John, Last: Doe
        parts := string_to_array(local_part, '.');
        fname := initcap(parts[1]);
        lname := CASE WHEN array_length(parts, 1) > 1 THEN initcap(parts[2]) ELSE '' END;
    ELSIF position('_' in local_part) > 0 THEN
        -- <EMAIL> -> First: John, Last: Doe
        parts := string_to_array(local_part, '_');
        fname := initcap(parts[1]);
        lname := CASE WHEN array_length(parts, 1) > 1 THEN initcap(parts[2]) ELSE '' END;
    ELSE
        -- <EMAIL> -> First: '', Last: Kelvin21706
        fname := '';
        lname := initcap(local_part);
    END IF;

    -- Return the extracted names
    first_name := fname;
    last_name := lname;
    full_name := trim(CASE WHEN fname != '' THEN fname || ' ' || lname ELSE lname END);

    RETURN NEXT;
EXCEPTION
    WHEN OTHERS THEN
        first_name := '';
        last_name := 'User';
        full_name := 'User';
        RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function to automatically set names
CREATE OR REPLACE FUNCTION set_user_names()
RETURNS TRIGGER AS $$
DECLARE
    extracted_names RECORD;
BEGIN
    -- If first_name and last_name are provided, use them
    IF NEW.first_name IS NOT NULL AND NEW.last_name IS NOT NULL AND
       (trim(NEW.first_name) != '' OR trim(NEW.last_name) != '') THEN
        NEW.full_name := trim(NEW.first_name || ' ' || NEW.last_name);
    ELSE
        -- Extract names from email
        SELECT * INTO extracted_names FROM extract_names_from_email(NEW.email);

        -- Only update if the fields are empty
        IF NEW.first_name IS NULL OR trim(NEW.first_name) = '' THEN
            NEW.first_name := extracted_names.first_name;
        END IF;

        IF NEW.last_name IS NULL OR trim(NEW.last_name) = '' THEN
            NEW.last_name := extracted_names.last_name;
        END IF;

        NEW.full_name := extracted_names.full_name;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for INSERT and UPDATE
DROP TRIGGER IF EXISTS set_user_names_trigger ON users;
CREATE TRIGGER set_user_names_trigger
    BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION set_user_names();

-- Update existing users' names
DO $$
DECLARE
    user_record RECORD;
    extracted_names RECORD;
BEGIN
    FOR user_record IN SELECT email, first_name, last_name FROM users LOOP
        -- Extract names from email
        SELECT * INTO extracted_names FROM extract_names_from_email(user_record.email);

        -- Update the user with extracted names
        UPDATE users
        SET
            first_name = COALESCE(NULLIF(user_record.first_name, ''), extracted_names.first_name),
            last_name = COALESCE(NULLIF(user_record.last_name, ''), extracted_names.last_name),
            full_name = extracted_names.full_name
        WHERE email = user_record.email;
    END LOOP;
END $$;

-- Update quiz_questions constraint to include multi_choice (for existing databases)
DO $$
BEGIN
    -- Check if the constraint exists and doesn't include multi_choice
    IF EXISTS (
        SELECT 1 FROM information_schema.check_constraints
        WHERE constraint_name = 'quiz_questions_question_type_check'
        AND check_clause NOT LIKE '%multi_choice%'
    ) THEN
        -- Drop the old constraint
        ALTER TABLE quiz_questions DROP CONSTRAINT quiz_questions_question_type_check;
        -- Add the new constraint with multi_choice
        ALTER TABLE quiz_questions ADD CONSTRAINT quiz_questions_question_type_check
        CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'multi_choice'));
    END IF;
END $$;

-- Add selected_option_ids column to quiz_attempt_answers if it doesn't exist (for existing databases)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'quiz_attempt_answers' AND column_name = 'selected_option_ids') THEN
        ALTER TABLE quiz_attempt_answers ADD COLUMN selected_option_ids INTEGER[];
    END IF;
END $$;

-- Insert default system configuration (handle timezone and max_file_size column migration)
DO $$
BEGIN
    -- Check if both timezone and max_file_size columns exist
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'system_config' AND column_name = 'timezone'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'system_config' AND column_name = 'max_file_size'
    ) THEN
        -- Insert with both columns if they exist
        INSERT INTO system_config (id, password_expiration_days, timezone, max_file_size)
        VALUES (1, 8, 'Asia/Shanghai', '500MB')
        ON CONFLICT (id) DO UPDATE SET
            timezone = COALESCE(system_config.timezone, 'Asia/Shanghai'),
            max_file_size = COALESCE(system_config.max_file_size, '500MB');
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'system_config' AND column_name = 'timezone'
    ) THEN
        -- Insert with timezone only if max_file_size doesn't exist yet
        INSERT INTO system_config (id, password_expiration_days, timezone)
        VALUES (1, 8, 'Asia/Shanghai')
        ON CONFLICT (id) DO UPDATE SET
            timezone = COALESCE(system_config.timezone, 'Asia/Shanghai');
    ELSE
        -- Insert basic config if neither column exists yet
        INSERT INTO system_config (id, password_expiration_days)
        VALUES (1, 8)
        ON CONFLICT (id) DO NOTHING;
    END IF;
END $$;

-- Add image support columns to existing databases
DO $$
BEGIN
    -- Add question_image column to quiz_questions table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'quiz_questions' AND column_name = 'question_image') THEN
        ALTER TABLE quiz_questions ADD COLUMN question_image TEXT;
    END IF;

    -- Add option_image column to quiz_question_options table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'quiz_question_options' AND column_name = 'option_image') THEN
        ALTER TABLE quiz_question_options ADD COLUMN option_image TEXT;
    END IF;
END $$;

-- Insert default admin user (password: admin123)
INSERT INTO users (email, password_hash, first_name, last_name, is_admin, password_created_at, is_default_admin)
VALUES ('<EMAIL>', '$2a$12$ptcWWXn2JA2zXxshYLpdWOR1HIzjmFogzxetUTowHNAVdG0r2FuMO', 'System', 'Administrator', TRUE, CURRENT_TIMESTAMP, TRUE)
ON CONFLICT (email) DO NOTHING;
