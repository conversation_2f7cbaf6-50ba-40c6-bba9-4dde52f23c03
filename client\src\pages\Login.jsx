import React, { useState } from 'react'
import { Form, Input, Button, Card, Typography, Space, Alert } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useAuth } from '../contexts/AuthContext'
import tradeLinkLogoColor from '../assets/tradelink-logo-color.png'

const { Title, Text } = Typography

function Login() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const { login } = useAuth()

  const onFinish = async (values) => {
    setLoading(true)
    setError(null)
    
    const result = await login(values)
    
    if (!result.success) {
      setError(result.error)
    }
    
    setLoading(false)
  }

  return (
    <div
      className="login-page"
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#ffffff',
        padding: '20px'
      }}
    >
      <Card
        className="login-card"
        style={{
          width: '100%',
          maxWidth: 420,
          borderRadius: 8,
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Space direction="vertical" size="medium">
            <img
              src={tradeLinkLogoColor}
              alt="TradeLink Logo"
              style={{
                height: 60,
                width: 'auto',
                marginBottom: 8
              }}
            />
            <Title level={2} style={{ margin: 0, color: '#262626', fontSize: 24 }}>
              {t('common.appTitle')}
            </Title>
            <Text type="secondary" style={{ fontSize: 16 }}>
              {t('auth.login')}
            </Text>
          </Space>
        </div>

        {error && (
          <Alert
            message={t('auth.loginError')}
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <Form
          name="login"
          onFinish={onFinish}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            label={t('auth.emailAddress')}
            rules={[
              { required: true, message: t('auth.pleaseEnterEmail') }
            ]}
          >
            <Input
              prefix={<UserOutlined style={{ color: '#8c8c8c' }} />}
              placeholder={t('auth.enterEmail')}
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label={t('auth.password')}
            rules={[
              { required: true, message: t('auth.pleaseEnterPassword') }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#8c8c8c' }} />}
              placeholder={t('auth.enterPassword')}
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{
                height: 48,
                fontSize: 16,
                fontWeight: 500,
                borderRadius: 6,
              }}
            >
              {t('auth.loginButton')}
            </Button>
          </Form.Item>
        </Form>

        <div style={{
          marginTop: 32,
          padding: 20,
          background: '#fafafa',
          borderRadius: 6,
          textAlign: 'center',
          border: '1px solid #f0f0f0'
        }}>
          <Text type="secondary" style={{ fontSize: 14, lineHeight: 1.5 }}>
            Please enter your credentials to access the training system.<br />
            For support or account issues, contact your system administrator.
          </Text>
        </div>
      </Card>
    </div>
  )
}

export default Login
