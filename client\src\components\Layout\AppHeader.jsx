import React from 'react'
import { Layout, Dropdown, Avatar, Space, Typography, Button } from 'antd'
import { UserOutlined, LogoutOutlined, SettingOutlined, MenuOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useAuth } from '../../contexts/AuthContext'
import LanguageSwitch from '../LanguageSwitch'
import tradeLinkLogoColor from '../../assets/tradelink-logo-color.png'

const { Header } = Layout
const { Text } = Typography

function AppHeader({ onToggleSidebar }) {
  const { t } = useTranslation()
  const { user, logout } = useAuth()
  const navigate = useNavigate()

  // User menu configuration
  const getUserMenuItems = () => [
    {
      key: 'profile',
      icon: <SettingOutlined />,
      label: t('auth.profileSettings'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('auth.logout'),
    },
  ]

  const handleMenuClick = ({ key }) => {
    switch (key) {
      case 'profile':
        navigate('/profile')
        break
      case 'logout':
        logout()
        break
      default:
        break
    }
  }

  const userMenuItems = getUserMenuItems()

  const displayName = user?.fullName || user?.email

  return (
    <Header className="app-header">
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        <Button
          type="text"
          icon={<MenuOutlined />}
          onClick={onToggleSidebar}
          style={{
            fontSize: '16px',
            width: 32,
            height: 32,
            color: '#000000'
          }}
        />
        <div className="app-logo">
          <img
            src={tradeLinkLogoColor}
            alt="TradeLink Logo"
            className="app-logo-image"
          />
          <span className="app-logo-text">{t('common.appTitle')}</span>
        </div>
      </div>

      <Space size="large">
        <LanguageSwitch
          showIcon={true}
          style={{ color: '#000000' }}
        />

        <Text style={{ color: '#000000' }}>
          {t('common.welcome')}, {user?.fullName || user?.email}
        </Text>

        <Dropdown
          menu={{ items: userMenuItems, onClick: handleMenuClick }}
          placement="bottomRight"
        >
          <Space style={{ cursor: 'pointer' }}>
            <Avatar
              size="small"
              icon={<UserOutlined />}
              style={{ backgroundColor: '#1890ff' }}
            />
            <Text style={{ color: '#000000' }}>
              {displayName}
            </Text>
          </Space>
        </Dropdown>
      </Space>
    </Header>
  )
}

export default AppHeader
