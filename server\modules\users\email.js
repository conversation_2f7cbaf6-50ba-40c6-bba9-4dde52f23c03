/**
 * Generate password reset email template
 */
function generatePasswordResetEmail(resetToken, userEmail) {
  const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}&email=${encodeURIComponent(userEmail)}`;
  
  return {
    subject: 'Password Reset Request - Internal Training System',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset Request</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
          }
          .content {
            background-color: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 5px 5px;
          }
          .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
          }
          .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #6c757d;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Password Reset Request</h1>
        </div>
        <div class="content">
          <p>Hello,</p>
          
          <p>We received a request to reset the password for your Internal Training System account associated with <strong>${userEmail}</strong>.</p>
          
          <p>To reset your password, click the button below:</p>
          
          <p style="text-align: center;">
            <a href="${resetUrl}" class="button">Reset Password</a>
          </p>
          
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background-color: #e9ecef; padding: 10px; border-radius: 3px;">
            ${resetUrl}
          </p>
          
          <div class="warning">
            <strong>Important:</strong>
            <ul>
              <li>This link will expire in 1 hour for security reasons</li>
              <li>If you didn't request this password reset, please ignore this email</li>
              <li>Your password will remain unchanged until you create a new one</li>
            </ul>
          </div>
          
          <p>If you're having trouble with the link above, contact your system administrator.</p>
          
          <p>Best regards,<br>Internal Training System</p>
        </div>
        <div class="footer">
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </body>
      </html>
    `,
    text: `
Password Reset Request - Internal Training System

Hello,

We received a request to reset the password for your Internal Training System account associated with ${userEmail}.

To reset your password, visit this link:
${resetUrl}

Important:
- This link will expire in 1 hour for security reasons
- If you didn't request this password reset, please ignore this email
- Your password will remain unchanged until you create a new one

If you're having trouble with the link above, contact your system administrator.

Best regards,
Internal Training System

This is an automated message. Please do not reply to this email.
    `
  };
}

/**
 * Generate welcome email template for new users
 */
function generateWelcomeEmail(userEmail, temporaryPassword) {
  const loginUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/login`;
  
  return {
    subject: 'Welcome to Internal Training System',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Internal Training System</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            background-color: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
          }
          .content {
            background-color: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 5px 5px;
          }
          .button {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
          }
          .credentials {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
          }
          .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #6c757d;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Welcome to Internal Training System!</h1>
        </div>
        <div class="content">
          <p>Hello,</p>
          
          <p>Your account has been created for the Internal Training System. You can now access training modules and complete quizzes.</p>
          
          <p><strong>Your login credentials:</strong></p>
          <div class="credentials">
            <strong>Email:</strong> ${userEmail}<br>
            <strong>Temporary Password:</strong> ${temporaryPassword}
          </div>
          
          <div class="warning">
            <strong>Important:</strong> Please change your password after your first login for security reasons.
          </div>
          
          <p style="text-align: center;">
            <a href="${loginUrl}" class="button">Login Now</a>
          </p>
          
          <p>If you're having trouble logging in, contact your system administrator.</p>
          
          <p>Best regards,<br>Internal Training System</p>
        </div>
        <div class="footer">
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </body>
      </html>
    `,
    text: `
Welcome to Internal Training System!

Hello,

Your account has been created for the Internal Training System. You can now access training modules and complete quizzes.

Your login credentials:
Email: ${userEmail}
Temporary Password: ${temporaryPassword}

Important: Please change your password after your first login for security reasons.

Login at: ${loginUrl}

If you're having trouble logging in, contact your system administrator.

Best regards,
Internal Training System

This is an automated message. Please do not reply to this email.
    `
  };
}

module.exports = {
  generatePasswordResetEmail,
  generateWelcomeEmail
};
