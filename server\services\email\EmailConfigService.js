const { query } = require('../../database/init');
const PasswordEncryptionService = require('./PasswordEncryptionService');

class EmailConfigService {
  constructor() {
    this.passwordService = new PasswordEncryptionService();
  }

  // Get email configuration from database or environment
  async getEmailConfig() {
    try {
      // Try to get from database first
      const result = await query('SELECT * FROM email_config WHERE id = 1');
      
      if (result.rows.length > 0) {
        return result.rows[0];
      }
      
      // Fallback to environment variables
      return {
        email_enabled: process.env.EMAIL_ENABLED === 'true',
        email_method: process.env.EMAIL_METHOD || 'clipboard',
        smtp_host: process.env.SMTP_HOST || '',
        smtp_port: process.env.SMTP_PORT || '587',
        smtp_secure: process.env.SMTP_SECURE || 'false',
        smtp_user: process.env.SMTP_USER || '',
        smtp_pass: process.env.SMTP_PASS || '',
        email_from_name: process.env.EMAIL_FROM_NAME || 'TradeLink Training System',
        email_from_address: process.env.EMAIL_FROM_ADDRESS || ''
      };
    } catch (error) {
      console.error('Error getting email config:', error);
      // Return default config
      return {
        email_enabled: false,
        email_method: 'clipboard',
        smtp_host: '',
        smtp_port: '587',
        smtp_secure: 'false',
        smtp_user: '',
        smtp_pass: '',
        email_from_name: 'TradeLink Training System',
        email_from_address: ''
      };
    }
  }

  // Update email configuration
  async updateEmailConfig(config) {
    try {
      const {
        email_enabled,
        email_method,
        smtp_host,
        smtp_port,
        smtp_secure,
        smtp_user,
        smtp_pass,
        email_from_name,
        email_from_address,
        email_reply_to_address,
        email_cc_addresses,
        email_template_subject_en,
        email_template_body_en,
        email_template_subject_zh,
        email_template_body_zh
      } = config;

      // Encrypt sensitive data before storing
      const encryptedPassword = this.passwordService.encryptPassword(smtp_pass);
      const encryptedFromAddress = this.passwordService.encryptPassword(email_from_address);

      // Get default templates
      const defaultTemplateEn = this.getDefaultEmailTemplate('en');
      const defaultTemplateZh = this.getDefaultEmailTemplate('zh');

      // Upsert configuration
      await query(`
        INSERT INTO email_config (
          id, email_enabled, email_method, smtp_host, smtp_port, smtp_secure,
          smtp_user, smtp_pass, email_from_name, email_from_address, email_reply_to_address, email_cc_addresses,
          email_template_subject_en, email_template_body_en, email_template_subject_zh, email_template_body_zh, updated_at
        ) VALUES (1, $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CURRENT_TIMESTAMP)
        ON CONFLICT (id) DO UPDATE SET
          email_enabled = $1,
          email_method = $2,
          smtp_host = $3,
          smtp_port = $4,
          smtp_secure = $5,
          smtp_user = $6,
          smtp_pass = $7,
          email_from_name = $8,
          email_from_address = $9,
          email_reply_to_address = $10,
          email_cc_addresses = $11,
          email_template_subject_en = $12,
          email_template_body_en = $13,
          email_template_subject_zh = $14,
          email_template_body_zh = $15,
          updated_at = CURRENT_TIMESTAMP
      `, [
        email_enabled,
        email_method,
        smtp_host,
        smtp_port,
        smtp_secure,
        smtp_user,
        encryptedPassword, // Store encrypted password
        email_from_name,
        encryptedFromAddress, // Store encrypted from address
        email_reply_to_address || '', // Store reply-to address as plain text
        email_cc_addresses || '', // Store CC addresses as plain text (comma-separated)
        email_template_subject_en || 'Your TradeLink Training System Login Credentials',
        email_template_body_en || defaultTemplateEn,
        email_template_subject_zh || '您的TradeLink培训系统登录凭据',
        email_template_body_zh || defaultTemplateZh
      ]);
      
      return true;
    } catch (error) {
      console.error('Error updating email config:', error);
      throw error;
    }
  }

  // Get default email template
  getDefaultEmailTemplate(language = 'en') {
    if (language === 'zh') {
      return `亲爱的 {{fullName}}，

您的TradeLink内部培训系统账户已创建。

登录凭据：
邮箱：{{email}}
密码：{{password}}

重要安全信息：
• 您的密码将在8天后过期
• 请及时登录开始您的培训
• 请保护您的凭据安全，不要与他人分享
• 如需帮助请联系您的管理员

如果您有任何问题或需要帮助，请联系您的系统管理员。

此致，
TradeLink培训系统管理员

---
这是一封自动发送的邮件，请勿回复。`;
    }

    // Default English template
    return `Dear {{fullName}},

Your account has been created for the TradeLink Internal Training System.

Login Credentials:
Email: {{email}}
Password: {{password}}

Important Security Information:
• Your password will expire in 8 days
• Please log in promptly to begin your training
• Keep your credentials secure and do not share them
• Contact your administrator if you need assistance

If you have any questions or need assistance, please contact your system administrator.

Best regards,
TradeLink Training System Administration

---
This is an automated message. Please do not reply to this email.`;
  }

  // Migrate sensitive data using password service
  async migrateSensitiveData() {
    const updateCallback = async (encryptedPassword, encryptedFromAddress) => {
      await query(`
        UPDATE email_config
        SET smtp_pass = $1, email_from_address = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = 1
      `, [encryptedPassword, encryptedFromAddress]);
    };

    await this.passwordService.migrateSensitiveData(
      () => this.getEmailConfig(),
      updateCallback
    );
  }
}

module.exports = EmailConfigService;
