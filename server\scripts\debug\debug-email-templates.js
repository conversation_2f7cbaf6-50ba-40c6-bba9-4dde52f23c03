const { query } = require('./database/init');

async function debugEmailTemplates() {
  try {
    console.log('=== Email Template Debug ===');
    
    // Check email configuration
    const configResult = await query('SELECT * FROM email_config LIMIT 1');
    
    if (configResult.rows.length === 0) {
      console.log('No email configuration found in database');
      return;
    }
    
    const config = configResult.rows[0];
    
    console.log('Email Configuration:');
    console.log('- Email enabled:', config.email_enabled);
    console.log('- Email method:', config.email_method);
    console.log('- English subject:', config.email_template_subject_en);
    console.log('- English body preview:', config.email_template_body_en?.substring(0, 200) + '...');
    console.log('- Chinese subject:', config.email_template_subject_zh);
    console.log('- Chinese body preview:', config.email_template_body_zh?.substring(0, 200) + '...');
    
    // Check for problematic patterns
    const englishBodyIssues = [];
    const chineseBodyIssues = [];
    
    if (config.email_template_body_en?.includes('{{password}}{{password}}')) {
      englishBodyIssues.push('Contains double password template');
    }
    if (config.email_template_body_en?.includes('NuPj{{password}}qxbXMG')) {
      englishBodyIssues.push('Contains malformed password pattern');
    }
    
    if (config.email_template_body_zh?.includes('{{password}}{{password}}')) {
      chineseBodyIssues.push('Contains double password template');
    }
    if (config.email_template_body_zh?.includes('NuPj{{password}}qxbXMG')) {
      chineseBodyIssues.push('Contains malformed password pattern');
    }
    
    if (englishBodyIssues.length > 0) {
      console.log('🚨 English template issues:', englishBodyIssues);
    }
    if (chineseBodyIssues.length > 0) {
      console.log('🚨 Chinese template issues:', chineseBodyIssues);
    }
    
    if (englishBodyIssues.length === 0 && chineseBodyIssues.length === 0) {
      console.log('✅ No obvious template issues found');
    }
    
    // Test password generation
    console.log('\n=== Password Generation Test ===');
    const testPasswords = [];
    for (let i = 0; i < 10; i++) {
      const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]|;:,.<>?';
      let password = '';
      for (let j = 0; j < 12; j++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
      }
      testPasswords.push(password);
      
      if (password.includes('{{') || password.includes('}}')) {
        console.log('🚨 Generated password contains template syntax:', password);
      }
    }
    
    console.log('Generated test passwords:', testPasswords);
    
  } catch (error) {
    console.error('Error debugging email templates:', error);
  } finally {
    process.exit(0);
  }
}

debugEmailTemplates();
