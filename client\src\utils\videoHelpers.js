/**
 * Video Player Utility Functions
 * Extracted from VideoPlayer.jsx for better modularity
 */

/**
 * Safely get video URL with fallback options
 * @param {Object} video - Video object with video_url property
 * @returns {string|null} - Formatted video URL or null
 */
export const getVideoUrl = (video) => {
  if (!video?.video_url) return null

  try {
    // Check if it's already a full URL
    if (video.video_url.startsWith('http://') || video.video_url.startsWith('https://')) {
      return video.video_url
    }

    // Extract filename from video_url (e.g., "/uploads/videos/filename.mp4" -> "filename.mp4")
    const filename = video.video_url.split('/').pop()
    const baseUrl = process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://127.0.0.1:3002'

    // Return primary API endpoint URL
    return `${baseUrl}/api/training/video/${filename}`
  } catch (error) {
    console.error('Error building video URL:', error)
    return null
  }
}

/**
 * Get fallback video URL - optimized to reuse logic
 * @param {Object} video - Video object with video_url property
 * @returns {string|null} - Fallback video URL or null
 */
export const getFallbackVideoUrl = (video) => {
  if (!video?.video_url) return null

  try {
    const filename = video.video_url.split('/').pop()
    const baseUrl = process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://127.0.0.1:3002'
    return `${baseUrl}/uploads/videos/${filename}`
  } catch (error) {
    console.error('Error building fallback video URL:', error)
    return null
  }
}

/**
 * Safely format time in YouTube style (MM:SS or HH:MM:SS)
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time string
 */
export const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds) || seconds < 0) return '0:00'

  const totalSeconds = Math.floor(seconds)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const secs = totalSeconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

/**
 * Calculate video progress percentages
 * @param {number} duration - Total video duration
 * @param {number} watchTime - Current watch time
 * @param {number} maxWatched - Maximum watched time
 * @param {number} hoverTime - Hover time (can be null)
 * @param {boolean} hasCompletedBefore - Whether user completed video before
 * @param {boolean} adminControlsActive - Whether admin controls are active
 * @returns {Object} - Object with calculated percentages
 */
export const calculateVideoProgress = (duration, watchTime, maxWatched, hoverTime, hasCompletedBefore, adminControlsActive) => {
  const watchedPercent = duration > 0 ? (watchTime / duration) * 100 : 0
  const maxWatchedPercent = duration > 0 ? (maxWatched / duration) * 100 : 0
  const hoverPercent = duration > 0 && hoverTime !== null ? (hoverTime / duration) * 100 : null
  const canSeekToEnd = hasCompletedBefore || adminControlsActive
  const seekablePercent = canSeekToEnd ? 100 : maxWatchedPercent

  return { watchedPercent, maxWatchedPercent, hoverPercent, canSeekToEnd, seekablePercent }
}

/**
 * Check if video is completed based on progress
 * @param {number} currentTime - Current playback time
 * @param {number} duration - Total video duration
 * @returns {boolean} - Whether video is considered completed
 */
export const isVideoCompleted = (currentTime, duration) => {
  if (duration <= 0) return false
  const progressPercent = Math.round((currentTime / duration) * 100)
  return progressPercent >= 100
}

/**
 * Save video progress to server (for unmount scenarios)
 * @param {string} moduleId - Module ID
 * @param {number} watchTime - Current watch time
 * @param {boolean} completed - Whether video is completed
 * @param {string} languageCode - Language code
 * @returns {Promise} - Promise that resolves when progress is saved
 */
export const saveProgressToServer = async (moduleId, watchTime, completed, languageCode) => {
  try {
    const token = localStorage.getItem('token')
    if (!token) {
      throw new Error('No authentication token found')
    }

    const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://127.0.0.1:3002/api'}/training/modules/${moduleId}/progress`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        watchTime: Math.round(watchTime),
        completed,
        languageCode
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Failed to save progress to server:', error)
    throw error
  }
}

/**
 * Validate if user can seek to a specific time
 * @param {number} targetTime - Time user wants to seek to
 * @param {number} maxWatched - Maximum watched time
 * @param {boolean} hasCompletedBefore - Whether user completed video before
 * @param {boolean} adminControlsActive - Whether admin controls are active
 * @returns {Object} - Object with canSeek boolean and reason
 */
export const validateSeekTime = (targetTime, maxWatched, hasCompletedBefore, adminControlsActive) => {
  if (hasCompletedBefore || adminControlsActive) {
    return { canSeek: true, reason: null }
  }

  if (targetTime > maxWatched + 2) {
    return { 
      canSeek: false, 
      reason: 'cannot_skip_ahead_first_viewing'
    }
  }

  return { canSeek: true, reason: null }
}
