/**
 * Clipboard Utilities
 * Safe clipboard operations with fallback support
 */

/**
 * Copy text to clipboard with fallback for older browsers or insecure contexts
 * @param {string} text - Text to copy to clipboard
 * @returns {Promise<void>} - Promise that resolves when copy is successful
 */
export const copyToClipboard = async (text) => {
  try {
    // Try modern clipboard API first (requires HTTPS or localhost)
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text)
      return
    }
    
    // Fallback for older browsers or insecure contexts
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    textArea.style.opacity = '0'
    textArea.style.pointerEvents = 'none'
    
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    // Use deprecated execCommand as fallback
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    
    if (!successful) {
      throw new Error('Copy command failed')
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    throw new Error(`Failed to copy to clipboard: ${error.message}`)
  }
}

/**
 * Check if clipboard API is available
 * @returns {boolean} - True if clipboard API is available
 */
export const isClipboardAvailable = () => {
  return !!(navigator.clipboard && navigator.clipboard.writeText)
}

/**
 * Copy text with user feedback
 * @param {string} text - Text to copy
 * @param {Function} onSuccess - Success callback
 * @param {Function} onError - Error callback
 */
export const copyWithFeedback = async (text, onSuccess, onError) => {
  try {
    await copyToClipboard(text)
    if (onSuccess) onSuccess()
  } catch (error) {
    if (onError) onError(error)
  }
}

export default {
  copyToClipboard,
  isClipboardAvailable,
  copyWithFeedback
}
