/**
 * Form Validation and Helper Utilities
 * Extracted common form validation logic for reuse across components
 */

/**
 * Common validation rules for forms
 */
export const validationRules = {
  required: (message) => ({ required: true, message }),
  email: (message) => ({ type: 'email', message }),
  minLength: (min, message) => ({ min, message }),
  maxLength: (max, message) => ({ max, message }),
  pattern: (pattern, message) => ({ pattern, message }),
  numeric: (message) => ({ 
    pattern: /^\d+$/, 
    message 
  }),
  positiveNumber: (message) => ({
    validator: (_, value) => {
      if (!value || (Number(value) > 0)) {
        return Promise.resolve()
      }
      return Promise.reject(new Error(message))
    }
  }),
  percentage: (message) => ({
    validator: (_, value) => {
      const num = Number(value)
      if (!value || (num >= 0 && num <= 100)) {
        return Promise.resolve()
      }
      return Promise.reject(new Error(message))
    }
  }),
  fileSize: (message) => ({
    validator: (_, value) => {
      if (!value) return Promise.resolve()
      
      const fileSizePattern = /^\d+(\.\d+)?\s*(MB|GB|KB)$/i
      if (fileSizePattern.test(value)) {
        return Promise.resolve()
      }
      return Promise.reject(new Error(message))
    }
  })
}

/**
 * Generate common form rules with translation support
 * @param {Function} t - Translation function
 * @returns {Object} - Object with common validation rules
 */
export const getCommonValidationRules = (t) => ({
  required: (field) => validationRules.required(t('validation.required')),
  email: () => validationRules.email(t('validation.email')),
  minLength: (min) => validationRules.minLength(min, t('validation.minLength', { min })),
  maxLength: (max) => validationRules.maxLength(max, t('validation.maxLength', { max })),
  numeric: () => validationRules.numeric(t('validation.numeric')),
  positiveNumber: () => validationRules.positiveNumber(t('validation.positive')),
  percentage: () => validationRules.percentage(t('validation.percentage')),
  fileSize: () => validationRules.fileSize(t('systemConfig.maxFileSizeInvalid'))
})

/**
 * Form field configurations for common inputs
 */
export const getFormFieldConfig = (t) => ({
  email: {
    name: 'email',
    label: t('auth.email'),
    rules: [getCommonValidationRules(t).required(), getCommonValidationRules(t).email()],
    placeholder: t('auth.enterEmail')
  },
  password: {
    name: 'password',
    label: t('auth.password'),
    rules: [getCommonValidationRules(t).required(), getCommonValidationRules(t).minLength(6)],
    placeholder: t('auth.enterPassword')
  },
  title: {
    name: 'title',
    label: t('common.title'),
    rules: [getCommonValidationRules(t).required(), getCommonValidationRules(t).maxLength(255)],
    placeholder: t('common.enterTitle')
  },
  description: {
    name: 'description',
    label: t('common.description'),
    rules: [getCommonValidationRules(t).maxLength(1000)],
    placeholder: t('common.enterDescription')
  }
})

/**
 * Handle form submission with loading state and error handling
 * @param {Function} submitFunction - Function to call for submission
 * @param {Function} setLoading - Function to set loading state
 * @param {Function} onSuccess - Success callback
 * @param {Function} onError - Error callback
 * @param {Function} t - Translation function
 * @returns {Function} - Form submission handler
 */
export const createFormSubmissionHandler = (submitFunction, setLoading, onSuccess, onError, t) => {
  return async (values) => {
    setLoading(true)
    try {
      const result = await submitFunction(values)
      onSuccess?.(result)
    } catch (error) {
      console.error('Form submission error:', error)
      onError?.(error)
    } finally {
      setLoading(false)
    }
  }
}

/**
 * Reset form with optional default values
 * @param {Object} form - Ant Design form instance
 * @param {Object} defaultValues - Default values to set
 */
export const resetFormWithDefaults = (form, defaultValues = {}) => {
  form.resetFields()
  if (Object.keys(defaultValues).length > 0) {
    form.setFieldsValue(defaultValues)
  }
}

/**
 * Validate form fields and return first error
 * @param {Object} form - Ant Design form instance
 * @returns {Promise<Object|null>} - First validation error or null
 */
export const validateFormFields = async (form) => {
  try {
    await form.validateFields()
    return null
  } catch (errorInfo) {
    const firstError = errorInfo.errorFields?.[0]
    return firstError ? {
      field: firstError.name[0],
      message: firstError.errors[0]
    } : null
  }
}

/**
 * Common form layout configurations
 */
export const formLayouts = {
  vertical: {
    layout: 'vertical',
    size: 'large'
  },
  horizontal: {
    layout: 'horizontal',
    labelCol: { span: 6 },
    wrapperCol: { span: 18 }
  },
  inline: {
    layout: 'inline'
  }
}

/**
 * Common form item styles
 */
export const formItemStyles = {
  fullWidth: { width: '100%' },
  halfWidth: { width: '50%' },
  quarterWidth: { width: '25%' },
  marginBottom: (size = 16) => ({ marginBottom: size }),
  noMargin: { marginBottom: 0 }
}

/**
 * Extract form errors for display
 * @param {Object} error - Error object from API or validation
 * @returns {Array} - Array of error messages
 */
export const extractFormErrors = (error) => {
  if (!error) return []
  
  if (typeof error === 'string') return [error]
  
  if (error.message) return [error.message]
  
  if (error.errors && Array.isArray(error.errors)) {
    return error.errors.map(err => typeof err === 'string' ? err : err.message).filter(Boolean)
  }
  
  return ['An unknown error occurred']
}

/**
 * Format form data for API submission
 * @param {Object} formData - Raw form data
 * @param {Array} numericFields - Fields that should be converted to numbers
 * @param {Array} booleanFields - Fields that should be converted to booleans
 * @returns {Object} - Formatted form data
 */
export const formatFormDataForAPI = (formData, numericFields = [], booleanFields = []) => {
  const formatted = { ...formData }
  
  // Convert numeric fields
  numericFields.forEach(field => {
    if (formatted[field] !== undefined && formatted[field] !== null) {
      formatted[field] = Number(formatted[field])
    }
  })
  
  // Convert boolean fields
  booleanFields.forEach(field => {
    if (formatted[field] !== undefined) {
      formatted[field] = Boolean(formatted[field])
    }
  })
  
  return formatted
}
