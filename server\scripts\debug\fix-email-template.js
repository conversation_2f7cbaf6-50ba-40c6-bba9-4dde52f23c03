const { Pool } = require('pg');

const pool = new Pool({
  user: 'training_user',
  host: '127.0.0.1',
  database: 'training_system',
  password: 'training_password',
  port: 5432,
});

async function fixEmailTemplate() {
  try {
    console.log('=== Email Template Fix ===');
    
    // Check current email configuration
    const configResult = await pool.query('SELECT * FROM email_config LIMIT 1');
    
    if (configResult.rows.length === 0) {
      console.log('No email configuration found in database');
      return;
    }
    
    const config = configResult.rows[0];
    
    console.log('Current Email Configuration:');
    console.log('- Email enabled:', config.email_enabled);
    console.log('- Email method:', config.email_method);
    console.log('- English subject:', config.email_template_subject_en);
    console.log('- English body preview:', config.email_template_body_en?.substring(0, 200) + '...');
    
    // Check for problematic patterns
    let needsFix = false;
    const issues = [];
    
    if (config.email_template_body_en?.includes('{{password}}{{password}}')) {
      issues.push('English template contains double password template');
      needsFix = true;
    }
    if (config.email_template_body_en?.includes('NuPj{{password}}qxbXMG')) {
      issues.push('English template contains malformed password pattern');
      needsFix = true;
    }
    if (config.email_template_body_zh?.includes('{{password}}{{password}}')) {
      issues.push('Chinese template contains double password template');
      needsFix = true;
    }
    if (config.email_template_body_zh?.includes('NuPj{{password}}qxbXMG')) {
      issues.push('Chinese template contains malformed password pattern');
      needsFix = true;
    }
    
    if (issues.length > 0) {
      console.log('🚨 Issues found:', issues);
    } else {
      console.log('✅ No obvious template issues found');
    }
    
    if (needsFix) {
      console.log('🔧 Fixing email templates...');
      
      // Default English template
      const defaultEnglishTemplate = `Dear {{fullName}},

Your account has been created for the TradeLink Internal Training System.

Login Credentials:
Email: {{email}}
Password: {{password}}

Important Security Information:
• Your password will expire in 8 days
• Please log in promptly to begin your training
• Keep your credentials secure and do not share them
• Contact your administrator if you need assistance

If you have any questions or need assistance, please contact your system administrator.

Best regards,
TradeLink Training System Administration

---
This is an automated message. Please do not reply to this email.`;

      // Default Chinese template
      const defaultChineseTemplate = `亲爱的 {{fullName}}，

您的TradeLink内部培训系统账户已创建。

登录凭据：
邮箱：{{email}}
密码：{{password}}

重要安全信息：
• 您的密码将在8天后过期
• 请及时登录开始您的培训
• 请保护您的凭据安全，不要与他人分享
• 如需帮助请联系您的管理员

如果您有任何问题或需要帮助，请联系您的系统管理员。

此致，
TradeLink培训系统管理员

---
这是一封自动发送的邮件，请勿回复。`;

      // Update the templates
      await pool.query(`
        UPDATE email_config 
        SET 
          email_template_subject_en = $1,
          email_template_body_en = $2,
          email_template_subject_zh = $3,
          email_template_body_zh = $4,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = 1
      `, [
        'Your TradeLink Training System Login Credentials',
        defaultEnglishTemplate,
        '您的TradeLink培训系统登录凭据',
        defaultChineseTemplate
      ]);
      
      console.log('✅ Email templates have been fixed!');
    }
    
    console.log('\n=== Password Generation Test ===');
    // Test password generation to ensure no template syntax
    const testPasswords = [];
    for (let i = 0; i < 5; i++) {
      const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]|;:,.<>?';
      let password = '';
      for (let j = 0; j < 12; j++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
      }
      testPasswords.push(password);
      
      if (password.includes('{{') || password.includes('}}')) {
        console.log('🚨 Generated password contains template syntax:', password);
      }
    }
    
    console.log('✅ Test passwords generated:', testPasswords);
    console.log('\n🎉 Email template fix completed!');
    
  } catch (error) {
    console.error('❌ Error fixing email templates:', error);
  } finally {
    await pool.end();
  }
}

fixEmailTemplate();
