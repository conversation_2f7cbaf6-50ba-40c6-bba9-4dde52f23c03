const express = require('express');
const Joi = require('joi');
const { requireAuth, requireAdmin } = require('../middleware/auth');
const emailService = require('../services/emailService');

const router = express.Router();

// Validation schemas
const emailConfigSchema = Joi.object({
  email_enabled: Joi.boolean().required(),
  email_method: Joi.string().valid('clipboard', 'smtp').required(),
  smtp_host: Joi.string().allow('').optional(),
  smtp_port: Joi.number().integer().min(1).max(65535).optional(),
  smtp_secure: Joi.boolean().optional(),
  smtp_user: Joi.string().allow('').optional(),
  smtp_pass: Joi.string().allow('').optional(),
  email_from_name: Joi.string().allow('').optional(),
  email_from_address: Joi.string().email().allow('').optional(),
  email_reply_to_address: Joi.string().email().allow('').optional(),
  email_cc_addresses: Joi.string().allow('').optional(),
  email_template_subject_en: Joi.string().allow('').optional(),
  email_template_body_en: Joi.string().allow('').optional(),
  email_template_subject_zh: Joi.string().allow('').optional(),
  email_template_body_zh: Joi.string().allow('').optional()
});

const testEmailSchema = Joi.object({
  email_enabled: Joi.boolean().optional(),
  email_method: Joi.string().valid('clipboard', 'smtp').optional(),
  smtp_host: Joi.string().required(),
  smtp_port: Joi.number().integer().min(1).max(65535).required(),
  smtp_secure: Joi.boolean().required(),
  smtp_user: Joi.string().allow('').optional(),
  smtp_pass: Joi.string().allow('').optional(),
  email_from_name: Joi.string().required(),
  email_from_address: Joi.string().email().required(),
  email_reply_to_address: Joi.string().email().allow('').optional(),
  email_cc_addresses: Joi.string().allow('').optional(),
  email_template_subject_en: Joi.string().allow('').optional(),
  email_template_body_en: Joi.string().allow('').optional(),
  email_template_subject_zh: Joi.string().allow('').optional(),
  email_template_body_zh: Joi.string().allow('').optional(),
  test_email: Joi.string().email().required()
});

// Debug email configuration (shows raw encrypted data)
router.get('/debug', requireAuth, requireAdmin, async (req, res) => {
  try {
    const config = await emailService.getEmailConfig();

    console.log('=== EMAIL CONFIG DEBUG ===');
    console.log('Raw config from database:', {
      email_from_address: config.email_from_address,
      smtp_pass: config.smtp_pass ? 'PROVIDED' : 'NOT_PROVIDED',
      email_from_address_length: config.email_from_address ? config.email_from_address.length : 0,
      contains_at: config.email_from_address ? config.email_from_address.includes('@') : false,
      contains_colon: config.email_from_address ? config.email_from_address.includes(':') : false
    });

    // Try decryption
    const decryptedFromAddress = emailService.decryptPassword(config.email_from_address);
    const decryptedPassword = emailService.decryptPassword(config.smtp_pass);

    console.log('Decryption results:', {
      from_address_decrypted: decryptedFromAddress ? 'SUCCESS' : 'FAILED',
      password_decrypted: decryptedPassword ? 'SUCCESS' : 'FAILED'
    });

    res.json({
      success: true,
      debug: {
        raw: {
          email_from_address: config.email_from_address,
          smtp_pass: config.smtp_pass ? '••••••••' : '',
          email_from_address_length: config.email_from_address ? config.email_from_address.length : 0
        },
        decrypted: {
          email_from_address: decryptedFromAddress,
          smtp_pass: decryptedPassword ? '••••••••' : ''
        },
        analysis: {
          from_address_looks_encrypted: config.email_from_address && !config.email_from_address.includes('@'),
          from_address_decryption_success: !!decryptedFromAddress,
          password_decryption_success: !!decryptedPassword
        }
      }
    });
  } catch (error) {
    console.error('Debug email config error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to debug email configuration'
    });
  }
});

// Get email configuration
router.get('/', requireAuth, requireAdmin, async (req, res) => {
  try {
    const config = await emailService.getEmailConfig();

    // Decrypt sensitive data for display
    let displayFromAddress = config.email_from_address;
    if (config.email_from_address) {
      // Try to decrypt - if it's not encrypted, decryptPassword will return empty string
      const decrypted = emailService.decryptPassword(config.email_from_address);
      if (decrypted && decrypted !== config.email_from_address) {
        // Successfully decrypted and it's different from original
        displayFromAddress = decrypted;
        console.log('Decrypted from address for display');
      } else if (!config.email_from_address.includes('@')) {
        // Looks encrypted but decryption failed - might be corrupted
        console.warn('From address appears encrypted but decryption failed');
        displayFromAddress = ''; // Clear it for safety
      }
    }

    // Don't send password at all - frontend will handle "new password" workflow
    const safeConfig = {
      ...config,
      smtp_pass: '', // Always empty - frontend will show "new password" field if config exists
      email_from_address: displayFromAddress,
      has_existing_password: !!config.smtp_pass // Flag to indicate if password exists
    };

    res.json({
      success: true,
      config: safeConfig
    });
  } catch (error) {
    console.error('Get email config error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to get email configuration' 
    });
  }
});

// Update email configuration
router.put('/', requireAuth, requireAdmin, async (req, res) => {
  try {
    // Validate input
    const { error, value } = emailConfigSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ 
        success: false,
        error: 'Validation failed', 
        details: error.details[0].message 
      });
    }

    // If SMTP is enabled, validate required fields
    if (value.email_enabled && value.email_method === 'smtp') {
      if (!value.smtp_host || !value.email_from_address) {
        return res.status(400).json({
          success: false,
          error: 'SMTP host and from address are required when SMTP is enabled'
        });
      }
    }

    // Get current config to preserve encrypted password if not provided
    const currentConfig = await emailService.getEmailConfig();

    // Handle password update logic
    if (!value.smtp_pass || value.smtp_pass.trim() === '') {
      // No new password provided - keep existing encrypted password
      value.smtp_pass = currentConfig.smtp_pass;
      console.log('Keeping existing encrypted password');
    } else {
      // New password provided - it will be encrypted in updateEmailConfig
      console.log('New password provided, will be encrypted');
    }

    // Note: email_from_address will always be encrypted in updateEmailConfig

    // Update configuration
    await emailService.updateEmailConfig(value);
    
    res.json({
      success: true,
      message: 'Email configuration updated successfully'
    });
  } catch (error) {
    console.error('Update email config error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to update email configuration' 
    });
  }
});

// Test email configuration (server-side only)
router.post('/test', requireAuth, requireAdmin, async (req, res) => {
  try {
    console.log('=== SERVER-SIDE EMAIL TEST START ===');

    // Validate input
    const { error, value } = testEmailSchema.validate(req.body);
    if (error) {
      console.error('Validation error:', error.details[0].message);
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    console.log('Validation passed, proceeding with server-side test...');

    // Get current config to use encrypted sensitive data if needed
    const currentConfig = await emailService.getEmailConfig();

    // Handle password for testing
    if (!value.smtp_pass || value.smtp_pass.trim() === '') {
      console.log('Using stored encrypted password from database for test');
      value.smtp_pass = currentConfig.smtp_pass;
    } else {
      console.log('Using new password from form for test');
    }

    // Use server-side email service for testing
    const testResult = await emailService.testEmailConfigAndSend({
      ...value,
      test_email: value.test_email
    });

    if (testResult.success) {
      console.log('Server-side email test successful');
      res.json({
        success: true,
        message: 'Test email sent successfully via server',
        messageId: testResult.messageId,
        method: 'server-side'
      });
    } else {
      console.error('Server-side email test failed:', testResult.message);
      res.status(400).json({
        success: false,
        error: 'Email test failed',
        details: testResult.message,
        method: 'server-side'
      });
    }

  } catch (error) {
    console.error('=== SERVER-SIDE EMAIL TEST ERROR ===');
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    res.status(500).json({
      success: false,
      error: 'Failed to test email configuration',
      details: error.message
    });
  }
});

// Send email template (server-side email generation)
router.post('/send-template', requireAuth, requireAdmin, async (req, res) => {
  try {
    const { templateType, recipientEmail, templateData } = req.body;

    console.log('=== SERVER-SIDE EMAIL TEMPLATE SEND ===');
    console.log('Template type:', templateType);
    console.log('Recipient:', recipientEmail);

    // Validate required fields
    if (!templateType || !recipientEmail) {
      return res.status(400).json({
        success: false,
        error: 'Template type and recipient email are required'
      });
    }

    // Generate email content based on template type
    let emailContent, subject;

    switch (templateType) {
      case 'login_credentials':
        if (!templateData.password) {
          return res.status(400).json({
            success: false,
            error: 'Password is required for login credentials template'
          });
        }
        subject = 'Your Training System Login Credentials';
        emailContent = emailService.generateLoginEmailHTML(recipientEmail, templateData.password);
        break;

      case 'test_email':
        subject = 'Training System - Email Configuration Test';
        emailContent = emailService.generateTestEmailHTML(templateData);
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid template type'
        });
    }

    // Send email using server-side service
    const result = await emailService.sendEmail(recipientEmail, subject, emailContent);

    if (result.success) {
      console.log('Template email sent successfully');
      res.json({
        success: true,
        message: 'Email sent successfully',
        messageId: result.messageId,
        templateType,
        method: 'server-side'
      });
    } else {
      console.error('Template email send failed:', result.message);
      res.status(400).json({
        success: false,
        error: 'Failed to send email',
        details: result.message
      });
    }

  } catch (error) {
    console.error('=== TEMPLATE EMAIL SEND ERROR ===');
    console.error('Error details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send template email',
      details: error.message
    });
  }
});

module.exports = router;
