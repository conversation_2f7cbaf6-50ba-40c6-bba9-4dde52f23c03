const { query } = require('../database/init');

/**
 * Parse file size string (e.g., "5GB", "500MB", "1024KB") to bytes
 */
function parseFileSize(sizeStr) {
  if (!sizeStr || typeof sizeStr !== 'string') {
    return 500 * 1024 * 1024; // Default 500MB
  }

  const units = {
    'B': 1,
    'KB': 1024,
    'MB': 1024 * 1024,
    'GB': 1024 * 1024 * 1024,
    'TB': 1024 * 1024 * 1024 * 1024
  };

  const match = sizeStr.trim().toUpperCase().match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)$/);
  if (!match) {
    console.warn(`Invalid file size format: ${sizeStr}, using default 500MB`);
    return 500 * 1024 * 1024;
  }

  const [, size, unit] = match;
  return Math.floor(parseFloat(size) * units[unit]);
}

/**
 * Format bytes to human readable string
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${units[i]}`;
}

class SystemConfigService {
  constructor() {
    this._cachedMaxFileSize = null;
  }

  // Initialize the service (cache database values)
  async initialize() {
    try {
      await this._cacheMaxFileSize();
      console.log('SystemConfigService initialized with cached max file size');
    } catch (error) {
      console.error('Error initializing SystemConfigService:', error);
    }
  }

  // Get system configuration
  async getSystemConfig() {
    try {
      const result = await query('SELECT * FROM system_config WHERE id = 1');
      
      if (result.rows.length > 0) {
        return result.rows[0];
      }
      
      // Return default config if none exists
      return {
        id: 1,
        password_expiration_days: 8,
        timezone: 'Asia/Shanghai',
        max_file_size: '500MB',
        created_at: new Date(),
        updated_at: new Date()
      };
    } catch (error) {
      console.error('Error getting system config:', error);
      // Return default config on error
      return {
        id: 1,
        password_expiration_days: 8,
        timezone: 'Asia/Shanghai',
        max_file_size: '500MB',
        created_at: new Date(),
        updated_at: new Date()
      };
    }
  }

  // Update system configuration
  async updateSystemConfig(config) {
    try {
      const { password_expiration_days, timezone, max_file_size } = config;

      // Validate password expiration days
      if (!password_expiration_days || password_expiration_days < 1 || password_expiration_days > 365) {
        throw new Error('Password expiration days must be between 1 and 365');
      }

      // Validate timezone (basic validation)
      if (timezone && typeof timezone !== 'string') {
        throw new Error('Timezone must be a valid string');
      }

      // Validate max file size format if provided
      if (max_file_size) {
        if (typeof max_file_size !== 'string') {
          throw new Error('Max file size must be a valid string');
        }
        // Test if the format is valid by trying to parse it
        try {
          parseFileSize(max_file_size);
        } catch (parseError) {
          throw new Error('Max file size format is invalid. Use format like "500MB", "2GB", etc.');
        }
      }

      // Upsert configuration
      const result = await query(`
        INSERT INTO system_config (id, password_expiration_days, timezone, max_file_size, updated_at)
        VALUES (1, $1, $2, $3, CURRENT_TIMESTAMP)
        ON CONFLICT (id) DO UPDATE SET
          password_expiration_days = $1,
          timezone = $2,
          max_file_size = $3,
          updated_at = CURRENT_TIMESTAMP
        RETURNING *
      `, [password_expiration_days, timezone || 'Asia/Shanghai', max_file_size || '500MB']);

      // Update the cached max file size after successful database update
      await this._cacheMaxFileSize();

      return result.rows[0];
    } catch (error) {
      console.error('Error updating system config:', error);
      throw error;
    }
  }

  // Get password expiration days (convenience method)
  async getPasswordExpirationDays() {
    try {
      const config = await this.getSystemConfig();
      return config.password_expiration_days || 8;
    } catch (error) {
      console.error('Error getting password expiration days:', error);
      return 8; // Default fallback
    }
  }

  // Get system timezone (convenience method)
  async getSystemTimezone() {
    try {
      const config = await this.getSystemConfig();
      return config.timezone || 'Asia/Shanghai';
    } catch (error) {
      console.error('Error getting system timezone:', error);
      return 'Asia/Shanghai'; // Default fallback
    }
  }

  // Check if password is expired for a user
  async isPasswordExpired(passwordCreatedAt, isAdmin = false) {
    try {
      // Admin passwords never expire
      if (isAdmin || !passwordCreatedAt) {
        return false;
      }

      const expirationDays = await this.getPasswordExpirationDays();
      const passwordAge = Date.now() - new Date(passwordCreatedAt).getTime();
      const expirationMs = expirationDays * 24 * 60 * 60 * 1000;

      return passwordAge > expirationMs;
    } catch (error) {
      console.error('Error checking password expiration:', error);
      return false; // Default to not expired on error
    }
  }

  // Get password age in days
  async getPasswordAgeDays(passwordCreatedAt) {
    try {
      if (!passwordCreatedAt) {
        return 0;
      }

      const passwordAge = Date.now() - new Date(passwordCreatedAt).getTime();
      return Math.floor(passwordAge / (24 * 60 * 60 * 1000));
    } catch (error) {
      console.error('Error calculating password age:', error);
      return 0;
    }
  }

  // Get days left until password expires
  async getPasswordDaysLeft(passwordCreatedAt, isAdmin = false) {
    try {
      if (isAdmin || !passwordCreatedAt) {
        return null; // Admin passwords don't expire
      }

      const expirationDays = await this.getPasswordExpirationDays();
      const ageDays = await this.getPasswordAgeDays(passwordCreatedAt);
      const daysLeft = expirationDays - ageDays;

      return Math.max(0, daysLeft);
    } catch (error) {
      console.error('Error calculating password days left:', error);
      return null;
    }
  }

  // Get maximum file size from database config, fallback to environment variable
  async getMaxFileSize() {
    try {
      const config = await this.getSystemConfig();
      const maxFileSizeStr = config.max_file_size || process.env.MAX_FILE_SIZE || '500MB';
      return parseFileSize(maxFileSizeStr);
    } catch (error) {
      console.error('Error getting max file size from config, using environment variable:', error);
      const maxFileSizeStr = process.env.MAX_FILE_SIZE || '500MB';
      return parseFileSize(maxFileSizeStr);
    }
  }

  // Get maximum file size synchronously (for backward compatibility)
  getMaxFileSizeSync() {
    try {
      // Try to get from database synchronously using a cached value
      // This is a fallback approach - we'll cache the database value
      if (this._cachedMaxFileSize) {
        return this._cachedMaxFileSize;
      }

      // If no cached value, use environment variable or default
      const maxFileSizeStr = process.env.MAX_FILE_SIZE || '500MB';
      return parseFileSize(maxFileSizeStr);
    } catch (error) {
      console.error('Error in getMaxFileSizeSync:', error);
      return parseFileSize('500MB');
    }
  }

  // Cache the max file size for synchronous access
  async _cacheMaxFileSize() {
    try {
      const maxFileSize = await this.getMaxFileSize();
      this._cachedMaxFileSize = maxFileSize;
      return maxFileSize;
    } catch (error) {
      console.error('Error caching max file size:', error);
      this._cachedMaxFileSize = parseFileSize('500MB');
      return this._cachedMaxFileSize;
    }
  }

  // Get maximum file size in human readable format
  async getMaxFileSizeFormatted() {
    const bytes = await this.getMaxFileSize();
    return formatFileSize(bytes);
  }

  // Get file upload configuration
  async getFileUploadConfig() {
    const maxSizeBytes = await this.getMaxFileSize();
    return {
      maxFileSize: maxSizeBytes,
      maxFileSizeFormatted: formatFileSize(maxSizeBytes),
      uploadPath: process.env.UPLOAD_PATH || './uploads'
    };
  }

  // Get file upload configuration synchronously (for backward compatibility)
  getFileUploadConfigSync() {
    const maxSizeBytes = this.getMaxFileSizeSync();
    return {
      maxFileSize: maxSizeBytes,
      maxFileSizeFormatted: formatFileSize(maxSizeBytes),
      uploadPath: process.env.UPLOAD_PATH || './uploads'
    };
  }
}

module.exports = new SystemConfigService();
