# =============================================================================
# UNIFIED ENVIRONMENT CONFIGURATION - EXAMPLE
# Internal Training System - All Services Configuration
# Copy this file to .env and customize the values for your environment
# =============================================================================

# =============================================================================
# DEPLOYMENT ENVIRONMENT
# =============================================================================
NODE_ENV=production
DEPLOYMENT_TYPE=docker
# Options: local, docker, azure-app-service, azure-container-instances

# =============================================================================
# APPLICATION PORTS
# =============================================================================
FRONTEND_PORT=80
FRONTEND_DEV_PORT=3000
BACKEND_PORT=3002
PORT=8080

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Localhost Docker style deployment
REACT_APP_API_URL=http://localhost:3002/api

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Docker Compose - Use service name for container communication
DB_HOST=postgres
DB_PORT=5432
DB_NAME=training_system
DB_USER=training_user
DB_PASSWORD=your_secure_password_here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
SESSION_SECRET=your_session_secret_change_this_in_production
JWT_SECRET=your_jwt_secret_change_this_in_production
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS (comma-separated origins) - Localhost Docker style
CORS_ORIGIN=http://localhost:3000,http://localhost:80,http://localhost

# SSL/HTTPS
FORCE_HTTPS=false
TRUST_PROXY=false

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_ENABLED=false
EMAIL_METHOD=clipboard

# SMTP Settings (when EMAIL_METHOD=smtp)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM_NAME=Training System
EMAIL_FROM_ADDRESS=<EMAIL>

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
UPLOAD_PROVIDER=local
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5GB

# Azure Storage (when UPLOAD_PROVIDER=azure)
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=...
AZURE_STORAGE_ACCOUNT=your-storage-account
AZURE_STORAGE_KEY=your-storage-key
AZURE_STORAGE_CONTAINER=training-uploads

# =============================================================================
# AZURE CONFIGURATION
# =============================================================================
AZURE_SUBSCRIPTION_ID=your-subscription-id
AZURE_RESOURCE_GROUP=training-system-rg
AZURE_LOCATION=East US
FRONTEND_APP_NAME=training-system-frontend
BACKEND_APP_NAME=training-system-backend
AZURE_KEY_VAULT_URL=https://your-keyvault.vault.azure.net/

# =============================================================================
# MONITORING
# =============================================================================
APPINSIGHTS_INSTRUMENTATIONKEY=your-instrumentation-key
APPLICATIONINSIGHTS_CONNECTION_STRING=your-connection-string
LOG_LEVEL=info
HEALTH_CHECK_ENABLED=true

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
REDIS_ENABLED=false
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
SESSION_STORE=memory

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
DEV_SERVER_HOST=0.0.0.0
DEV_SERVER_PORT=3000
DEV_PROXY_TARGET=http://localhost:3002

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_EMAIL_NOTIFICATIONS=true
FEATURE_VIDEO_CONTROLS=true
FEATURE_QUIZ_GENERATION=true
FEATURE_CERTIFICATE_GENERATION=true
FEATURE_ADMIN_PANEL=true

# =============================================================================
# INTERNATIONALIZATION
# =============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,zh

# =============================================================================
# MAINTENANCE
# =============================================================================
BACKUP_ENABLED=false
MAINTENANCE_MODE=false
