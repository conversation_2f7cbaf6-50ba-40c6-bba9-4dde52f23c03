import React, { useState } from 'react'
import { Card, Select, DatePicker, Space, Table, Spin, Alert, Typography, Row, Col, Statistic } from 'antd'
import { Bar<PERSON>hartOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'
import { reportsAPI, trainingAPI, handleAPIError } from '../services/api'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

// Colors for charts
const COLORS = ['#52c41a', '#ff4d4f', '#1890ff', '#faad14', '#722ed1', '#13c2c2']

function QuestionStatistics() {
  const { t } = useTranslation()
  const [filters, setFilters] = useState({
    moduleId: null,
    quizId: null,
    startDate: null,
    endDate: null,
    questionType: null
  })

  // Fetch training modules for filter
  const { data: modulesData } = useQuery(
    'training-modules',
    trainingAPI.getModules,
    {
      select: (response) => response.data.modules,
    }
  )

  // Fetch available quizzes based on selected module
  const { data: quizzesData } = useQuery(
    ['available-quizzes', filters.moduleId],
    () => reportsAPI.getAvailableQuizzes({ moduleId: filters.moduleId }),
    {
      select: (response) => response.data.quizzes,
      enabled: !!filters.moduleId
    }
  )

  // Fetch question statistics
  const { data: statsData, isLoading: statsLoading, error: statsError } = useQuery(
    ['question-statistics', filters],
    () => reportsAPI.getQuestionStatistics(filters),
    {
      select: (response) => response.data,
      enabled: !!(filters.moduleId || filters.quizId || filters.startDate)
    }
  )

  const handleDateRangeChange = (dates) => {
    setFilters(prev => ({
      ...prev,
      startDate: dates?.[0]?.format('YYYY-MM-DD'),
      endDate: dates?.[1]?.format('YYYY-MM-DD')
    }))
  }

  const handleModuleChange = (moduleId) => {
    setFilters(prev => ({
      ...prev,
      moduleId,
      quizId: null // Reset quiz selection when module changes
    }))
  }

  // Prepare chart data
  const chartData = statsData?.questionStats?.map((item, index) => ({
    name: `Q${item.question_order}`,
    questionText: item.question_text.length > 50 
      ? item.question_text.substring(0, 50) + '...' 
      : item.question_text,
    correct: parseInt(item.correct_answers) || 0,
    incorrect: parseInt(item.incorrect_answers) || 0,
    total: parseInt(item.total_attempts) || 0,
    percentage: parseFloat(item.correct_percentage) || 0,
    questionType: item.question_type,
    quizTitle: item.quiz_title
  })) || []

  // Group data by quiz for better visualization
  const chartDataByQuiz = chartData.reduce((acc, item) => {
    const quizKey = item.quizTitle
    if (!acc[quizKey]) {
      acc[quizKey] = []
    }
    acc[quizKey].push(item)
    return acc
  }, {})

  // Summary statistics for pie chart
  const summaryData = statsData?.summary ? [
    { name: 'Correct', value: parseInt(statsData.summary.total_correct) || 0, color: '#52c41a' },
    { name: 'Incorrect', value: parseInt(statsData.summary.total_incorrect) || 0, color: '#ff4d4f' }
  ] : []

  // Table columns for detailed view
  const columns = [
    {
      title: 'Quiz',
      dataIndex: 'quiz_title',
      key: 'quiz_title',
      width: 150,
    },
    {
      title: 'Question',
      dataIndex: 'question_text',
      key: 'question_text',
      render: (text) => (
        <div style={{ maxWidth: 300 }}>
          {text.length > 100 ? text.substring(0, 100) + '...' : text}
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'question_type',
      key: 'question_type',
      width: 120,
      render: (type) => {
        const typeMap = {
          'multiple_choice': 'Multiple Choice',
          'true_false': 'True/False',
          'short_answer': 'Short Answer',
          'multi_choice': 'Multi-Select'
        }
        return typeMap[type] || type
      }
    },
    {
      title: 'Total Attempts',
      dataIndex: 'total_attempts',
      key: 'total_attempts',
      width: 120,
      align: 'center',
    },
    {
      title: 'Correct',
      dataIndex: 'correct_answers',
      key: 'correct_answers',
      width: 100,
      align: 'center',
      render: (value) => <span style={{ color: '#52c41a', fontWeight: 'bold' }}>{value}</span>
    },
    {
      title: 'Incorrect',
      dataIndex: 'incorrect_answers',
      key: 'incorrect_answers',
      width: 100,
      align: 'center',
      render: (value) => <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>{value}</span>
    },
    {
      title: 'Success Rate',
      dataIndex: 'correct_percentage',
      key: 'correct_percentage',
      width: 120,
      align: 'center',
      render: (value) => (
        <span style={{ 
          color: value >= 70 ? '#52c41a' : value >= 50 ? '#faad14' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {value}%
        </span>
      )
    },
    {
      title: 'Unique Users',
      dataIndex: 'unique_users_attempted',
      key: 'unique_users_attempted',
      width: 120,
      align: 'center',
    }
  ]

  return (
    <div>
      {/* Filters */}
      <Card style={{ marginBottom: 24 }}>
        <Title level={4}>
          <QuestionCircleOutlined style={{ marginRight: 8 }} />
          Question Analytics Filters
        </Title>
        <Space wrap>
          <Select
            placeholder="Select Module"
            style={{ width: 200 }}
            allowClear
            value={filters.moduleId}
            onChange={handleModuleChange}
          >
            {(modulesData || []).map(module => (
              <Option key={module.id} value={module.id}>
                {module.title}
              </Option>
            ))}
          </Select>

          <Select
            placeholder="Select Quiz"
            style={{ width: 200 }}
            allowClear
            value={filters.quizId}
            onChange={(value) => setFilters(prev => ({ ...prev, quizId: value }))}
            disabled={!filters.moduleId}
          >
            {(quizzesData || []).map(quiz => (
              <Option key={quiz.id} value={quiz.id}>
                {quiz.title} ({quiz.language_code})
              </Option>
            ))}
          </Select>

          <Select
            placeholder="Question Type"
            style={{ width: 150 }}
            allowClear
            value={filters.questionType}
            onChange={(value) => setFilters(prev => ({ ...prev, questionType: value }))}
          >
            <Option value="multiple_choice">Multiple Choice</Option>
            <Option value="true_false">True/False</Option>
            <Option value="short_answer">Short Answer</Option>
            <Option value="multi_choice">Multi-Select</Option>
          </Select>

          <RangePicker
            placeholder={['Start Date', 'End Date']}
            onChange={handleDateRangeChange}
          />
        </Space>
      </Card>

      {statsLoading ? (
        <div style={{ textAlign: 'center', padding: 40 }}>
          <Spin size="large" />
        </div>
      ) : statsError ? (
        <Alert
          message="Error Loading Question Statistics"
          description={handleAPIError(statsError).message}
          type="error"
          showIcon
        />
      ) : !statsData ? (
        <div style={{ textAlign: 'center', padding: 40 }}>
          <QuestionCircleOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={4} type="secondary">No Data Available</Title>
          <Text type="secondary">
            Please select filters to view question statistics
          </Text>
        </div>
      ) : (
        <>
          {/* Summary Statistics */}
          <Card style={{ marginBottom: 24 }}>
            <Title level={4}>
              <BarChartOutlined style={{ marginRight: 8 }} />
              Summary Statistics
            </Title>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="Total Questions"
                  value={statsData.summary?.total_questions || 0}
                  prefix={<QuestionCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Total Attempts"
                  value={statsData.summary?.total_question_attempts || 0}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Overall Success Rate"
                  value={statsData.summary?.overall_correct_percentage || 0}
                  suffix="%"
                  valueStyle={{ 
                    color: (statsData.summary?.overall_correct_percentage || 0) >= 70 ? '#3f8600' : '#cf1322' 
                  }}
                />
              </Col>
              <Col span={6}>
                <div style={{ height: 120 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={summaryData}
                        cx="50%"
                        cy="50%"
                        innerRadius={20}
                        outerRadius={40}
                        dataKey="value"
                      >
                        {summaryData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Charts by Quiz */}
          {Object.entries(chartDataByQuiz).map(([quizTitle, quizData]) => (
            <Card key={quizTitle} style={{ marginBottom: 24 }}>
              <Title level={5}>{quizTitle} - Question Performance</Title>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={quizData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [value, name === 'correct' ? 'Correct' : 'Incorrect']}
                    labelFormatter={(label) => {
                      const item = quizData.find(d => d.name === label)
                      return item ? `${label}: ${item.questionText}` : label
                    }}
                  />
                  <Legend />
                  <Bar dataKey="correct" fill="#52c41a" name="Correct" />
                  <Bar dataKey="incorrect" fill="#ff4d4f" name="Incorrect" />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          ))}

          {/* Detailed Table */}
          <Card>
            <Title level={4}>Detailed Question Statistics</Title>
            <Table
              columns={columns}
              dataSource={statsData.questionStats || []}
              rowKey="question_id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
              }}
              scroll={{ x: 1200 }}
            />
          </Card>
        </>
      )}
    </div>
  )
}

export default QuestionStatistics
