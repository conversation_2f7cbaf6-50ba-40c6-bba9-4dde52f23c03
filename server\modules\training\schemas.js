const Joi = require('joi');

// Validation schemas for training operations
const createModuleSchema = Joi.object({
  title: Joi.string().min(1).max(255).required(),
  description: Joi.string().max(1000).optional(),
});

const uploadVideoSchema = Joi.object({
  moduleId: Joi.number().integer().positive().required(),
  languageCode: Joi.string().min(2).max(10).required(),
  languageName: Joi.string().min(1).max(50).required(),
});

const updateProgressSchema = Joi.object({
  watchTime: Joi.number().integer().min(0).required(),
  completed: Joi.boolean().optional(),
  languageCode: Joi.string().min(2).max(10).optional(),
  showMessage: Joi.boolean().optional(),
  forceOverwrite: Joi.boolean().optional(),
});

module.exports = {
  createModuleSchema,
  uploadVideoSchema,
  updateProgressSchema
};
