/**
 * Quiz Results Component
 * Extracted from Quiz.jsx for better modularity
 */

import React from 'react'
import { Card, Button, Typography, Space, Progress } from 'antd'
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  TrophyOutlined, 
  ArrowLeftOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const { Title, Text, Paragraph } = Typography

function QuizResults({
  results,
  quiz,
  onRetakeQuiz,
  onViewCertificate,
  onBackToModules,
  showRetakeOption = true,
  showCertificateOption = true
}) {
  const { t } = useTranslation()

  if (!results) {
    return null
  }

  const { passed, score, correctAnswers, totalQuestions, attemptNumber } = results

  const getScoreColor = (score) => {
    if (score >= 80) return '#52c41a'
    if (score >= 60) return '#faad14'
    return '#ff4d4f'
  }

  const getResultIcon = () => {
    if (passed) {
      return <CheckCircleOutlined style={{ fontSize: 64, color: '#52c41a' }} />
    }
    return <CloseCircleOutlined style={{ fontSize: 64, color: '#ff4d4f' }} />
  }

  const getResultTitle = () => {
    if (passed) {
      return (
        <Title level={2} style={{ color: '#52c41a' }}>
          {t('quiz.quizPassed')}
        </Title>
      )
    }
    return (
      <Title level={2} style={{ color: '#ff4d4f' }}>
        {t('quiz.quizNotPassed')}
      </Title>
    )
  }

  const getResultMessage = () => {
    if (passed) {
      return (
        <Paragraph style={{ fontSize: 16 }}>
          {t('quiz.congratulationsPassed')}
        </Paragraph>
      )
    }
    return (
      <Paragraph style={{ fontSize: 16 }}>
        {t('quiz.quizNotPassedTryAgain')}
      </Paragraph>
    )
  }

  return (
    <div>
      {/* Back Button */}
      <div style={{ marginBottom: 24 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={onBackToModules}
          style={{ marginBottom: 16 }}
        >
          {t('quiz.backToModules')}
        </Button>
      </div>

      {/* Results Card */}
      <Card style={{ textAlign: 'center', maxWidth: 600, margin: '0 auto' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {getResultIcon()}
          
          <div>
            {getResultTitle()}
            {getResultMessage()}
          </div>

          {/* Score Display */}
          <div style={{ width: '100%' }}>
            <Progress
              type="circle"
              percent={score}
              strokeColor={getScoreColor(score)}
              format={(percent) => `${percent}%`}
              size={120}
            />
            <div style={{ marginTop: 16 }}>
              <Text strong style={{ fontSize: 18 }}>
                {t('quiz.score')}: {score}%
              </Text>
              <br />
              <Text type="secondary">
                {t('quiz.correctAnswers')}: {correctAnswers}/{totalQuestions}
              </Text>
              <br />
              <Text type="secondary">
                {t('quiz.attemptNumber', { number: attemptNumber })}
              </Text>
            </div>
          </div>

          {/* Quiz Information */}
          <div style={{ 
            background: '#f8f9fa', 
            padding: 16, 
            borderRadius: 6,
            width: '100%',
            textAlign: 'left'
          }}>
            <Text strong>{quiz?.title}</Text>
            <br />
            <Text type="secondary">
              {t('quiz.passingScore')}: {quiz?.passing_score}%
            </Text>
            {quiz?.time_limit_minutes && (
              <>
                <br />
                <Text type="secondary">
                  {t('quiz.timeLimit')}: {quiz.time_limit_minutes} {t('quiz.minutes')}
                </Text>
              </>
            )}
          </div>

          {/* Action Buttons */}
          <Space size="large">
            {passed && showCertificateOption && (
              <Button
                type="primary"
                size="large"
                icon={<TrophyOutlined />}
                onClick={onViewCertificate}
              >
                {t('quiz.viewCertificate')}
              </Button>
            )}

            {!passed && showRetakeOption && (
              <Button
                type="primary"
                size="large"
                icon={<ReloadOutlined />}
                onClick={onRetakeQuiz}
              >
                {t('quiz.retakeQuiz')}
              </Button>
            )}

            <Button
              size="large"
              onClick={onBackToModules}
            >
              {t('quiz.backToModules')}
            </Button>
          </Space>
        </Space>
      </Card>
    </div>
  )
}

export default QuizResults
