import axios from 'axios'

// Create axios instance with default config
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 300000, // 5 minutes timeout for large uploads
})

// Function to get token from localStorage
const getToken = () => {
  return localStorage.getItem('authToken');
}

// Function to set token in localStorage
const setToken = (token) => {
  if (token) {
    localStorage.setItem('authToken', token);
  } else {
    localStorage.removeItem('authToken');
  }
}

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Only log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('API Request:', config.method?.toUpperCase(), config.url, config.baseURL)
    }

    // Add JWT token to requests
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login on unauthorized
      setToken(null);
      // Use history API instead of direct window.location for better SPA behavior
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getMe: () => api.get('/auth/me'),
  checkStatus: () => api.get('/auth/status'),
  changePassword: (data) => api.post('/auth/change-password', data),
}

// Users API
export const usersAPI = {
  getAll: () => api.get('/users'),
  getProfile: () => api.get('/users/profile'),
  getCertificate: (moduleId, languageCode) => api.get(`/users/certificate/${moduleId}/${languageCode}`),
  updateOwnLanguage: (language) => api.put('/users/my-language', { language }),
  bulkCreate: (emails) => api.post('/users/bulk-create', { emails }),
  update: (email, data) => api.put(`/users/${email}`, data),
  updateLanguage: (email, language) => api.put(`/users/${email}/language`, { language }),
  resetPassword: (email) => api.post(`/users/${email}/reset-password`),
  sendLoginEmail: (email) => api.post(`/users/${email}/send-login-email`),
  delete: (email) => api.delete(`/users/${email}`),
  deleteAllProgress: () => api.delete('/users/progress/all'),
}

// Training API
export const trainingAPI = {
  getModules: () => api.get('/training/modules'),
  getModule: (id) => api.get(`/training/modules/${id}`),
  getProgress: (moduleId) => api.get(`/training/modules/${moduleId}/progress`),
  updateProgress: (moduleId, data) => api.post(`/training/modules/${moduleId}/progress`, data),
  deleteOtherLanguagesProgress: (moduleId, keepLanguageCode) => api.delete(`/training/modules/${moduleId}/progress/other-languages`, {
    data: { keepLanguageCode }
  }),
  createModule: (data) => api.post('/training/modules', data),
  toggleModuleStatus: (id, isActive) => api.patch(`/training/modules/${id}/toggle-status`, { isActive }),
  deleteModule: (id) => api.delete(`/training/modules/${id}`),
  updateVideoLanguageName: (videoId, languageName) => api.patch(`/training/videos/${videoId}/language-name`, { languageName }),
  deleteVideo: (videoId) => api.delete(`/training/videos/${videoId}`),

  // Traditional single-file upload (kept as fallback)
  uploadVideo: (moduleId, formData) => api.post(`/training/modules/${moduleId}/videos`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 0, // Infinite timeout for slow internet connections
  }),

  // Video replacement API (chunked upload endpoints)
  // Note: Video replacement now uses chunked uploads through VideoReplacementService
  // The endpoints are:
  // POST /training/modules/:id/videos/:languageCode/replace/initiate
  // POST /training/modules/:id/videos/:languageCode/replace/chunk
  // POST /training/modules/:id/videos/:languageCode/replace/finalize
  // POST /training/modules/:id/videos/:languageCode/replace/cancel

  // Chunked upload endpoints
  initiateChunkedUpload: (moduleId, data) => api.post(`/training/modules/${moduleId}/videos/initiate`, data),
  uploadChunk: (moduleId, formData) => api.post(`/training/modules/${moduleId}/videos/chunk`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 0, // No timeout - infinite wait
  }),
  finalizeChunkedUpload: (moduleId, data) => api.post(`/training/modules/${moduleId}/videos/finalize`, data),
  cancelChunkedUpload: (moduleId, uploadId) => api.post(`/training/modules/${moduleId}/videos/cancel/${uploadId}`),
  cleanupChunkedUpload: (moduleId, uploadId) => api.delete(`/training/modules/${moduleId}/videos/cleanup/${uploadId}`),
  getChunkedUploadStatus: (moduleId, uploadId) => api.get(`/training/modules/${moduleId}/videos/status/${uploadId}`),
  cleanupOrphanedChunks: () => api.post('/training/admin/cleanup-orphaned-chunks'),
}

// Quiz API
export const quizAPI = {
  // For taking quizzes (by module and language)
  getQuiz: (moduleId, languageCode) => api.get(`/quiz/modules/${moduleId}/quiz/${languageCode}`),
  submitQuiz: (moduleId, languageCode, answers) => api.post(`/quiz/modules/${moduleId}/quiz/${languageCode}/submit`, { answers }),

  // For admin management (by video ID)
  getQuizForEdit: (videoId) => api.get(`/quiz/videos/${videoId}/quiz/edit`),
  createQuiz: (videoId, data) => api.post(`/quiz/videos/${videoId}/quiz`, data),
  updateQuiz: (videoId, data) => api.put(`/quiz/videos/${videoId}/quiz`, data),
  deleteQuiz: (videoId) => api.delete(`/quiz/videos/${videoId}/quiz`),
}

// Reports API
export const reportsAPI = {
  getComplianceReport: (params) => api.get('/reports/compliance', { params }),
  getCompletionStats: (params) => api.get('/reports/completion-stats', { params }),
  getUserActivity: (params) => api.get('/reports/user-activity', { params }),
  getQuestionStatistics: (params) => api.get('/reports/question-statistics', { params }),
  getAvailableQuizzes: (params) => api.get('/reports/available-quizzes', { params }),
  // Quiz Results API
  getQuizResultsOverview: (params) => api.get('/reports/quiz-results-overview', { params }),
  getQuizResultsDetail: (quizId) => api.get(`/reports/quiz-results-detail/${quizId}`),
  getQuizResultsUserDetail: (quizId, userEmail) => api.get(`/reports/quiz-results-user/${quizId}/${encodeURIComponent(userEmail)}`),
  downloadComplianceReport: (params) => {
    return api.get('/reports/compliance', {
      params: { ...params, format: 'html' },
      responseType: 'blob'
    })
  },
}

// Admin API
export const adminAPI = {
  resetDatabase: () => api.post('/admin/reset-database'),
  clearUploads: () => api.post('/admin/clear-uploads'),
}

// Email Configuration API
export const emailConfigAPI = {
  getConfig: () => api.get('/email-config'),
  updateConfig: (config) => api.put('/email-config', config),
  testConfig: (config) => api.post('/email-config/test', config),
}

// Helper function to handle API errors
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const message = error.response.data?.error || error.response.data?.message || 'An error occurred'
    return {
      message,
      status: error.response.status,
      details: error.response.data?.details,
    }
  } else if (error.request) {
    // Request was made but no response received
    return {
      message: 'Network error - please check your connection',
      status: 0,
    }
  } else {
    // Something else happened
    return {
      message: error.message || 'An unexpected error occurred',
      status: 0,
    }
  }
}

// Helper function to download file from blob response
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

export default api
export { getToken, setToken }
