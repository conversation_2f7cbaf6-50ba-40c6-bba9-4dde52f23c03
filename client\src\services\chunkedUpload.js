import api from './api'

// Development-only logging functions
const devError = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.error(...args)
  }
}

const devWarn = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.warn(...args)
  }
}

const CHUNK_SIZE = 10 * 1024 * 1024 // 10MB chunks

/**
 * Chunked file upload service for large video files
 */
export class ChunkedUploadService {
  constructor() {
    this.uploadId = null
    this.file = null
    this.chunks = []
    this.uploadedChunks = new Set()
    this.onProgress = null
    this.onError = null
    this.onComplete = null
    this.aborted = false
  }

  /**
   * Calculate file chunks
   */
  calculateChunks(file) {
    const chunks = []
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE)
    
    for (let i = 0; i < totalChunks; i++) {
      const start = i * CHUNK_SIZE
      const end = Math.min(start + CHUNK_SIZE, file.size)
      
      chunks.push({
        index: i,
        start,
        end,
        size: end - start,
        blob: file.slice(start, end)
      })
    }
    
    return chunks
  }

  /**
   * Initialize chunked upload
   */
  async initiate(moduleId, file, metadata) {
    try {
      this.file = file
      this.chunks = this.calculateChunks(file)
      this.uploadedChunks.clear()
      this.aborted = false

      const response = await api.post(`/training/modules/${moduleId}/videos/initiate`, {
        filename: file.name,
        fileSize: file.size,
        mimeType: file.type,
        totalChunks: this.chunks.length,
        chunkSize: CHUNK_SIZE,
        ...metadata
      })

      this.uploadId = response.data.uploadId
      
      return {
        uploadId: this.uploadId,
        totalChunks: this.chunks.length,
        fileSize: file.size
      }
    } catch (error) {
      this.handleError('Failed to initiate upload', error)
      throw error
    }
  }

  /**
   * Upload a single chunk
   */
  async uploadChunk(moduleId, chunkIndex, retryCount = 0) {
    if (this.aborted) {
      throw new Error('Upload aborted')
    }

    const chunk = this.chunks[chunkIndex]
    if (!chunk) {
      throw new Error(`Chunk ${chunkIndex} not found`)
    }

    const formData = new FormData()
    formData.append('uploadId', this.uploadId)
    formData.append('chunkIndex', chunkIndex.toString())
    formData.append('chunk', chunk.blob)
    formData.append('chunkSize', chunk.size.toString())

    try {
      await api.post(`/training/modules/${moduleId}/videos/chunk`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 0, // No timeout - infinite wait
      })

      this.uploadedChunks.add(chunkIndex)
      this.updateProgress()
      
      return true
    } catch (error) {
      // Retry logic for failed chunks
      if (retryCount < 3) {
        devWarn(`Chunk ${chunkIndex} failed, retrying (${retryCount + 1}/3)...`)
        await this.delay(1000 * (retryCount + 1)) // Exponential backoff
        return this.uploadChunk(moduleId, chunkIndex, retryCount + 1)
      } else {
        this.handleError(`Failed to upload chunk ${chunkIndex}`, error)
        throw error
      }
    }
  }

  /**
   * Upload all chunks
   */
  async uploadAllChunks(moduleId, concurrency = 3) {
    let activeUploads = 0
    let chunkIndex = 0

    return new Promise((resolve, reject) => {
      const uploadNext = async () => {
        if (this.aborted) {
          reject(new Error('Upload aborted'))
          return
        }

        if (chunkIndex >= this.chunks.length) {
          if (activeUploads === 0) {
            resolve()
          }
          return
        }

        if (activeUploads >= concurrency) {
          return
        }

        const currentChunkIndex = chunkIndex++
        activeUploads++

        try {
          await this.uploadChunk(moduleId, currentChunkIndex)
          activeUploads--
          uploadNext() // Start next upload
          
          if (this.uploadedChunks.size === this.chunks.length && activeUploads === 0) {
            resolve()
          }
        } catch (error) {
          activeUploads--
          reject(error)
        }
      }

      // Start initial uploads
      for (let i = 0; i < Math.min(concurrency, this.chunks.length); i++) {
        uploadNext()
      }
    })
  }

  /**
   * Finalize upload and reassemble file
   */
  async finalize(moduleId, metadata) {
    try {
      const response = await api.post(`/training/modules/${moduleId}/videos/finalize`, {
        uploadId: this.uploadId,
        ...metadata
      })

      if (this.onComplete) {
        this.onComplete(response.data)
      }

      return response.data
    } catch (error) {
      this.handleError('Failed to finalize upload', error)
      throw error
    }
  }

  /**
   * Complete chunked upload process
   */
  async upload(moduleId, file, metadata, options = {}) {
    try {
      // Set callbacks
      this.onProgress = options.onProgress
      this.onError = options.onError
      this.onComplete = options.onComplete

      // Step 1: Initiate upload
      await this.initiate(moduleId, file, metadata)
      
      // Step 2: Upload all chunks
      await this.uploadAllChunks(moduleId, options.concurrency || 3)
      
      // Step 3: Finalize upload
      const result = await this.finalize(moduleId, metadata)
      
      return result
    } catch (error) {
      // Cleanup on error
      if (this.uploadId) {
        this.cleanup(moduleId).catch(console.error)
      }
      throw error
    }
  }

  /**
   * Abort upload and clean up server-side chunks
   */
  async abort(moduleId) {
    this.aborted = true

    // If we have an uploadId, notify the server to cancel and cleanup
    if (this.uploadId && moduleId) {
      try {
        await api.post(`/training/modules/${moduleId}/videos/cancel/${this.uploadId}`)
        if (process.env.NODE_ENV === 'development') {
          console.log(`Upload ${this.uploadId} cancelled and cleaned up on server`)
        }
      } catch (error) {
        devError('Failed to cancel upload on server:', error)
        // Fallback to cleanup endpoint
        try {
          await this.cleanup(moduleId)
        } catch (cleanupError) {
          devError('Failed to cleanup upload:', cleanupError)
        }
      }
    }
  }

  /**
   * Cleanup failed upload
   */
  async cleanup(moduleId) {
    if (!this.uploadId) return

    try {
      await api.delete(`/training/modules/${moduleId}/videos/cleanup/${this.uploadId}`)
    } catch (error) {
      devError('Failed to cleanup upload:', error)
    }
  }

  /**
   * Update progress
   */
  updateProgress() {
    if (this.onProgress && this.chunks.length > 0) {
      const progress = {
        uploadedChunks: this.uploadedChunks.size,
        totalChunks: this.chunks.length,
        percentage: Math.round((this.uploadedChunks.size / this.chunks.length) * 100),
        uploadedBytes: this.uploadedChunks.size * CHUNK_SIZE,
        totalBytes: this.file.size
      }
      
      this.onProgress(progress)
    }
  }

  /**
   * Handle errors
   */
  handleError(message, error) {
    devError(message, error)
    if (this.onError) {
      this.onError(message, error)
    }
  }

  /**
   * Delay utility
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

export default ChunkedUploadService
