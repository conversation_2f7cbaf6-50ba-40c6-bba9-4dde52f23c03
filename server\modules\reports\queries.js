const { query } = require('../../database/init');

/**
 * Get comprehensive training data for compliance reports
 */
async function getComplianceReportData(whereConditions, queryParams) {
  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
  
  return await query(`
    SELECT 
      u.email,
      u.first_name,
      u.last_name,
      u.created_at as user_created_at,
      tm.id as module_id,
      tm.title as module_title,
      tm.description as module_description,
      up.language_code,
      up.video_watched,
      up.video_completed_at,
      up.quiz_completed,
      up.quiz_passed,
      up.quiz_score,
      up.quiz_completed_at,
      up.certificate_generated,
      up.certificate_generated_at,
      c.certificate_number,
      c.issued_at as certificate_issued_at,
      (
        SELECT COUNT(*)
        FROM quiz_attempts qa
        WHERE qa.user_email = u.email
        AND qa.quiz_id IN (
          SELECT q.id FROM quizzes q
          JOIN training_videos tv ON q.video_id = tv.id
          WHERE tv.module_id = tm.id AND tv.language_code = up.language_code
        )
      ) as total_attempts,
      (
        SELECT MAX(qa.score)
        FROM quiz_attempts qa
        WHERE qa.user_email = u.email
        AND qa.quiz_id IN (
          SELECT q.id FROM quizzes q
          JOIN training_videos tv ON q.video_id = tv.id
          WHERE tv.module_id = tm.id AND tv.language_code = up.language_code
        )
      ) as best_score
    FROM users u
    LEFT JOIN user_progress up ON u.email = up.user_email
    LEFT JOIN training_modules tm ON up.module_id = tm.id
    LEFT JOIN certificates c ON (
      c.user_email = u.email 
      AND c.module_id = tm.id 
      AND c.language_code = up.language_code
    )
    ${whereClause}
    ORDER BY u.email, tm.title, up.language_code
  `, queryParams);
}

/**
 * Get summary statistics for compliance reports
 */
async function getComplianceSummaryData(whereConditions, queryParams, moduleId) {
  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
  
  return await query(`
    SELECT
      COUNT(DISTINCT u.email) as total_users,
      COUNT(DISTINCT tm.id) as total_modules,
      COUNT(DISTINCT CASE WHEN up.video_watched = true THEN u.email || '-' || tm.id || '-' || up.language_code END) as users_completed_videos,
      COUNT(DISTINCT CASE WHEN up.quiz_passed = true THEN u.email || '-' || tm.id || '-' || up.language_code END) as users_passed_quizzes,
      -- Count actual certificates from certificates table
      (SELECT COUNT(*) FROM certificates c
       JOIN training_modules tm2 ON c.module_id = tm2.id
       ${moduleId ? 'WHERE tm2.id = $' + (queryParams.length + 1) : ''}) as certificates_issued,
      ROUND(AVG(CASE WHEN up.quiz_score IS NOT NULL THEN up.quiz_score END), 2) as average_quiz_score
    FROM users u
    LEFT JOIN user_progress up ON u.email = up.user_email
    LEFT JOIN training_modules tm ON up.module_id = tm.id
    ${whereClause}
  `, moduleId ? [...queryParams, moduleId] : queryParams);
}

/**
 * Get completion statistics for modules
 */
async function getCompletionStats(moduleFilter, queryParams) {
  return await query(`
    SELECT
      tm.id as module_id,
      tm.title as module_title,
      COUNT(DISTINCT tv.language_code) as available_languages,
      -- Count only users who actually have progress (are enrolled)
      COUNT(DISTINCT up.user_email) as total_enrolled_users,
      -- Count users who completed videos (per user, not per language)
      COUNT(DISTINCT CASE WHEN up.video_watched = true THEN up.user_email END) as users_watched_video,
      -- Count users who attempted quizzes (per user, not per language)
      COUNT(DISTINCT CASE WHEN up.quiz_completed = true THEN up.user_email END) as users_attempted_quiz,
      -- Count users who passed quizzes (per user, not per language)
      COUNT(DISTINCT CASE WHEN up.quiz_passed = true THEN up.user_email END) as users_passed_quiz,
      -- Count actual certificates from certificates table (not just the flag)
      (SELECT COUNT(*) FROM certificates c WHERE c.module_id = tm.id) as certificates_issued,
      -- Average score across all quiz attempts for this module
      ROUND(AVG(CASE WHEN up.quiz_score IS NOT NULL THEN up.quiz_score END), 2) as average_score,
      -- Additional useful metrics - users who have actual certificates
      COUNT(DISTINCT CASE WHEN EXISTS (
        SELECT 1 FROM certificates c
        WHERE c.user_email = up.user_email
        AND c.module_id = tm.id
        AND c.language_code = up.language_code
      ) THEN up.user_email END) as users_fully_completed
    FROM training_modules tm
    LEFT JOIN training_videos tv ON tm.id = tv.module_id
    LEFT JOIN user_progress up ON tm.id = up.module_id AND up.user_email IN (SELECT email FROM users WHERE is_active = true)
    ${moduleFilter}
    GROUP BY tm.id, tm.title
    ORDER BY tm.title
  `, queryParams);
}

/**
 * Get user activity data
 */
async function getUserActivityData(whereConditions, queryParams) {
  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
  
  return await query(`
    SELECT
      u.email,
      COALESCE(NULLIF(u.first_name, ''),
        CASE
          WHEN POSITION('.' IN SPLIT_PART(u.email, '@', 1)) > 0 THEN
            INITCAP(SPLIT_PART(SPLIT_PART(u.email, '@', 1), '.', 1))
          ELSE
            INITCAP(SPLIT_PART(u.email, '@', 1))
        END
      ) as first_name,
      COALESCE(NULLIF(u.last_name, ''),
        CASE
          WHEN POSITION('.' IN SPLIT_PART(u.email, '@', 1)) > 0 THEN
            INITCAP(SPLIT_PART(SPLIT_PART(u.email, '@', 1), '.', 2))
          ELSE
            ''
        END
      ) as last_name,
      tm.title as module_title,
      tv.language_code,
      qa.attempt_number,
      qa.score,
      qa.passed,
      qa.completed_at,
      qa.time_taken_seconds
    FROM quiz_attempts qa
    JOIN users u ON qa.user_email = u.email
    JOIN quizzes q ON qa.quiz_id = q.id
    JOIN training_videos tv ON q.video_id = tv.id
    JOIN training_modules tm ON tv.module_id = tm.id
    ${whereClause}
    ORDER BY qa.completed_at DESC
  `, queryParams);
}

/**
 * Get total certificate count
 */
async function getTotalCertificateCount() {
  const result = await query('SELECT COUNT(*) as total_certificates FROM certificates');
  return parseInt(result.rows[0].total_certificates);
}

module.exports = {
  getComplianceReportData,
  getComplianceSummaryData,
  getCompletionStats,
  getUserActivityData,
  getTotalCertificateCount
};
