const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { query, getClient } = require('../database/init');
const { requireAuth, requireAdmin } = require('../middleware/auth');
const emailService = require('../services/emailService');
const systemConfigService = require('../services/systemConfigService');
const { hashPassword, generateRandomPassword } = require('../modules/users/password');
const { createUserSchema, updateUserSchema } = require('../modules/users/schemas');

const router = express.Router();

// Validation schemas
const bulkCreateUsersSchema = require('joi').object({
  emails: require('joi').string().required(), // Semicolon-separated email list
});

// Unified function to create/update user password and hash
async function createUserPasswordHash(email, context = 'unknown') {
  console.log(`🔑 Creating password hash for ${email} (${context})`);

  const password = generateRandomPassword(12, context);
  const passwordHash = await hashPassword(password);

  console.log(`✅ Password hash created for ${email}:`, {
    passwordLength: password.length,
    hashLength: passwordHash.length,
    hashPrefix: passwordHash.substring(0, 20),
    context: context
  });

  return { password, passwordHash };
}

// Get all users (admin only)
router.get('/', requireAdmin, async (req, res) => {
  try {
    // Get password expiration days from system config
    const passwordExpirationDays = await systemConfigService.getPasswordExpirationDays();

    const result = await query(`
      SELECT
        email,
        first_name,
        last_name,
        is_admin,
        is_active,
        language,
        created_at,
        last_login,
        password_created_at,
        is_default_admin,
        CASE
          WHEN is_admin = true THEN false
          WHEN password_created_at IS NULL THEN true
          WHEN password_created_at < NOW() - INTERVAL '${passwordExpirationDays} days' THEN true
          ELSE false
        END as password_expired,
        CASE
          WHEN is_admin = true THEN null
          WHEN password_created_at IS NULL THEN 0
          ELSE EXTRACT(days FROM (NOW() - password_created_at))::integer
        END as password_age_days,
        (SELECT COUNT(*) FROM certificates WHERE user_email = users.email) as completed_trainings
      FROM users
      ORDER BY created_at DESC, email ASC
    `);

    res.json({ users: result.rows });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get user profile
router.get('/profile', requireAuth, async (req, res) => {
  try {
    const userEmail = req.user.email;
    
    const userResult = await query(`
      SELECT
        email,
        first_name,
        last_name,
        full_name,
        is_admin,
        created_at,
        last_login
      FROM users
      WHERE email = $1
    `, [userEmail]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get user's training progress with certificate data (only for active modules)
    const progressResult = await query(`
      SELECT
        up.module_id,
        tm.title as module_title,
        up.language_code,
        up.video_watched,
        up.quiz_completed,
        up.quiz_passed,
        up.quiz_score,
        up.certificate_generated,
        up.certificate_generated_at,
        up.quiz_completed_at,
        c.certificate_number,
        c.certificate_data,
        c.issued_at as certificate_issued_at
      FROM user_progress up
      JOIN training_modules tm ON up.module_id = tm.id
      LEFT JOIN certificates c ON c.user_email = up.user_email
        AND c.module_id = up.module_id
        AND c.language_code = up.language_code
      WHERE up.user_email = $1 AND tm.is_active = true
      ORDER BY up.updated_at DESC
    `, [userEmail]);

    res.json({ 
      user: userResult.rows[0],
      progress: progressResult.rows
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get certificate for a specific module and language
router.get('/certificate/:moduleId/:languageCode', requireAuth, async (req, res) => {
  try {
    const { moduleId, languageCode } = req.params;
    const userEmail = req.user.email;

    console.log('=== GET CERTIFICATE DEBUG ===');
    console.log('moduleId:', moduleId);
    console.log('languageCode:', languageCode);
    console.log('userEmail:', userEmail);

    // Get certificate data
    const certificateResult = await query(`
      SELECT
        c.*,
        tm.title as module_title,
        up.quiz_score,
        up.quiz_completed_at
      FROM certificates c
      JOIN training_modules tm ON c.module_id = tm.id
      JOIN user_progress up ON up.user_email = c.user_email
        AND up.module_id = c.module_id
        AND up.language_code = c.language_code
      WHERE c.user_email = $1 AND c.module_id = $2 AND c.language_code = $3
    `, [userEmail, moduleId, languageCode]);

    if (certificateResult.rows.length === 0) {
      return res.status(404).json({ error: 'Certificate not found' });
    }

    const certificate = certificateResult.rows[0];
    console.log('Found certificate:', certificate.certificate_number);

    res.json({ certificate });
  } catch (error) {
    console.error('Get certificate error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Bulk create users from email list (admin only)
router.post('/bulk-create', requireAdmin, async (req, res) => {
  try {
    // Validate input
    const { error, value } = bulkCreateUsersSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: error.details[0].message 
      });
    }

    const { emails } = value;
    
    // Parse email list
    const emailList = emails
      .split(';')
      .map(email => email.trim().toLowerCase())
      .filter(email => email.length > 0);

    if (emailList.length === 0) {
      return res.status(400).json({ error: 'No valid emails provided' });
    }

    // Validate email formats
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = emailList.filter(email => !emailRegex.test(email));
    
    if (invalidEmails.length > 0) {
      return res.status(400).json({ 
        error: 'Invalid email formats', 
        invalidEmails 
      });
    }

    const client = await getClient();
    const createdUsers = [];
    const existingUsers = [];
    const errors = [];

    try {
      await client.query('BEGIN');

      for (const email of emailList) {
        try {
          // Check if user already exists
          const existingResult = await client.query(
            'SELECT email FROM users WHERE email = $1',
            [email]
          );

          if (existingResult.rows.length > 0) {
            existingUsers.push(email);
            continue;
          }

          // Generate random password using unified function
          const { password, passwordHash } = await createUserPasswordHash(email, 'bulk-create');

          // Create user
          await client.query(`
            INSERT INTO users (email, password_hash, is_admin, is_active, language, password_created_at)
            VALUES ($1, $2, false, true, 'en', CURRENT_TIMESTAMP)
          `, [email, passwordHash]);

          createdUsers.push({
            email,
            password, // Include password in response for admin to copy
          });

        } catch (userError) {
          console.error(`Error creating user ${email}:`, userError);
          errors.push({ email, error: userError.message });
        }
      }

      await client.query('COMMIT');

      res.json({
        message: `Bulk user creation completed`,
        summary: {
          total: emailList.length,
          created: createdUsers.length,
          existing: existingUsers.length,
          errors: errors.length,
        },
        createdUsers,
        existingUsers,
        errors,
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Bulk create users error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user's own language preference - MUST come before /:email route
router.put('/my-language', requireAuth, async (req, res) => {
  try {
    const userEmail = req.user.email;
    const { language } = req.body;

    // Validate language
    if (!language || !['en', 'zh'].includes(language)) {
      return res.status(400).json({ error: 'Invalid language. Must be "en" or "zh"' });
    }

    // Update user language
    await query('UPDATE users SET language = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2', [language, userEmail]);

    console.log(`Updated language for user ${userEmail} to ${language}`);

    res.json({
      message: 'Language preference updated successfully',
      language: language
    });

  } catch (error) {
    console.error('Update user language error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user (admin only)
router.put('/:email', requireAdmin, async (req, res) => {
  try {
    const { email } = req.params;
    
    // Validate input
    const { error, value } = updateUserSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: error.details[0].message 
      });
    }

    const { firstName, lastName, isActive } = value;

    // Check if user exists
    const existingResult = await query(
      'SELECT email FROM users WHERE email = $1',
      [email.toLowerCase()]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    if (firstName !== undefined) {
      updateFields.push(`first_name = $${paramIndex++}`);
      updateValues.push(firstName);
    }
    if (lastName !== undefined) {
      updateFields.push(`last_name = $${paramIndex++}`);
      updateValues.push(lastName);
    }
    if (isActive !== undefined) {
      updateFields.push(`is_active = $${paramIndex++}`);
      updateValues.push(isActive);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(email.toLowerCase());

    const updateQuery = `
      UPDATE users 
      SET ${updateFields.join(', ')} 
      WHERE email = $${paramIndex}
      RETURNING email, first_name, last_name, is_admin, is_active, updated_at
    `;

    const result = await query(updateQuery, updateValues);

    res.json({ 
      message: 'User updated successfully',
      user: result.rows[0]
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Reset user password (admin only)
router.post('/:email/reset-password', requireAdmin, async (req, res) => {
  try {
    const { email } = req.params;

    // Check if user exists and get user info
    const existingResult = await query(
      'SELECT email, is_default_admin FROM users WHERE email = $1',
      [email.toLowerCase()]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = existingResult.rows[0];

    // Prevent automatic password reset for default admin
    if (user.is_default_admin) {
      return res.status(400).json({
        error: 'Cannot reset password for default administrator account',
        message: 'The default administrator account password cannot be automatically reset. Please use the change password feature instead.'
      });
    }

    // Generate new random password using unified function
    const { password: newPassword, passwordHash } = await createUserPasswordHash(email.toLowerCase(), 'password-reset');

    // Update password with new timestamp
    await query(
      'UPDATE users SET password_hash = $1, password_created_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE email = $2',
      [passwordHash, email.toLowerCase()]
    );

    res.json({
      message: 'Password reset successfully',
      email: email.toLowerCase(),
      newPassword // Include new password for admin to copy
    });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete user (admin only)
router.delete('/:email', requireAdmin, async (req, res) => {
  try {
    const { email } = req.params;

    // Check if user is default admin before deletion
    const userResult = await query(
      'SELECT is_default_admin FROM users WHERE email = $1',
      [email.toLowerCase()]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent deletion of default admin user
    if (userResult.rows[0].is_default_admin) {
      return res.status(400).json({
        error: 'Cannot delete default administrator account',
        message: 'The default administrator account cannot be deleted for security reasons.'
      });
    }

    const result = await query(
      'DELETE FROM users WHERE email = $1 RETURNING email',
      [email.toLowerCase()]
    );

    res.json({
      message: 'User deleted successfully',
      email: result.rows[0].email
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Send login email (admin only) - supports both clipboard and SMTP methods
router.post('/:email/send-login-email', requireAdmin, async (req, res) => {
  try {
    const { email } = req.params;

    // Check if user exists and get user info
    const existingResult = await query(
      'SELECT email, is_default_admin FROM users WHERE email = $1',
      [email.toLowerCase()]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = existingResult.rows[0];

    // Prevent email generation for default admin
    if (user.is_default_admin) {
      return res.status(400).json({
        error: 'Cannot generate email for default administrator account',
        message: 'Email functionality is disabled for the default administrator account for security reasons.'
      });
    }

    // Generate new random password using unified function
    const { password: newPassword, passwordHash } = await createUserPasswordHash(email.toLowerCase(), 'send-login-email');

    // Update password with new timestamp
    await query(
      'UPDATE users SET password_hash = $1, password_created_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE email = $2',
      [passwordHash, email.toLowerCase()]
    );

    // Get email configuration
    const emailConfig = await emailService.getEmailConfig();

    // Generate email template for clipboard fallback
    const generateEmailTemplate = async (userEmail, password) => {
      const passwordExpirationDays = await systemConfigService.getPasswordExpirationDays();
      return `To: ${userEmail}
Subject: Your Training System Login Credentials

Dear Team Member,

Your account has been created for the Internal Training System.

Please use the following credentials to log in:

Login Credentials:

Email: ${userEmail}
Password: ${password}

Important Security Information:
• Your password will expire in ${passwordExpirationDays} days
• Contact your administrator if you need assistance
• Keep your credentials secure and do not share them
• Please log in promptly to begin your training

If you have any questions or need assistance, please contact your system administrator.

Best regards,
Training System Administration

---
This is an automated message. Please do not reply to this email.`;
    };

    const emailTemplate = await generateEmailTemplate(email.toLowerCase(), newPassword);

    let emailSent = false;
    let emailError = null;

    // Try to send email using server-side email system
    if (emailConfig.email_enabled && emailConfig.email_method === 'smtp') {
      try {
        console.log('Sending login credentials via server-side email system...');

        // Get user details for template variables
        const userResult = await query('SELECT first_name, last_name, language FROM users WHERE email = $1', [email.toLowerCase()]);
        const firstName = userResult.rows[0]?.first_name || '';
        const lastName = userResult.rows[0]?.last_name || '';
        const userLanguage = userResult.rows[0]?.language || 'en';

        const htmlContent = await emailService.generateLoginEmailHTML(email.toLowerCase(), newPassword, firstName, lastName, userLanguage);

        // Get subject template based on user language and process variables
        const subjectTemplate = userLanguage === 'zh'
          ? (emailService.config?.email_template_subject_zh || '您的培训系统登录凭据')
          : (emailService.config?.email_template_subject_en || 'Your Training System Login Credentials');

        // Process subject template with variables
        const passwordExpirationDays = await systemConfigService.getPasswordExpirationDays();

        // Calculate password expiration date
        const passwordExpirationDate = new Date();
        passwordExpirationDate.setDate(passwordExpirationDate.getDate() + passwordExpirationDays);

        const variables = {
          firstName: firstName,
          lastName: lastName,
          email: email.toLowerCase(),
          password: newPassword,
          fullName: `${firstName} ${lastName}`.trim() || emailService.extractNameFromEmail(email.toLowerCase()).firstName || 'Team Member',
          date: new Date().toLocaleDateString(),
          time: new Date().toLocaleTimeString(),
          year: new Date().getFullYear(),
          passwordExpirationDays: passwordExpirationDays,
          passwordExpirationDate: passwordExpirationDate.toLocaleDateString()
        };

        const subject = emailService.processTemplate(subjectTemplate, variables);

        const emailResult = await emailService.sendEmail(
          email.toLowerCase(),
          subject,
          htmlContent
        );

        if (emailResult.success) {
          emailSent = true;
          console.log('Login credentials email sent successfully via server');
        } else {
          emailError = emailResult.message;
          console.error('Server-side email sending failed:', emailResult.message);
        }
      } catch (error) {
        console.error('Server-side email sending error:', error);
        emailError = error.message;
      }
    }

    // Response includes both email status and template for clipboard fallback
    res.json({
      message: 'Password reset successfully',
      email: email.toLowerCase(),
      newPassword,
      emailTemplate,
      emailSent,
      emailError,
      emailMethod: emailConfig.email_method,
      emailEnabled: emailConfig.email_enabled
    });

  } catch (error) {
    console.error('Send login email error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete all progress for current user (for language switching)
router.delete('/progress/all', requireAuth, async (req, res) => {
  try {
    const userEmail = req.user.email;

    // Delete all progress for the user
    const result = await query(
      'DELETE FROM user_progress WHERE user_email = $1 RETURNING module_id, language_code',
      [userEmail]
    );

    res.json({
      message: 'All progress deleted successfully',
      deletedCount: result.rows.length,
      deletedProgress: result.rows
    });

  } catch (error) {
    console.error('Delete all progress error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user language (admin only)
router.put('/:email/language', requireAuth, requireAdmin, async (req, res) => {
  try {
    const { email } = req.params;
    const { language } = req.body;

    // Validate language
    if (!language || !['en', 'zh'].includes(language)) {
      return res.status(400).json({ error: 'Invalid language. Must be "en" or "zh"' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    // Check if user exists
    const userCheck = await query('SELECT email FROM users WHERE email = $1', [email]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update user language
    await query('UPDATE users SET language = $1, updated_at = CURRENT_TIMESTAMP WHERE email = $2', [language, email]);

    console.log(`Updated language for user ${email} to ${language}`);

    res.json({
      message: 'User language updated successfully',
      email: email,
      language: language
    });

  } catch (error) {
    console.error('Update user language error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
