{"name": "internal-training-system", "version": "1.0.0", "description": "Internal Training System for Cybersecurity Videos", "main": "server/index.js", "scripts": {"dev": "npm run dev:server & npm run dev:client", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "start": "npm run start:server & npm run start:client", "start:server": "cd server && npm start", "start:client": "cd client && npm start", "build": "cd client && npm run build", "test:e2e": "cd server && npm run test:e2e", "docker:up": "docker-compose up --build", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose down && docker-compose up --build", "data:wipe": "cd server && node scripts/data-wipe.js", "migrate:default-admin": "node scripts/migrate-default-admin.js", "test": "node scripts/test-setup.js", "deploy": "node scripts/deploy.js", "deploy:local": "node scripts/deploy.js local", "deploy:docker": "node scripts/deploy.js docker", "deploy:azure": "powershell -ExecutionPolicy Bypass -File deploy-azure-optimized.ps1", "deploy:azure-containers": "powershell -ExecutionPolicy Bypass -File deploy-azure-optimized.ps1 -UseContainerInstances", "config:validate": "node -e \"require('./config/environment').printSummary()\"", "config:check": "node -e \"console.log('Environment configuration loaded successfully')\"", "env:copy": "copy .env.azure .env"}, "keywords": ["training", "cybersecurity", "video", "assessment"], "author": "Internal Training Team", "license": "MIT"}