import React, { useState } from 'react'
import { 
  Card, 
  Table, 
  Typography, 
  Select, 
  Row, 
  Col, 
  Statistic, 
  Button, 
  Modal, 
  Tag, 
  Space,
  Tooltip,
  Progress,
  Descriptions,
  Alert
} from 'antd'
import { 
  EyeOutlined, 
  UserOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  TrophyOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { useQuery } from 'react-query'
import { useTranslation } from 'react-i18next'
import { reportsAPI, trainingAPI } from '../../services/api'

const { Title, Text } = Typography
const { Option } = Select

function QuizResults() {
  const { t } = useTranslation()
  const [selectedModule, setSelectedModule] = useState(null)
  const [selectedLanguage, setSelectedLanguage] = useState(null)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [userDetailModalVisible, setUserDetailModalVisible] = useState(false)
  const [selectedQuiz, setSelectedQuiz] = useState(null)
  const [selectedUser, setSelectedUser] = useState(null)

  // Fetch modules for filtering
  const { data: modulesData } = useQuery(
    'training-modules',
    trainingAPI.getModules,
    {
      select: (response) => response.data.modules,
    }
  )

  // Fetch quiz results overview
  const { data: quizResultsResponse, isLoading: resultsLoading } = useQuery(
    ['quiz-results-overview', selectedModule, selectedLanguage],
    () => reportsAPI.getQuizResultsOverview({
      moduleId: selectedModule,
      languageCode: selectedLanguage
    }),
    {
      select: (response) => response.data,
    }
  )

  const quizResultsData = quizResultsResponse?.quizResults || []
  const summaryData = quizResultsResponse?.summary || {}
  const availableLanguages = quizResultsResponse?.availableLanguages || []

  // Fetch detailed quiz results when modal is open
  const { data: quizDetailData, isLoading: detailLoading } = useQuery(
    ['quiz-results-detail', selectedQuiz?.quiz_id],
    () => reportsAPI.getQuizResultsDetail(selectedQuiz.quiz_id),
    {
      enabled: !!selectedQuiz && detailModalVisible,
      select: (response) => response.data,
    }
  )

  // Fetch user-specific quiz results
  const { data: userDetailData, isLoading: userDetailLoading } = useQuery(
    ['quiz-results-user', selectedQuiz?.quiz_id, selectedUser?.email],
    () => reportsAPI.getQuizResultsUserDetail(selectedQuiz.quiz_id, selectedUser.email),
    {
      enabled: !!selectedQuiz && !!selectedUser && userDetailModalVisible,
      select: (response) => response.data,
    }
  )

  const handleViewDetails = (quiz) => {
    setSelectedQuiz(quiz)
    setDetailModalVisible(true)
  }

  const handleViewUserDetails = (user) => {
    setSelectedUser(user)
    setUserDetailModalVisible(true)
  }

  const handleCloseDetailModal = () => {
    setDetailModalVisible(false)
    setSelectedQuiz(null)
  }

  const handleCloseUserDetailModal = () => {
    setUserDetailModalVisible(false)
    setSelectedUser(null)
  }

  // Available languages are now provided by the API response

  // Use summary statistics from backend
  const totalQuizzes = summaryData.total_quizzes || 0
  const totalAttempts = summaryData.total_attempts || 0
  const totalUniqueUsers = summaryData.total_unique_users || 0
  const averagePassRate = summaryData.overall_pass_rate || 0

  const columns = [
    {
      title: t('quizResults.quizTitle'),
      dataIndex: 'quiz_title',
      key: 'quiz_title',
      render: (title, record) => (
        <div>
          <Text strong>{title}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.module_title} ({record.language_code?.toUpperCase()})
          </Text>
        </div>
      )
    },
    {
      title: t('quizResults.passingScore'),
      dataIndex: 'passing_score',
      key: 'passing_score',
      render: (score) => `${score}%`,
      width: 120,
      align: 'center'
    },
    {
      title: t('quizResults.usersAttempted'),
      dataIndex: 'total_users_attempted',
      key: 'total_users_attempted',
      width: 120,
      align: 'center'
    },
    {
      title: t('quizResults.usersPassed'),
      key: 'users_passed',
      render: (_, record) => {
        const passRate = record.total_users_attempted > 0 ? 
          Math.round((record.users_passed / record.total_users_attempted) * 100) : 0
        return (
          <div style={{ textAlign: 'center' }}>
            <div>{record.users_passed}</div>
            <Progress 
              percent={passRate} 
              size="small" 
              showInfo={false}
              strokeColor={passRate >= 70 ? '#52c41a' : passRate >= 50 ? '#faad14' : '#ff4d4f'}
            />
            <Text type="secondary" style={{ fontSize: '11px' }}>{passRate}%</Text>
          </div>
        )
      },
      width: 120
    },
    {
      title: t('quizResults.totalAttempts'),
      dataIndex: 'total_attempts',
      key: 'total_attempts',
      width: 120,
      align: 'center'
    },
    {
      title: t('quizResults.averageScore'),
      dataIndex: 'average_score',
      key: 'average_score',
      render: (score) => score ? `${score}%` : '-',
      width: 120,
      align: 'center'
    },
    {
      title: t('quizResults.lastAttempt'),
      dataIndex: 'last_attempt_date',
      key: 'last_attempt_date',
      render: (date) => date ? new Date(date).toLocaleDateString() : '-',
      width: 120
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_, record) => (
        <Button
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetails(record)}
        >
          {t('quizResults.viewDetails')}
        </Button>
      ),
      width: 120
    }
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>{t('quizResults.title')}</Title>
      </div>

      {/* Summary Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('quizResults.totalQuizzes')}
              value={totalQuizzes}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('quizResults.totalAttempts')}
              value={totalAttempts}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('quizResults.uniqueUsers')}
              value={totalUniqueUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('quizResults.averagePassRate')}
              value={averagePassRate}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: averagePassRate >= 70 ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Text strong>{t('quizResults.filterByModule')}</Text>
            <Select
              style={{ width: '100%', marginTop: 8 }}
              placeholder={t('quizResults.selectModule')}
              allowClear
              value={selectedModule}
              onChange={setSelectedModule}
            >
              {modulesData?.map(module => (
                <Option key={module.id} value={module.id}>
                  {module.title}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Text strong>{t('quizResults.filterByLanguage')}</Text>
            <Select
              style={{ width: '100%', marginTop: 8 }}
              placeholder={t('quizResults.selectLanguage')}
              allowClear
              value={selectedLanguage}
              onChange={setSelectedLanguage}
            >
              {availableLanguages.map(lang => (
                <Option key={lang} value={lang}>
                  {lang?.toUpperCase()}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Card>

      {/* Quiz Results Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={quizResultsData || []}
          rowKey="quiz_id"
          loading={resultsLoading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => t('quizResults.totalQuizzes', { total })
          }}
        />
      </Card>

      {/* Quiz Detail Modal */}
      <Modal
        title={selectedQuiz ? `${t('quizResults.quizDetails')}: ${selectedQuiz.quiz_title}` : ''}
        open={detailModalVisible}
        onCancel={handleCloseDetailModal}
        width={1000}
        footer={null}
      >
        {detailLoading ? (
          <div style={{ textAlign: 'center', padding: 40 }}>
            {t('common.loading')}...
          </div>
        ) : quizDetailData ? (
          <div>
            <Descriptions bordered column={2} style={{ marginBottom: 24 }}>
              <Descriptions.Item label={t('quizResults.module')}>
                {quizDetailData.quiz.module_title}
              </Descriptions.Item>
              <Descriptions.Item label={t('quizResults.language')}>
                {quizDetailData.quiz.language_code?.toUpperCase()}
              </Descriptions.Item>
              <Descriptions.Item label={t('quizResults.passingScore')}>
                {quizDetailData.quiz.passing_score}%
              </Descriptions.Item>
              <Descriptions.Item label={t('quizResults.timeLimit')}>
                {quizDetailData.quiz.time_limit_minutes ?
                  `${quizDetailData.quiz.time_limit_minutes} ${t('common.minutes')}` :
                  t('quizResults.noTimeLimit')
                }
              </Descriptions.Item>
            </Descriptions>

            <Title level={4}>{t('quizResults.userAttempts')}</Title>
            <Table
              dataSource={quizDetailData.userAttempts || []}
              rowKey="email"
              pagination={{ pageSize: 10 }}
              columns={[
                {
                  title: t('quizResults.user'),
                  key: 'user',
                  render: (_, record) => {
                    const displayName = record.first_name && record.last_name
                      ? `${record.first_name} ${record.last_name}`.trim()
                      : record.email.split('@')[0].replace(/[._]/g, ' ');

                    return (
                      <div>
                        <Text strong>{displayName}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {record.email}
                        </Text>
                      </div>
                    )
                  }
                },
                {
                  title: t('quizResults.attempts'),
                  dataIndex: 'total_attempts',
                  key: 'total_attempts',
                  align: 'center'
                },
                {
                  title: t('quizResults.bestScore'),
                  dataIndex: 'best_score',
                  key: 'best_score',
                  render: (score) => `${score}%`,
                  align: 'center'
                },
                {
                  title: t('quizResults.averageScore'),
                  dataIndex: 'average_score',
                  key: 'average_score',
                  render: (score) => `${score}%`,
                  align: 'center'
                },
                {
                  title: t('quizResults.status'),
                  key: 'status',
                  render: (_, record) => (
                    <Tag color={record.ever_passed ? 'green' : 'red'}>
                      {record.ever_passed ? t('quizResults.passed') : t('quizResults.failed')}
                    </Tag>
                  ),
                  align: 'center'
                },
                {
                  title: t('quizResults.lastAttempt'),
                  dataIndex: 'last_attempt_date',
                  key: 'last_attempt_date',
                  render: (date) => new Date(date).toLocaleDateString()
                },
                {
                  title: t('common.actions'),
                  key: 'actions',
                  render: (_, record) => (
                    <Button
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => handleViewUserDetails(record)}
                    >
                      {t('quizResults.viewAttempts')}
                    </Button>
                  )
                }
              ]}
            />
          </div>
        ) : null}
      </Modal>

      {/* User Detail Modal */}
      <Modal
        title={selectedUser ?
          `${t('quizResults.userAttemptDetails')}: ${
            selectedUser.first_name && selectedUser.last_name
              ? `${selectedUser.first_name} ${selectedUser.last_name}`.trim()
              : selectedUser.email.split('@')[0].replace(/[._]/g, ' ')
          }` :
          ''
        }
        open={userDetailModalVisible}
        onCancel={handleCloseUserDetailModal}
        width={1200}
        footer={null}
      >
        {userDetailLoading ? (
          <div style={{ textAlign: 'center', padding: 40 }}>
            {t('common.loading')}...
          </div>
        ) : userDetailData ? (
          <div>
            <Alert
              message={`${t('quizResults.quiz')}: ${userDetailData.quiz.quiz_title}`}
              description={`${t('quizResults.user')}: ${
                userDetailData.quiz.first_name && userDetailData.quiz.last_name
                  ? `${userDetailData.quiz.first_name} ${userDetailData.quiz.last_name}`.trim()
                  : userDetailData.quiz.email.split('@')[0].replace(/[._]/g, ' ')
              } (${userDetailData.quiz.email})`}
              type="info"
              style={{ marginBottom: 24 }}
            />

            {userDetailData.attempts?.map((attempt, index) => (
              <Card
                key={attempt.id}
                title={
                  <Space>
                    <Text strong>{t('quizResults.attempt')} {attempt.attempt_number}</Text>
                    <Tag color={attempt.passed ? 'green' : 'red'}>
                      {attempt.score}% {attempt.passed ? t('quizResults.passed') : t('quizResults.failed')}
                    </Tag>
                    <Text type="secondary">
                      {new Date(attempt.completed_at).toLocaleString()}
                    </Text>
                  </Space>
                }
                style={{ marginBottom: 16 }}
              >
                <Table
                  dataSource={attempt.answers || []}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  columns={[
                    {
                      title: t('quizResults.question'),
                      dataIndex: 'question_text',
                      key: 'question_text',
                      width: '40%'
                    },
                    {
                      title: t('quizResults.userAnswer'),
                      dataIndex: 'user_answer',
                      key: 'user_answer',
                      width: '25%',
                      render: (answer) => answer || t('quizResults.noAnswer')
                    },
                    {
                      title: t('quizResults.correctAnswer'),
                      dataIndex: 'correct_answer',
                      key: 'correct_answer',
                      width: '25%'
                    },
                    {
                      title: t('quizResults.result'),
                      key: 'result',
                      render: (_, record) => (
                        <Space>
                          {record.is_correct ? (
                            <CheckCircleOutlined style={{ color: '#52c41a' }} />
                          ) : (
                            <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                          )}
                          <Text>{record.points_earned}/{record.max_points}</Text>
                        </Space>
                      ),
                      width: '10%',
                      align: 'center'
                    }
                  ]}
                />
              </Card>
            ))}
          </div>
        ) : null}
      </Modal>
    </div>
  )
}

export default QuizResults
